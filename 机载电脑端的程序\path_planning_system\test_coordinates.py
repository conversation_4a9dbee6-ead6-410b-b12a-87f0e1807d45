#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标转换测试
"""

from core.grid_map import GridMap

def test_coordinate_conversion():
    """测试坐标转换"""
    grid_map = GridMap()
    
    print("🔍 坐标转换测试")
    print("=" * 50)
    
    # 测试关键点的坐标转换
    test_points = [
        (91, "A9B1", "右下角起点"),
        (11, "A1B1", "左下角"),
        (97, "A9B7", "右上角"),
        (17, "A1B7", "左上角")
    ]
    
    for position_code, name, description in test_points:
        row, col = grid_map.position_code_to_grid(position_code)
        back_code = grid_map.grid_to_position_code(row, col)
        
        print(f"{name} ({description}):")
        print(f"  position_code: {position_code}")
        print(f"  网格坐标: ({row}, {col})")
        print(f"  反向转换: {back_code}")
        print(f"  转换正确: {'✅' if back_code == position_code else '❌'}")
        print()
    
    # 测试起点坐标
    print("🎯 起点坐标验证")
    print("=" * 50)
    
    start_position_code = 91  # A9B1
    start_row, start_col = grid_map.position_code_to_grid(start_position_code)
    
    print(f"起点 A9B1:")
    print(f"  position_code: {start_position_code}")
    print(f"  网格坐标: ({start_row}, {start_col})")
    print(f"  预期坐标: (0, 8)  # B1=row0, A9=col8")
    print(f"  坐标正确: {'✅' if (start_row, start_col) == (0, 8) else '❌'}")

if __name__ == "__main__":
    test_coordinate_conversion()
