#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
返航路径数据补充工具
版权信息：米醋电子工作室
创建日期：2025-07-31

功能描述：
为现有的预计算路径数据添加模拟的返航路径信息，用于测试C代码生成器
"""

import json
import random

def position_code_to_grid(position_code: int) -> tuple:
    """将position_code转换为网格坐标"""
    row_num = position_code // 10  # A部分：1-9
    col_num = position_code % 10   # B部分：1-7
    
    # 坐标系统：A1B7(左上) A1B1(左下) A9B7(右上) A9B1(右下起点)
    grid_row = col_num - 1  # B1->0, B7->6 (X轴：下到上)
    grid_col = 9 - row_num  # A9->0, A1->8 (Y轴：右到左)
    
    return (grid_row, grid_col)

def grid_to_position_code(grid_row: int, grid_col: int) -> int:
    """将网格坐标转换为position_code"""
    row_num = 9 - grid_col  # A部分：1-9
    col_num = grid_row + 1  # B部分：1-7
    return row_num * 10 + col_num

def calculate_distance(pos1: int, pos2: int) -> float:
    """计算两个位置之间的距离"""
    grid1 = position_code_to_grid(pos1)
    grid2 = position_code_to_grid(pos2)
    
    dx = abs(grid1[0] - grid2[0])
    dy = abs(grid1[1] - grid2[1])
    
    return (dx * dx + dy * dy) ** 0.5

def generate_simple_return_path(start_pos: int, end_pos: int, no_fly_zones: list) -> list:
    """生成简单的返航路径（避开禁飞区）"""
    if start_pos == end_pos:
        return [start_pos]
    
    start_grid = position_code_to_grid(start_pos)
    end_grid = position_code_to_grid(end_pos)
    
    path = [start_pos]
    current_grid = start_grid
    
    # 简单的贪心算法：每次向目标方向移动一步
    max_steps = 20  # 防止无限循环
    step_count = 0
    
    while current_grid != end_grid and step_count < max_steps:
        step_count += 1
        
        # 计算到目标的方向
        dr = 1 if end_grid[0] > current_grid[0] else (-1 if end_grid[0] < current_grid[0] else 0)
        dc = 1 if end_grid[1] > current_grid[1] else (-1 if end_grid[1] < current_grid[1] else 0)
        
        # 尝试移动
        next_grid = (current_grid[0] + dr, current_grid[1] + dc)
        
        # 检查边界
        if (next_grid[0] < 0 or next_grid[0] >= 7 or 
            next_grid[1] < 0 or next_grid[1] >= 9):
            # 边界外，尝试其他方向
            if dr != 0:
                next_grid = (current_grid[0], current_grid[1] + dc)
            elif dc != 0:
                next_grid = (current_grid[0] + dr, current_grid[1])
            else:
                break
        
        # 转换为position_code并检查禁飞区
        next_pos = grid_to_position_code(next_grid[0], next_grid[1])
        
        if next_pos not in no_fly_zones:
            path.append(next_pos)
            current_grid = next_grid
        else:
            # 遇到禁飞区，尝试绕行
            # 简单处理：尝试垂直或水平移动
            alternatives = []
            
            for dr_alt, dc_alt in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
                alt_grid = (current_grid[0] + dr_alt, current_grid[1] + dc_alt)
                if (0 <= alt_grid[0] < 7 and 0 <= alt_grid[1] < 9):
                    alt_pos = grid_to_position_code(alt_grid[0], alt_grid[1])
                    if alt_pos not in no_fly_zones:
                        alternatives.append((alt_grid, alt_pos))
            
            if alternatives:
                # 选择距离目标最近的替代路径
                best_alt = min(alternatives, 
                              key=lambda x: abs(x[0][0] - end_grid[0]) + abs(x[0][1] - end_grid[1]))
                path.append(best_alt[1])
                current_grid = best_alt[0]
            else:
                # 无法绕行，停止
                break
    
    # 确保路径以目标位置结束
    if path[-1] != end_pos:
        path.append(end_pos)
    
    return path

def add_return_paths_to_data(input_file: str, output_file: str):
    """为路径数据添加返航路径"""
    print(f"📁 读取原始数据: {input_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    path_data = data['path_data']
    print(f"   原始路径数量: {len(path_data)}")
    
    # 为每个路径添加返航路径
    for i, path_info in enumerate(path_data):
        no_fly_zones = path_info['no_fly_zones']
        path_sequence = path_info['path_sequence']
        
        if path_sequence:
            # 获取巡查路径的最后一个点
            last_position = path_sequence[-1]
            
            # 生成返航路径（从最后点到A9B1）
            return_path = generate_simple_return_path(last_position, 91, no_fly_zones)
            
            # 添加返航路径信息
            path_info['return_path'] = return_path
            path_info['return_length'] = len(return_path)
            
            if i < 5:  # 显示前5个路径的信息
                print(f"   路径{i+1}: 禁飞区{no_fly_zones}")
                print(f"      巡查终点: {last_position}")
                print(f"      返航路径: {return_path[:5]}{'...' if len(return_path) > 5 else ''}")
                print(f"      返航长度: {len(return_path)}")
        else:
            # 空路径，添加空返航路径
            path_info['return_path'] = []
            path_info['return_length'] = 0
    
    # 更新元数据
    data['metadata']['generation_time'] = "2025-07-31 15:00:00"
    data['metadata']['return_path_added'] = True
    
    # 保存扩展后的数据
    print(f"💾 保存扩展数据: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 返航路径添加完成!")
    
    # 统计信息
    return_lengths = [path_info['return_length'] for path_info in path_data]
    avg_return_length = sum(return_lengths) / len(return_lengths)
    max_return_length = max(return_lengths)
    min_return_length = min(return_lengths)
    
    print(f"📊 返航路径统计:")
    print(f"   平均长度: {avg_return_length:.1f}")
    print(f"   最大长度: {max_return_length}")
    print(f"   最小长度: {min_return_length}")
    print(f"   超过25点的路径: {sum(1 for x in return_lengths if x > 25)}")

def main():
    """主函数"""
    print("🚀 返航路径数据补充工具")
    print("版权：米醋电子工作室")
    print("=" * 50)
    
    input_file = "precomputed_paths_data.json"
    output_file = "precomputed_paths_data_with_return.json"
    
    try:
        add_return_paths_to_data(input_file, output_file)
        
        print("\n" + "=" * 50)
        print("🎉 数据处理完成!")
        print(f"📁 输出文件: {output_file}")
        
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
