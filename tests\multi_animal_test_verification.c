/*
 * 多动物检测功能测试验证脚本
 * 用于验证修改后的动物检测逻辑的正确性
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>

// 模拟嵌入式环境的数据类型
typedef unsigned char u8;
typedef signed short s16;

// 模拟动物检测相关的数据结构和常量
#define ANIMAL_DETECTION_THRESHOLD 4
#define MAX_SENT_PAIRS 315

// 位置-动物对结构体
typedef struct {
    u8 position_code;
    u8 animal_id;
} position_animal_pair_t;

// 全局变量模拟
static u8 animal_detection_counters[6] = {0};
static position_animal_pair_t sent_pairs[MAX_SENT_PAIRS];
static int sent_pairs_count = 0;

// 测试结果统计
static int test_passed = 0;
static int test_failed = 0;
static int test_total = 0;

// 模拟调试输出函数
void mock_debug_output(const char* color, const char* message) {
    printf("[%s] %s\n", color, message);
}

// 模拟zigbee发送函数
void mock_zigbee_send_screen_animal(u8 position_code, u8 animal_id, u8 count) {
    printf("ZIGBEE_SEND: Position=%d, Animal_ID=%d, Count=%d\n", 
           position_code, animal_id, count);
}

// 实现核心函数（从修改后的代码复制）
bool is_position_animal_sent(u8 position_code, u8 animal_id)
{
    if (position_code == 0 || position_code > 63 || animal_id < 1 || animal_id > 5) {
        return false;
    }
    
    for (int i = 0; i < sent_pairs_count; i++) {
        if (sent_pairs[i].position_code == position_code && 
            sent_pairs[i].animal_id == animal_id) {
            return true;
        }
    }
    return false;
}

void mark_position_animal_sent(u8 position_code, u8 animal_id)
{
    if (position_code == 0 || position_code > 63 || animal_id < 1 || animal_id > 5) {
        return;
    }
    
    if (!is_position_animal_sent(position_code, animal_id) && 
        sent_pairs_count < MAX_SENT_PAIRS) {
        
        sent_pairs[sent_pairs_count].position_code = position_code;
        sent_pairs[sent_pairs_count].animal_id = animal_id;
        sent_pairs_count++;
        
        printf("MARK_SENT: Position=%d, Animal_ID=%d, Total=%d\n", 
               position_code, animal_id, sent_pairs_count);
    }
}

void reset_specific_animal_counter(u8 animal_id)
{
    if (animal_id >= 1 && animal_id <= 5) {
        animal_detection_counters[animal_id] = 0;
        printf("RESET_COUNTER: Animal_ID=%d\n", animal_id);
    }
}

void process_animal_detection(u8 animal_id, u8 count, u8 position_code)
{
    if (animal_id < 1 || animal_id > 5) {
        printf("ERROR: Invalid animal_id: %d\n", animal_id);
        return;
    }
    
    if (position_code == 0 || position_code > 63) {
        printf("ERROR: Invalid position_code: %d\n", position_code);
        return;
    }

    if (!is_position_animal_sent(position_code, animal_id)) {
        animal_detection_counters[animal_id]++;

        printf("DETECT: Animal_ID=%d, Count=%d, Counter=%d/%d, Position=%d\n",
               animal_id, count, animal_detection_counters[animal_id], 
               ANIMAL_DETECTION_THRESHOLD, position_code);

        if (animal_detection_counters[animal_id] >= ANIMAL_DETECTION_THRESHOLD) {
            mock_zigbee_send_screen_animal(position_code, animal_id, count);
            mark_position_animal_sent(position_code, animal_id);
            reset_specific_animal_counter(animal_id);
        }
    } else {
        printf("SKIP: Animal_ID=%d at Position=%d already sent\n",
               animal_id, position_code);
    }
}

// 测试辅助函数
void reset_test_environment() {
    // 重置计数器
    for (int i = 0; i < 6; i++) {
        animal_detection_counters[i] = 0;
    }
    
    // 重置发送记录
    sent_pairs_count = 0;
    for (int i = 0; i < MAX_SENT_PAIRS; i++) {
        sent_pairs[i].position_code = 0;
        sent_pairs[i].animal_id = 0;
    }
}

void assert_test(bool condition, const char* test_name) {
    test_total++;
    if (condition) {
        printf("✅ PASS: %s\n", test_name);
        test_passed++;
    } else {
        printf("❌ FAIL: %s\n", test_name);
        test_failed++;
    }
}

// 测试用例实现
void test_single_animal_multiple_count() {
    printf("\n=== 测试用例1：单种多个动物检测 ===\n");
    reset_test_environment();
    
    // 模拟检测到3只象，重复发送4次达到阈值
    for (int i = 0; i < 4; i++) {
        process_animal_detection(1, 3, 25);  // 象，3只，位置25
    }
    
    // 验证结果
    assert_test(animal_detection_counters[1] == 0, "象的计数器已重置");
    assert_test(is_position_animal_sent(25, 1), "位置25的象已标记为发送");
    assert_test(sent_pairs_count == 1, "发送记录数量正确");
}

void test_multiple_different_animals() {
    printf("\n=== 测试用例2：多种不同动物检测 ===\n");
    reset_test_environment();
    
    // 模拟同一位置有象和虎
    // 先发送象的数据4次
    for (int i = 0; i < 4; i++) {
        process_animal_detection(1, 1, 30);  // 象，1只，位置30
    }
    
    // 再发送虎的数据4次
    for (int i = 0; i < 4; i++) {
        process_animal_detection(2, 2, 30);  // 虎，2只，位置30
    }
    
    // 验证结果
    assert_test(is_position_animal_sent(30, 1), "位置30的象已发送");
    assert_test(is_position_animal_sent(30, 2), "位置30的虎已发送");
    assert_test(sent_pairs_count == 2, "两种动物都已发送");
    assert_test(animal_detection_counters[1] == 0, "象的计数器已重置");
    assert_test(animal_detection_counters[2] == 0, "虎的计数器已重置");
}

void test_animal_type_switching() {
    printf("\n=== 测试用例3：动物类型快速切换 ===\n");
    reset_test_environment();
    
    // 模拟动物类型交替出现
    process_animal_detection(1, 1, 35);  // 象
    process_animal_detection(2, 2, 35);  // 虎
    process_animal_detection(1, 1, 35);  // 象
    process_animal_detection(2, 2, 35);  // 虎
    process_animal_detection(1, 1, 35);  // 象
    process_animal_detection(2, 2, 35);  // 虎
    process_animal_detection(1, 1, 35);  // 象 (达到阈值)
    process_animal_detection(2, 2, 35);  // 虎 (达到阈值)
    
    // 验证结果
    assert_test(is_position_animal_sent(35, 1), "象已发送");
    assert_test(is_position_animal_sent(35, 2), "虎已发送");
    assert_test(sent_pairs_count == 2, "两种动物都已发送");
}

void test_parameter_validation() {
    printf("\n=== 测试用例4：参数有效性验证 ===\n");
    reset_test_environment();
    
    // 测试无效的animal_id
    process_animal_detection(0, 1, 40);   // 无效animal_id
    process_animal_detection(6, 1, 40);   // 超出范围animal_id
    
    // 测试无效的position_code
    process_animal_detection(1, 1, 0);    // 无效position_code
    process_animal_detection(1, 1, 64);   // 超出范围position_code
    
    // 验证结果
    assert_test(sent_pairs_count == 0, "无效参数不产生发送记录");
    assert_test(animal_detection_counters[1] == 0, "无效参数不影响计数器");
}

void test_duplicate_send_protection() {
    printf("\n=== 测试用例5：重复发送防护 ===\n");
    reset_test_environment();
    
    // 第一次达到阈值
    for (int i = 0; i < 4; i++) {
        process_animal_detection(1, 1, 45);
    }
    
    // 再次尝试发送同一动物
    for (int i = 0; i < 4; i++) {
        process_animal_detection(1, 1, 45);
    }
    
    // 验证结果
    assert_test(sent_pairs_count == 1, "只发送一次，防止重复");
    assert_test(is_position_animal_sent(45, 1), "位置45的象已标记");
}

int main() {
    printf("多动物检测功能集成测试开始\n");
    printf("=====================================\n");
    
    // 执行所有测试用例
    test_single_animal_multiple_count();
    test_multiple_different_animals();
    test_animal_type_switching();
    test_parameter_validation();
    test_duplicate_send_protection();
    
    // 输出测试结果统计
    printf("\n=====================================\n");
    printf("测试结果统计:\n");
    printf("总测试数: %d\n", test_total);
    printf("通过数: %d\n", test_passed);
    printf("失败数: %d\n", test_failed);
    printf("通过率: %.1f%%\n", (float)test_passed / test_total * 100);
    
    if (test_failed == 0) {
        printf("🎉 所有测试通过！多动物检测功能验证成功！\n");
        return 0;
    } else {
        printf("⚠️  有测试失败，需要检查代码实现！\n");
        return 1;
    }
}
