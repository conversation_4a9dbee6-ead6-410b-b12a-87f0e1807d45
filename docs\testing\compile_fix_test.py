#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编译修复验证测试
版权信息：米醋电子工作室
创建日期：2025-07-31

功能描述：
验证编译错误修复后的编译状态
"""

import subprocess
import os
import time

def test_compilation():
    """测试编译"""
    print("🔨 编译修复验证测试")
    print("=" * 50)
    
    # Keil编译命令
    keil_path = "D:\\keil5\\UV4\\UV4.exe"
    project_path = "..\\..\\ProjectSTM32F429\\ANO_LX_STM32F429.uvprojx"
    log_path = "compile_fix_test.log"
    
    print(f"📁 项目路径: {project_path}")
    print(f"📄 日志路径: {log_path}")
    
    try:
        # 执行编译
        print("🚀 开始编译...")
        start_time = time.time()
        
        cmd = f'& "{keil_path}" -b "{project_path}" -o "{log_path}"'
        result = subprocess.run(["powershell", "-Command", cmd], 
                              capture_output=True, text=True, timeout=120)
        
        end_time = time.time()
        compile_time = end_time - start_time
        
        print(f"⏱️ 编译时间: {compile_time:.1f} 秒")
        print(f"🔄 返回码: {result.returncode}")
        
        # 检查编译日志
        if os.path.exists(log_path):
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                log_content = f.read()
            
            print("\n📋 编译日志分析:")
            print("-" * 30)
            
            # 统计错误和警告
            error_count = log_content.count("error:")
            warning_count = log_content.count("warning:")
            
            print(f"❌ 错误数量: {error_count}")
            print(f"⚠️ 警告数量: {warning_count}")
            
            # 检查编译结果
            if "Error(s)" in log_content:
                import re
                error_match = re.search(r'(\d+)\s+Error\(s\)', log_content)
                if error_match:
                    errors = int(error_match.group(1))
                    print(f"📊 总错误数: {errors}")
                    
                    if errors == 0:
                        print("✅ 编译成功!")
                        return True
                    else:
                        print("❌ 编译失败!")
                        
                        # 显示错误详情
                        print("\n🔍 错误详情:")
                        lines = log_content.split('\n')
                        for line in lines:
                            if "error:" in line.lower():
                                print(f"   {line.strip()}")
                        return False
            else:
                print("⚠️ 无法解析编译结果")
                return False
        else:
            print(f"❌ 编译日志文件不存在: {log_path}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 编译超时 (120秒)")
        return False
    except Exception as e:
        print(f"❌ 编译过程发生错误: {e}")
        return False

def check_binary_files():
    """检查生成的二进制文件"""
    print("\n📦 二进制文件检查")
    print("=" * 50)
    
    binary_files = [
        "../../ProjectSTM32F429/ANO-LX.bin",
        "../../ProjectSTM32F429/build/ANO_LX.hex"
    ]
    
    all_exist = True
    
    for file_path in binary_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {os.path.basename(file_path)}: {file_size:,} 字节")
        else:
            print(f"❌ {os.path.basename(file_path)}: 文件不存在")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("🚀 编译修复验证测试")
    print("版权：米醋电子工作室")
    print("=" * 70)
    
    # 测试编译
    compile_success = test_compilation()
    
    # 检查二进制文件
    binary_success = check_binary_files()
    
    print("\n" + "=" * 70)
    
    if compile_success and binary_success:
        print("🎉 编译修复验证成功!")
        print("✅ 所有编译错误已修复")
        print("✅ 二进制文件生成正常")
        return 0
    else:
        print("❌ 编译修复验证失败")
        if not compile_success:
            print("❌ 编译仍有错误")
        if not binary_success:
            print("❌ 二进制文件生成异常")
        return 1

if __name__ == "__main__":
    exit(main())
