#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证第50个路径的安全修正
"""

import json

def verify_path_50_safety():
    """验证第50个路径的安全性"""
    print("🔍 验证第50个路径的安全修正")
    print("=" * 50)
    
    # 加载修正后的数据
    with open('safe_optimized_return_paths.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    path_data = data['path_data']
    path_50 = path_data[49]  # 第50个路径（索引49）
    
    print(f"第50个组合:")
    print(f"禁飞区: {path_50['no_fly_zones']}")
    print(f"巡查路径长度: {path_50['patrol_path_length']}")
    print(f"巡查路径: {path_50['patrol_path_sequence']}")
    
    # 检查是否还有11→22的直接移动
    patrol_path = path_50['patrol_path_sequence']
    
    unsafe_moves = []
    for i in range(len(patrol_path) - 1):
        current = patrol_path[i]
        next_pos = patrol_path[i + 1]
        
        if current == 11 and next_pos == 22:
            unsafe_moves.append((i, current, next_pos))
    
    if unsafe_moves:
        print(f"\n❌ 仍然发现不安全移动: {unsafe_moves}")
    else:
        print(f"\n✅ 未发现11→22的直接移动")
    
    # 检查11和22之间的路径
    pos_11_index = -1
    pos_22_index = -1
    
    for i, pos in enumerate(patrol_path):
        if pos == 11:
            pos_11_index = i
        elif pos == 22:
            pos_22_index = i
    
    if pos_11_index != -1 and pos_22_index != -1:
        print(f"\n📍 位置分析:")
        print(f"   11的位置: 第{pos_11_index + 1}步")
        print(f"   22的位置: 第{pos_22_index + 1}步")
        
        if abs(pos_22_index - pos_11_index) == 1:
            print(f"   ❌ 11和22仍然相邻")
        else:
            print(f"   ✅ 11和22之间有{abs(pos_22_index - pos_11_index) - 1}个中间步骤")
            
            # 显示中间路径
            start_idx = min(pos_11_index, pos_22_index)
            end_idx = max(pos_11_index, pos_22_index)
            intermediate_path = patrol_path[start_idx:end_idx + 1]
            print(f"   中间路径: {intermediate_path}")
    
    # 检查所有对角线移动的安全性
    print(f"\n🔍 检查所有对角线移动的安全性:")
    
    def position_code_to_grid(position_code):
        col_num = position_code // 10
        row_num = position_code % 10
        return (row_num - 1, col_num - 1)
    
    def grid_to_position_code(row, col):
        return (col + 1) * 10 + (row + 1)
    
    def is_diagonal_move_safe(from_pos, to_pos, no_fly_zones):
        from_grid = position_code_to_grid(from_pos)
        to_grid = position_code_to_grid(to_pos)
        
        row_diff = abs(to_grid[0] - from_grid[0])
        col_diff = abs(to_grid[1] - from_grid[1])
        
        if row_diff != 1 or col_diff != 1:
            return True  # 不是对角线移动
        
        # 检查中间位置
        intermediate1 = (from_grid[0], to_grid[1])
        intermediate2 = (to_grid[0], from_grid[1])
        
        pos1_code = grid_to_position_code(intermediate1[0], intermediate1[1])
        pos2_code = grid_to_position_code(intermediate2[0], intermediate2[1])
        
        return pos1_code not in no_fly_zones and pos2_code not in no_fly_zones
    
    no_fly_set = set(path_50['no_fly_zones'])
    diagonal_moves = []
    unsafe_diagonal_moves = []
    
    for i in range(len(patrol_path) - 1):
        current = patrol_path[i]
        next_pos = patrol_path[i + 1]
        
        from_grid = position_code_to_grid(current)
        to_grid = position_code_to_grid(next_pos)
        
        row_diff = abs(to_grid[0] - from_grid[0])
        col_diff = abs(to_grid[1] - from_grid[1])
        
        if row_diff == 1 and col_diff == 1:  # 对角线移动
            diagonal_moves.append((current, next_pos))
            
            if not is_diagonal_move_safe(current, next_pos, no_fly_set):
                unsafe_diagonal_moves.append((current, next_pos))
    
    print(f"   总对角线移动: {len(diagonal_moves)}")
    print(f"   不安全对角线移动: {len(unsafe_diagonal_moves)}")
    
    if unsafe_diagonal_moves:
        print(f"   ❌ 不安全移动: {unsafe_diagonal_moves}")
    else:
        print(f"   ✅ 所有对角线移动都是安全的")
    
    return len(unsafe_diagonal_moves) == 0

def main():
    """主函数"""
    print("🚀 第50个路径安全验证")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Alex (工程师)")
    print("=" * 70)
    
    is_safe = verify_path_50_safety()
    
    print(f"\n🎉 验证完成!")
    if is_safe:
        print("✅ 第50个路径已修正为安全")
        print("✅ 所有对角线移动都符合飞机尺寸安全要求")
    else:
        print("❌ 第50个路径仍存在安全问题")
        print("需要进一步修正")

if __name__ == "__main__":
    main()
