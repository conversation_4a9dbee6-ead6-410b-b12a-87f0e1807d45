/*==========================================================================
 * 版权    ：米醋电子工作室
 * 创建时间：2025-07-31
 * 作者    ：Alex (Engineer)
 * 功能描述：path_storage查找函数编译测试
 * 编码格式：UTF-8
 * 
 * 说明：
 * 测试扩展后的path_storage查找函数的编译兼容性和基本功能
===========================================================================*/

#include <stdio.h>
#include <stdint.h>

// 模拟STM32类型定义
typedef uint8_t u8;
typedef uint16_t u16;

// 模拟调试输出函数
void AnoPTv8SendStr(int level, int device, int color, const char* str) {
    printf("[DEBUG] %s\n", str);
}

void AnoPTv8SendValStr(int level, int device, float value, const char* str) {
    printf("[DEBUG] %s %.1f\n", str, value);
}

// 模拟常量定义
#define LT_D_IMU 0
#define ANOPTV8DEVID_SWJ 0
#define ANOLOGCOLOR_RED 0
#define ANOLOGCOLOR_GREEN 1
#define ANOLOGCOLOR_YELLOW 2

// 包含path_storage的常量和结构体定义
#define MAX_NO_FLY_ZONES        3       // 最大禁飞区数量
#define MAX_PATH_LENGTH         60      // 最大巡查路径长度
#define MAX_RETURN_LENGTH       25      // 最大返航路径长度
#define PRECOMPUTED_PATH_COUNT  3       // 测试用的路径数量

/**
 * @brief 预计算路径数据结构（扩展版本，包含返航路径）
 */
typedef struct {
    u8 no_fly_zones[MAX_NO_FLY_ZONES];     // 禁飞区position_code数组
    u8 path_length;                        // 巡查路径长度（通常为60）
    u8 path_sequence[MAX_PATH_LENGTH];     // 巡查路径序列（position_code数组）
    u8 return_length;                      // 返航路径长度（新增）
    u8 return_sequence[MAX_RETURN_LENGTH]; // 返航路径序列（新增）
} precomputed_path_t;

// 测试数据
static const precomputed_path_t test_path_lookup_table[PRECOMPUTED_PATH_COUNT] = {
    // 路径1: 禁飞区[11, 21, 31], 巡查长度10, 返航长度6
    {
        {11, 21, 31},  // 禁飞区
        10,  // 巡查路径长度
        {  // 巡查路径序列
             91,  81,  71,  61,  51,  41,  42,  32,  22,  12,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0
        },
        6,  // 返航路径长度
        {  // 返航路径序列
             12,  22,  32,  42,  52,  62,  72,  82,  92,  91,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0
        }
    },
    
    // 路径2: 禁飞区[33, 34, 35], 巡查长度8, 返航长度5
    {
        {33, 34, 35},  // 禁飞区
        8,  // 巡查路径长度
        {  // 巡查路径序列
             91,  81,  71,  61,  51,  41,  31,  21,
              0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0
        },
        5,  // 返航路径长度
        {  // 返航路径序列
             21,  31,  41,  51,  61,  71,  81,  91,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0
        }
    },
    
    // 路径3: 禁飞区[55, 56, 57], 巡查长度12, 返航长度7
    {
        {55, 56, 57},  // 禁飞区
        12,  // 巡查路径长度
        {  // 巡查路径序列
             91,  81,  71,  61,  51,  41,  31,  21,  11,  12,  13,  23,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0
        },
        7,  // 返航路径长度
        {  // 返航路径序列
             23,  33,  43,  53,  63,  73,  83,  93,  92,  91,   0,   0,
              0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
              0
        }
    }
};

/**
 * @brief 查找预计算返航路径
 */
int find_precomputed_return_path(const u8* no_fly_zones, u8* output_path)
{
    // 参数验证
    if (no_fly_zones == NULL || output_path == NULL) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, 
                      "find_precomputed_return_path: NULL pointer");
        return -1;
    }
    
    // 线性搜索匹配的禁飞区组合
    for (int i = 0; i < PRECOMPUTED_PATH_COUNT; i++) {
        const precomputed_path_t* entry = &test_path_lookup_table[i];
        
        // 比较禁飞区（必须完全匹配）
        if (entry->no_fly_zones[0] == no_fly_zones[0] &&
            entry->no_fly_zones[1] == no_fly_zones[1] &&
            entry->no_fly_zones[2] == no_fly_zones[2]) {
            
            // 找到匹配项，复制返航路径数据
            u8 return_length = entry->return_length;
            
            for (int j = 0; j < return_length; j++) {
                output_path[j] = entry->return_sequence[j];
            }
            
            // 调试输出
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, 
                          "find_precomputed_return_path: Found optimal return path");
            AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)return_length, 
                             "Return path length:");
            
            return return_length;
        }
    }
    
    // 未找到匹配的返航路径
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, 
                  "find_precomputed_return_path: No matching return path found");
    return -1;
}

/**
 * @brief 直接获取预计算返航路径指针
 */
const u8* find_precomputed_return_path_direct(const u8* no_fly_zones, int* return_length)
{
    // 参数验证
    if (no_fly_zones == NULL || return_length == NULL) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, 
                      "find_precomputed_return_path_direct: NULL pointer");
        return NULL;
    }
    
    // 线性搜索匹配的禁飞区组合
    for (int i = 0; i < PRECOMPUTED_PATH_COUNT; i++) {
        const precomputed_path_t* entry = &test_path_lookup_table[i];
        
        // 比较禁飞区（必须完全匹配）
        if (entry->no_fly_zones[0] == no_fly_zones[0] &&
            entry->no_fly_zones[1] == no_fly_zones[1] &&
            entry->no_fly_zones[2] == no_fly_zones[2]) {
            
            // 找到匹配项，直接返回Flash指针
            *return_length = entry->return_length;
            
            // 调试输出
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, 
                          "find_precomputed_return_path_direct: Found return path pointer");
            AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)*return_length, 
                             "Return path length:");
            
            return entry->return_sequence;
        }
    }
    
    // 未找到匹配的返航路径
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, 
                  "find_precomputed_return_path_direct: No matching return path found");
    *return_length = 0;
    return NULL;
}

/**
 * @brief 测试函数
 */
int main()
{
    printf("🚀 path_storage查找函数编译测试\n");
    printf("版权：米醋电子工作室\n");
    printf("======================================\n");
    
    // 测试用例
    u8 test_no_fly_zones[][3] = {
        {11, 21, 31},
        {33, 34, 35},
        {55, 56, 57},
        {99, 88, 77}  // 不存在的组合
    };
    
    u8 output_buffer[MAX_RETURN_LENGTH];
    
    for (int i = 0; i < 4; i++) {
        printf("\n📋 测试用例 %d: 禁飞区[%d, %d, %d]\n", 
               i+1, test_no_fly_zones[i][0], test_no_fly_zones[i][1], test_no_fly_zones[i][2]);
        
        // 测试复制版本
        int result = find_precomputed_return_path(test_no_fly_zones[i], output_buffer);
        if (result > 0) {
            printf("   ✅ 复制版本: 找到返航路径，长度 %d\n", result);
            printf("   📍 路径: ");
            for (int j = 0; j < result && j < 5; j++) {
                printf("%d ", output_buffer[j]);
            }
            if (result > 5) printf("...");
            printf("\n");
        } else {
            printf("   ❌ 复制版本: 未找到返航路径\n");
        }
        
        // 测试直接指针版本
        int direct_length;
        const u8* direct_path = find_precomputed_return_path_direct(test_no_fly_zones[i], &direct_length);
        if (direct_path != NULL) {
            printf("   ✅ 直接版本: 找到返航路径指针，长度 %d\n", direct_length);
            printf("   📍 路径: ");
            for (int j = 0; j < direct_length && j < 5; j++) {
                printf("%d ", direct_path[j]);
            }
            if (direct_length > 5) printf("...");
            printf("\n");
        } else {
            printf("   ❌ 直接版本: 未找到返航路径\n");
        }
    }
    
    printf("\n======================================\n");
    printf("🎉 编译测试完成!\n");
    printf("📊 结构体大小: %zu 字节\n", sizeof(precomputed_path_t));
    printf("📦 测试数据大小: %zu 字节\n", sizeof(test_path_lookup_table));
    
    return 0;
}
