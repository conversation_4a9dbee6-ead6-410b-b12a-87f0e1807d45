#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
返航路径计算功能测试
版权信息：米醋电子工作室
创建日期：2025-07-31

功能描述：
测试扩展后的Dijkstra算法和PathPrecomputer的返航路径计算功能
验证返航路径的正确性、有效性和性能
"""

import sys
import os
import time

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from algorithms.dijkstra import DijkstraPlanner
from core.grid_map import GridMap

def test_return_path_calculation():
    """测试返航路径计算功能"""
    print("🧪 测试返航路径计算功能")
    print("=" * 50)
    
    # 初始化
    planner = DijkstraPlanner()
    grid_map = GridMap()
    
    # 测试用例1：简单禁飞区组合
    test_cases = [
        {
            'name': '水平禁飞区 [11, 21, 31]',
            'no_fly_zones': [11, 21, 31],
            'description': '第一行的3个连续禁飞区'
        },
        {
            'name': '垂直禁飞区 [12, 13, 14]', 
            'no_fly_zones': [12, 13, 14],
            'description': 'B2列的3个连续禁飞区'
        },
        {
            'name': '中心禁飞区 [44, 45, 46]',
            'no_fly_zones': [44, 45, 46], 
            'description': '中心位置的3个连续禁飞区'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"   描述: {test_case['description']}")
        print(f"   禁飞区: {test_case['no_fly_zones']}")
        
        # 设置禁飞区
        grid_map = GridMap()
        grid_map.set_no_fly_zones(test_case['no_fly_zones'])
        
        # 计算巡查路径
        start_pos = (0, 8)  # A9B1
        start_time = time.time()
        patrol_result = planner.plan_path(grid_map, start_pos)
        patrol_time = (time.time() - start_time) * 1000
        
        if patrol_result.is_valid() and len(patrol_result.path_sequence) > 0:
            print(f"   ✅ 巡查路径: {len(patrol_result.path_sequence)} 点")
            print(f"   📍 巡查终点: {patrol_result.path_sequence[-1]}")
            
            # 计算返航路径
            last_position_code = patrol_result.path_sequence[-1]
            last_grid_pos = position_code_to_grid(last_position_code)
            
            start_time = time.time()
            return_path = planner.calculate_return_path(grid_map, last_grid_pos, start_pos)
            return_time = (time.time() - start_time) * 1000
            
            if return_path:
                print(f"   ✅ 返航路径: {len(return_path)} 点")
                print(f"   🛤️  返航序列: {return_path[:5]}{'...' if len(return_path) > 5 else ''}")
                print(f"   ⏱️  计算时间: 巡查 {patrol_time:.2f}ms, 返航 {return_time:.2f}ms")
                
                # 验证返航路径
                if validate_return_path(return_path, test_case['no_fly_zones'], last_position_code):
                    print(f"   ✅ 返航路径验证通过")
                else:
                    print(f"   ❌ 返航路径验证失败")
            else:
                print(f"   ❌ 返航路径计算失败")
        else:
            print(f"   ❌ 巡查路径计算失败")

def position_code_to_grid(position_code: int) -> tuple:
    """将position_code转换为网格坐标"""
    col_num = position_code % 10  # B部分：1-7
    row_num = position_code // 10  # A部分：1-9
    
    grid_row = col_num - 1  # B1->0, B7->6
    grid_col = 9 - row_num  # A9->0, A1->8
    
    return (grid_row, grid_col)

def validate_return_path(return_path: list, no_fly_zones: list, start_code: int) -> bool:
    """验证返航路径的有效性"""
    if not return_path:
        return False
    
    # 检查起点
    if return_path[0] != start_code:
        print(f"     ❌ 起点错误: 期望 {start_code}, 实际 {return_path[0]}")
        return False
    
    # 检查终点
    if return_path[-1] != 91:  # A9B1
        print(f"     ❌ 终点错误: 期望 91, 实际 {return_path[-1]}")
        return False
    
    # 检查是否穿越禁飞区
    for position in return_path:
        if position in no_fly_zones:
            print(f"     ❌ 穿越禁飞区: 位置 {position}")
            return False
    
    # 检查路径长度合理性
    if len(return_path) < 2:
        print(f"     ❌ 路径过短: {len(return_path)} 点")
        return False
    
    if len(return_path) > 25:
        print(f"     ⚠️  路径较长: {len(return_path)} 点")
    
    return True

def test_precomputer_integration():
    """测试PathPrecomputer集成"""
    print("\n🔧 测试PathPrecomputer集成")
    print("=" * 50)
    
    # 导入PathPrecomputer
    from precompute_paths import PathPrecomputer
    
    precomputer = PathPrecomputer()
    
    # 测试单个组合
    test_combination = [33, 34, 35]  # A3B3, A3B4, A3B5
    print(f"📋 测试组合: {test_combination}")
    
    start_time = time.time()
    result = precomputer.compute_path_for_combination(test_combination)
    total_time = (time.time() - start_time) * 1000
    
    print(f"⏱️  总计算时间: {total_time:.2f}ms")
    print(f"📊 计算结果:")
    print(f"   巡查路径: {result['path_length']} 点")
    print(f"   返航路径: {result['return_length']} 点")
    print(f"   覆盖率: {result['coverage_rate']:.1f}%")
    print(f"   有效性: {'✅' if result['is_valid'] else '❌'}")
    
    if result['return_length'] > 0:
        print(f"   返航序列: {result['return_path'][:5]}{'...' if result['return_length'] > 5 else ''}")

def main():
    """主测试函数"""
    print("🚀 返航路径计算功能测试")
    print("版权：米醋电子工作室")
    print("=" * 70)
    
    try:
        # 测试基础返航路径计算
        test_return_path_calculation()
        
        # 测试PathPrecomputer集成
        test_precomputer_integration()
        
        print("\n" + "=" * 70)
        print("🎉 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
