/******************** (C) COPYRIGHT 2017 ANO Tech ********************************
 * ����    �������ƴ�
 * ����    ��www.anotc.com
 * �Ա�    ��anotc.taobao.com
 * ����QȺ ��190169595
 * ����    ���ɿس�ʼ��
**********************************************************************************/
#include "Drv_BSP.h"
#include "Drv_PwmOut.h"
#include "Drv_Dshot600.h"
#include "Drv_led.h"
#include "Drv_RcIn.h"
#include "Drv_Timer.h"
#include "DataTransfer.h"
#include "Drv_UbloxGPS.h"
#include "Drv_Uart.h"
#include "Drv_Timer.h"
#include "Drv_Usb.h"
#include "Drv_Adc.h"
#include "Tofsense-m.h"
#include "tofmini.h"
#include "PID.h"
#include "User_Task.h"

uint8_t EmergencyStopESC = 0;

uint8_t All_Init()
{
    DrvSysInit();
    //��ʱ
    MyDelayMs(100);
    //USB����ѡ��
    UsbPortCtlInit();
    UsbPortCtl(0);
    DrvUsbInit();
    //LED���ܳ�ʼ��
    DvrLedInit();
    //��ʼ������������
	if(ESC_TYPE == 1)
		DrvDshot600Init();
	else 
		DrvPwmOutInit();
    MyDelayMs(100);
    //
    AnoPTv8ParInit();
    AnoPTv8CmdInit();
    //����1��ʼ������������Ϊ������  IMU
    DrvUart1Init(1000000);
    //����2��ʼ������������Ϊ������  PMU
    DrvUart2Init(500000);
    //����3��ʼ��                    
    DrvUart3Init(115200);      //TOF
    //�ӻ��ص���										
    DrvUart4Init(115200);      //MID360
    //����5
    DrvUart5Init(115200);      //zigbee
    //����7
    DrvUart7Init(115200);			//Maixcam
    //����8
    DrvUart8Init(500000);
    MyDelayMs(100);
    //SBUS����ɼ���ʼ��
    DrvRcInputInit();
    MyDelayMs(100);
    //TOF�ӿڳ�ʼ��
		//tof_init();
		tofmini_init();
    //��ʼ����ʱ�ж�
    DrvTimerFcInit();
	//
	DrvAdcInit();
	PID_Init();
	jiguang(0,1,0);
    //��ʼ����ɣ�����1
    return (1);
}

_rc_input_st rc_in;
_rc_sbus_un sbus_in;

void DrvRcInputInit(void)
{
    //�����ʼ��һ��ģʽ
    //DrvRcPpmInit();
    DrvRcSbusInit();
    //�ȱ��λ��ʧ
    rc_in.no_signal = 1;
    rc_in.fail_safe = 1;
}

void DrvPpmGetOneCh(uint16_t  data)
{
    static uint8_t ch_sta = 0;

    if ((data > 2500 && ch_sta > 3) || ch_sta == 10)
    {
        ch_sta = 0;
        rc_in.signal_cnt_tmp++;
        rc_in.rc_in_mode_tmp = 1; //�л�ģʽ���Ϊppm
    }
    else if (data > 300 && data < 3000) //�쳣��������˵�
    {
        //
        rc_in.ppm_ch[ch_sta] = data;
        ch_sta++;
    }
}

void DrvSbusGetOneByte(uint8_t data)
{
    /*
    sbus flags�Ľṹ������ʾ��
    flags��
    bit7 = ch17 = digital channel (0x80)
    bit6 = ch18 = digital channel (0x40)
    bit5 = Frame lost, equivalent red LED on receiver (0x20)
    bit4 = failsafe activated (0x10) b: 0001 0000
    bit3 = n/a
    bit2 = n/a
    bit1 = n/a
    bit0 = n/a
    */
    const uint8_t frame_end[4] = {0x04, 0x14, 0x24, 0x34};
    static uint32_t sbus_time[2];
    static uint8_t datatmp[25];
    static uint8_t cnt = 0;
    static uint8_t frame_cnt;
    //
    sbus_time[0] = sbus_time[1];
    sbus_time[1] = GetSysRunTimeUs();

    if ((u32)(sbus_time[1] - sbus_time[0]) > 2500)
    {
        cnt = 0;
    }

    //
    if (cnt >= 25)
    {
        cnt = 0;
    }

    datatmp[cnt++] = data;

    //
    if (cnt == 25)
    {
        cnt = 24;

        if ((datatmp[0] == 0x0F && (datatmp[24] == 0x00 || datatmp[24] == frame_end[frame_cnt])))
        {
            cnt = 0;
            memcpy(sbus_in.rawdata, datatmp, 25);
            rc_in.sbus_ch[0] = sbus_in.stdata.ch0;
            rc_in.sbus_ch[1] = sbus_in.stdata.ch1;
            rc_in.sbus_ch[2] = sbus_in.stdata.ch2;
            rc_in.sbus_ch[3] = sbus_in.stdata.ch3;
            rc_in.sbus_ch[4] = sbus_in.stdata.ch4;
            rc_in.sbus_ch[5] = sbus_in.stdata.ch5;
            rc_in.sbus_ch[6] = sbus_in.stdata.ch6;
            rc_in.sbus_ch[7] = sbus_in.stdata.ch7;
            rc_in.sbus_ch[8] = sbus_in.stdata.ch8;
            rc_in.sbus_ch[9] = sbus_in.stdata.ch9;
            rc_in.sbus_ch[10] = sbus_in.stdata.ch10;
            rc_in.sbus_ch[11] = sbus_in.stdata.ch11;
            rc_in.sbus_ch[12] = sbus_in.stdata.ch12;
            rc_in.sbus_ch[13] = sbus_in.stdata.ch13;
            rc_in.sbus_ch[14] = sbus_in.stdata.ch14;
            rc_in.sbus_ch[15] = sbus_in.stdata.ch15;
            rc_in.sbus_flag = sbus_in.stdata.flag;

            //user
            //
            if (rc_in.sbus_flag & 0x08)
            {
                //������������ܽ��յ���ʧ�ر�ǣ��򲻴�����ת�޳�������ʧ�ء�
            }
            else
            {
                rc_in.signal_cnt_tmp++;
                rc_in.rc_in_mode_tmp = 2; //�л�ģʽ���Ϊsbus
            }

            //֡β����
            frame_cnt++;
            frame_cnt %= 4;
        }
        else
        {
            for (uint8_t i = 0; i < 24; i++)
            {
                datatmp[i] = datatmp[i + 1];
            }
        }
    }
}

static void rcSignalCheck(float *dT_s)
{
    //
    static uint8_t cnt_tmp;
    static uint16_t  time_dly;
    time_dly += ( *dT_s) * 1e3f;

    //==1000ms==
    if (time_dly > 1000)
    {
        time_dly = 0;
        //
        rc_in.signal_fre = rc_in.signal_cnt_tmp;

        //==�ж��ź��Ƿ�ʧ
        if (rc_in.signal_fre < 5)
        {
            rc_in.no_signal = 1;
        }
        else
        {
            rc_in.no_signal = 0;
        }

        //==�ж��Ƿ��л����뷽ʽ
        if (rc_in.no_signal)
        {
            //��ʼ0
            if (rc_in.sig_mode == 0)
            {
                cnt_tmp++;
                cnt_tmp %= 3;

                if (cnt_tmp == 1)
                {
                    DrvRcSbusInit();
                }
                else if (cnt_tmp == 2)
                {
                    DrvRcPpmInit();
                }
				else
				{
					DrvRcCrsfInit();
				}
            }
        }
        else
        {
            rc_in.sig_mode = rc_in.rc_in_mode_tmp;
        }

        //==
        rc_in.signal_cnt_tmp = 0;
    }
}

#define RC_NO_CHECK 1  //0：监测遥控信号；1：不检测遥控信号
// 【遥控器依赖问题修复】改为1以支持纯上位机控制，不依赖遥控器信号
//
void DrvRcInputTask(float dT_s)
{
    static uint8_t failsafe;
    //�źż��
    rcSignalCheck( &dT_s);

    //���ź�
    if (rc_in.no_signal == 0)
    {
        //ppm
        if (rc_in.sig_mode == 1)
        {
            for (uint8_t i = 0; i < 10; i++) //ע��ֻ��10��ͨ��
            {
                rc_in.rc_ch.st_data.ch_[i] = rc_in.ppm_ch[i];
            }
        }
        //sbus
        else if (rc_in.sig_mode == 2)
        {
            for (uint8_t i = 0; i < 10; i++) //ע��ֻ��10��ͨ��
            {
                rc_in.rc_ch.st_data.ch_[i] = 0.644f * (rc_in.sbus_ch[i] - 1024) + 1500; //248 --1024 --1800ת����1000-2000
            }
        }
		//crsf
		else if (rc_in.sig_mode == 3)
        {
            for (uint8_t i = 0; i < 10; i++) //ע��ֻ��10��ͨ��
            {
                rc_in.rc_ch.st_data.ch_[i] = 0.61013f * (rc_in.crsf_ch[i] - 172) + 1000; //172  --992  --1811 ת����1000-2000
            }
        }

        //���ʧ�ر�������
        if (
            (rc_in.rc_ch.st_data.ch_[RCCNANNELDEF_FLIGHTMODE] > 1200 && rc_in.rc_ch.st_data.ch_[RCCNANNELDEF_FLIGHTMODE] < 1400)
            || (rc_in.rc_ch.st_data.ch_[RCCNANNELDEF_FLIGHTMODE] > 1600 && rc_in.rc_ch.st_data.ch_[RCCNANNELDEF_FLIGHTMODE] < 1800))
        {
            //�������ã����Ϊʧ��
            failsafe = 1;
        }
        else
        {
            failsafe = 0;
        }
    }
    //���ź�
    else
    {
        //ʧ�ر����λ
        failsafe = 1;

        //
        for (uint8_t i = 0; i < 10; i++) //ע��ֻ��10��ͨ��
        {
            rc_in.rc_ch.st_data.ch_[i] = 0; //
        }
    }

#if (RC_NO_CHECK == 0)
    //ʧ�ر��
    rc_in.fail_safe = failsafe;
#else

    //���źŻ��߼�⵽ʧ��
    if (rc_in.no_signal != 0 || failsafe != 0)
    {
        for (uint8_t i = 0; i < 10; i++)
        {
            rc_in.rc_ch.st_data.ch_[i] = 1500;
        }
    }

    //�����ʧ��
    rc_in.fail_safe = 0;
#endif
}

/******************* (C) COPYRIGHT 2014 ANO TECH *****END OF FILE************/
