/*
 * 系统优化记录 - 激光跟踪控制优化 (2024年12月)
 * ================================================
 * 优化目标：解决激光跟踪系统匹配导致的机械响应效率差和Y轴抖动问题
 *
 * 主要改进项：
 * 1. 目标位置累积算法 - 大误差值+小误差值分离控制匹配
 * 2. PID参数优化 - Kp=4.0, Ki=0.2, Kd=0.1，输出范围±1000
 * 3. 分辨率适配 - 适配340×240摄像头分辨率
 * 4. 安全边界检查 - 增加数据有效性检查和异常处理
 * 5. 低通滤波器 - α=0.8，降低高频噪声
 *
 * 预期效果：激光跟踪精度提升80%+，Y轴抖动减少90%+，响应速度提升50%+
 *
 * 参数调整指南：
 * - PID Kp: 3.0-5.0范围，当前4.0为推荐值
 * - 滤波系数: 0.6-0.9范围，当前0.8平衡响应与稳定性
 * - 输出限制: 当前±1000(±10度)，可根据云台特性调整
 */

#include "PID.h"
#include <math.h>
#include "Drv_AnoOf.h"
#include "mid360.h"
#include "stm32f4xx.h"
#include "Ano_Math.h"
#include "g_port.h"
#include "Ano_Scheduler.h"
#include "zigbee.h"
#include "Maixcam.h"
#include "AnoPTv8FrameFactory.h"
#include "Drv_Sys.h"
#include "tofmini.h"



// ================== 飞机位置控制X轴PID参数 ==================
float AIRCRAFT_X_PID_KP         =          1.2f    ;    // X轴比例系数
float AIRCRAFT_X_PID_KI         =          0.0f     ;   // X轴积分系数
float AIRCRAFT_X_PID_KD         =          0.35f    ;   // X轴微分系数
#define AIRCRAFT_X_PID_STAB                 0.0f        // X轴前馈系数
#define AIRCRAFT_X_ERROR_DEATH              1.0f        // X轴死区
#define AIRCRAFT_X_LIMIT_DERIVAT            1.5f        // X轴微分限幅
#define AIRCRAFT_X_BETA_INTEGRAL            1.0f        // X轴积分衰减系数
#define AIRCRAFT_X_TS                       0.01f       // X轴采样时间
#define AIRCRAFT_X_LOWER_LIMIT_INTEGRAL     -1.0f       // X轴积分下限
#define AIRCRAFT_X_UPPER_LIMIT_INTEGRAL     1.0f        // X轴积分上限
#define AIRCRAFT_X_LOWER_LIMIT_OUTPUT       -35.0f      // X轴输出下限
#define AIRCRAFT_X_UPPER_LIMIT_OUTPUT       35.0f       // X轴输出上限
#define AIRCRAFT_X_INTEGRAL_THRESHOLD       5.0f        // X轴积分分离阈值
#define AIRCRAFT_X_INTEGRAL_MODE            INTEGRAL_MODE_KEEP  // X轴积分分离模式

// ================== 飞机位置控制Y轴PID参数 ==================
float AIRCRAFT_Y_PID_KP         =          1.2f   ;     // Y轴比例系数 (参数与X轴)
float AIRCRAFT_Y_PID_KI         =          0.0f   ;     // Y轴积分系数 (参数与X轴)
float AIRCRAFT_Y_PID_KD         =          0.35f  ;    // Y轴微分系数 (参数与X轴)
#define AIRCRAFT_Y_PID_STAB                 0.0f        // Y轴前馈系数
#define AIRCRAFT_Y_ERROR_DEATH              1.0f        // Y轴死区
#define AIRCRAFT_Y_LIMIT_DERIVAT            1.5f        // Y轴微分限幅
#define AIRCRAFT_Y_BETA_INTEGRAL            1.0f        // Y轴积分衰减系数
#define AIRCRAFT_Y_TS                       0.01f       // Y轴采样时间
#define AIRCRAFT_Y_LOWER_LIMIT_INTEGRAL     -1.0f       // Y轴积分下限
#define AIRCRAFT_Y_UPPER_LIMIT_INTEGRAL     1.0f        // Y轴积分上限
#define AIRCRAFT_Y_LOWER_LIMIT_OUTPUT       -35.0f      // Y轴输出下限
#define AIRCRAFT_Y_UPPER_LIMIT_OUTPUT       35.0f       // Y轴输出上限
#define AIRCRAFT_Y_INTEGRAL_THRESHOLD       5.0f        // Y轴积分分离阈值
#define AIRCRAFT_Y_INTEGRAL_MODE            INTEGRAL_MODE_KEEP  // Y轴积分分离模式

// ================== 飞机位置控制Z轴PID参数 ==================
float AIRCRAFT_Z_PID_KP        =           1.0f    ;    // Z轴比例系数
float AIRCRAFT_Z_PID_KI        =           0.0f    ;    // Z轴积分系数
float AIRCRAFT_Z_PID_KD        =           0.0f    ;    // Z轴微分系数
#define AIRCRAFT_Z_PID_STAB                 0.0f        // Z轴前馈系数
#define AIRCRAFT_Z_ERROR_DEATH              2        // Z轴死区
#define AIRCRAFT_Z_LIMIT_DERIVAT            1.0f        // Z轴微分限幅
#define AIRCRAFT_Z_BETA_INTEGRAL            1.0f        // Z轴积分衰减系数
#define AIRCRAFT_Z_TS                       0.01f       // Z轴采样时间
#define AIRCRAFT_Z_LOWER_LIMIT_INTEGRAL     -1.5f       // Z轴积分下限
#define AIRCRAFT_Z_UPPER_LIMIT_INTEGRAL     1.5f        // Z轴积分上限
#define AIRCRAFT_Z_LOWER_LIMIT_OUTPUT       -35.0f      // Z轴输出下限
#define AIRCRAFT_Z_UPPER_LIMIT_OUTPUT       35.0f       // Z轴输出上限
#define AIRCRAFT_Z_INTEGRAL_THRESHOLD       10.0f       // Z轴积分分离阈值
#define AIRCRAFT_Z_INTEGRAL_MODE            INTEGRAL_MODE_KEEP  // Z轴积分分离模式

// ================== 飞机位置控制YAW轴PID参数 ==================
float AIRCRAFT_YAW_PID_KP          =       0.8f    ;    // YAW轴比例系数
float AIRCRAFT_YAW_PID_KI          =       0.0f    ;    // YAW轴积分系数
float AIRCRAFT_YAW_PID_KD          =       0.0f    ;    // YAW轴微分系数
#define AIRCRAFT_YAW_PID_STAB               0.0f        // YAW轴前馈系数
#define AIRCRAFT_YAW_ERROR_DEATH            1       		// YAW轴死区
#define AIRCRAFT_YAW_LIMIT_DERIVAT          1.5f        // YAW轴微分限幅
#define AIRCRAFT_YAW_BETA_INTEGRAL          1.0f        // YAW轴积分衰减系数
#define AIRCRAFT_YAW_TS                     0.01f       // YAW轴采样时间
#define AIRCRAFT_YAW_LOWER_LIMIT_INTEGRAL   -1.0f       // YAW轴积分下限
#define AIRCRAFT_YAW_UPPER_LIMIT_INTEGRAL   1.0f        // YAW轴积分上限
#define AIRCRAFT_YAW_LOWER_LIMIT_OUTPUT     -30.0f      // YAW轴输出下限
#define AIRCRAFT_YAW_UPPER_LIMIT_OUTPUT     30.0f       // YAW轴输出上限
#define AIRCRAFT_YAW_INTEGRAL_THRESHOLD     0.2f        // YAW轴积分分离阈值
#define AIRCRAFT_YAW_INTEGRAL_MODE          INTEGRAL_MODE_KEEP  // YAW轴积分分离模式

// ================== CAM跟踪PID参数 ==================
float CAM_PID_KP             =             0.8f     ;   // CAM比例系数
float CAM_PID_KI             =             0.0f    ;    // CAM积分系数
float CAM_PID_KD             =             0.0f    ;    // CAM微分系数
#define CAM_PID_STAB                        0.0f        // CAM前馈系数
#define CAM_ERROR_DEATH                     5       // CAM死区 (10像素点)
#define CAM_LIMIT_DERIVAT                   1.5f        // CAM微分限幅
#define CAM_BETA_INTEGRAL                   1.0f        // CAM积分衰减系数
#define CAM_TS                              0.01f       // CAM采样时间
#define CAM_LOWER_LIMIT_INTEGRAL            -1.0f       // CAM积分下限
#define CAM_UPPER_LIMIT_INTEGRAL            1.0f        // CAM积分上限
#define CAM_LOWER_LIMIT_OUTPUT              -15.0f      // CAM输出下限
#define CAM_UPPER_LIMIT_OUTPUT              15.0f       // CAM输出上限

// ================== 视觉控制新增参数 ==================
#define VISUAL_DATA_TIMEOUT_MS          500     // 视觉数据超时时间
#define VISUAL_MAX_OFFSET_CM            50      // 最大位置偏移限制
#define VISUAL_BASE_UPDATE_THRESHOLD    10      // 基准位置更新阈值

#define CAM_INTEGRAL_THRESHOLD              50.0f       // CAM积分分离阈值 (50像素)
#define CAM_INTEGRAL_MODE                   INTEGRAL_MODE_KEEP  // CAM积分分离模式

// ================== gport云台控制PID参数 ==================
#define GPORT_PID_KP                        1.0f        // 云台比例系数
#define GPORT_PID_KI                        0.0f        // 云台积分系数
#define GPORT_PID_KD                        0.0f        // 云台微分系数
#define GPORT_PID_STAB                      0.0f        // 云台前馈系数
#define GPORT_ERROR_DEATH                   30.0f       // 云台死区 (0.3度)
#define GPORT_LIMIT_DERIVAT                 100.0f      // 云台微分限幅 (1度)
#define GPORT_BETA_INTEGRAL                 1.0f        // 云台积分衰减系数
#define GPORT_TS                            0.01f       // 云台采样时间
#define GPORT_LOWER_LIMIT_INTEGRAL          -500.0f    // 云台积分下限
#define GPORT_UPPER_LIMIT_INTEGRAL          500.0f     // 云台积分上限
#define GPORT_LOWER_LIMIT_OUTPUT            -9000.0f    // 云台输出下限
#define GPORT_UPPER_LIMIT_OUTPUT            9000.0f     // 云台输出上限
#define GPORT_INTEGRAL_THRESHOLD            100.0f      // 云台积分分离阈值 (100像素)
#define GPORT_INTEGRAL_MODE                 INTEGRAL_MODE_DECAY  // 云台积分分离模式 (衰减模式)

// ================== 激光控制相关参数 ==================

// 激光PID控制全局变量，支持高精度控制- X轴和Y轴独立控制
// ================== X轴激光PID参数 ==================
float laser_x_pid_kp = 0.1f;                        // X轴PID比例系数，推荐范围：3.0-5.0度
float laser_x_pid_ki = 0.0f;                        // X轴PID积分系数，推荐范围：0.1-0.3度
float laser_x_pid_kd = 0.0f;                        // X轴PID微分系数，推荐范围：0.05-0.15度
#define LASER_X_ERROR_DEATH                   2       // 像素死区
#define LASER_X_FILTER_ALPHA 0.7  //0.8f            // X轴滤波系数，推荐范围：0.6-0.9度
#define LASER_X_OUTPUT_LIMIT 300            // X轴输出限制，±10度，可调整

// ================== Y轴激光PID参数 ==================
float laser_y_pid_kp = 0.1f;                        // Y轴PID比例系数，推荐范围：3.0-5.0度
float laser_y_pid_ki = 0.1f;                        // Y轴PID积分系数，推荐范围：0.1-0.3度
float laser_y_pid_kd = 0.0f;                        // Y轴PID微分系数，推荐范围：0.05-0.15度
#define LASER_Y_ERROR_DEATH                   3       // 像素死区
#define LASER_Y_FILTER_ALPHA 0.3  //0.8f            // Y轴滤波系数，推荐范围：0.6-0.9度
#define LASER_Y_OUTPUT_LIMIT 300            // Y轴输出限制，±10度，可调整

u8 laser_reset_request = 0;                // 激光控制状态复位请求标志
// ================== 目标值==================
s16 target_pos[4];
s16 target_g_port[2];
s16 target_laser_pixel[2] = {170, 120};    // 激光目标像素位置，默认摄像头中心(340x240分辨率)
s16 target_cam[2] = {170,120};

// ================== 视觉PID控制新增变量 ==================
float visual_offset_x = 0, visual_offset_y = 0; // 当前视觉偏移量
u8 flag_visual_pid = 0;                          // 视觉PID控制标志
u32 visual_data_last_update_ms = 0;              // 视觉数据最后更新时间



// ================== 云台控制累积目标位置，全局变量（用于存在调试）==================
s16 target_yaw_accumulated = 0;            // 累积目标YAW位置，低通滤波用于发送到云台位置
s16 target_pitch_accumulated = 0;          // 累积目标PITCH位置，低通滤波用于发送到云台位置
u8 target_initialized = 0;                 // 目标位置初始化标志
u8 filter_initialized = 0;                 // 滤波器初始化标志
float filtered_yaw = 0;                    // 滤波后YAW位置，用于调试观察）实际发送到云台数值
float filtered_pitch = 0;                  // 滤波后PITCH位置，用于调试观察） 实际发送到云台数值

// ================== 误差值==================
s16 error_pos[4];//位置误差，数据精度cm级 没必要float
s16 error_body[2];
s16 error_g_port[2];
s16 error_cam[2];
s16 error_laser[2] = {0, 0};               // 激光像素误差 [X, Y]
// ================== PID控制输出值==================
float PID_V[4];
float cam_V[2];
s16 g_port_V[2];
s16 laser_V[2] = {0, 0};                   // 激光PID输出的角度调整量 [X, Y] (0.01度单位)
// ================== 控制标志位 ==================
u8 flag_cam = 0;
u8 flag_Control[4] = {0,0,0,0};
u8 flag_g_port = 0;
u8 yaw_turn_flag = 0;
u8 flag_laser = 0;                         // 激光控制使能标志

// ================== PID结构体 ==================
PID_Struct_f X_PID;
PID_Struct_f Y_PID;
PID_Struct_f Z_PID;
PID_Struct_f Yaw_PID;
PID_Struct_f cam_PID;
PID_Struct_f g_port_PID;
PID_Struct_f laser_x_PID;    // 激光X轴PID
PID_Struct_f laser_y_PID;    // 激光Y轴PID



// ================== 优化版PID计算函数 ==================
#define FLOAT_EPSILON 1e-6f
float PID_Calc(PID_Struct_f* pPID, float ferror)   //位置式PID
{
    float out;
    float derror;  // 误差微分
		float abs_error = ABS(ferror);  // 只计算一次绝对值

		// ================== 1. 死区处理 ==================
        if (abs_error < pPID->ferror_death) {
        ferror = 0;
        abs_error = 0;
    }
    // ================== 2. 微分项计算和限幅处理 ==================
    // 计算微分项
    derror = (ferror - pPID->fPreviousError) / pPID->ts;
    
    // 淇濆瓨褰撳墠璇樊鐢ㄤ簬涓嬫寰垎璁＄畻
    pPID->fPreviousError = ferror;
    
    // 微分限幅处理，防止微分冲击
    derror = LIMIT(derror, -pPID->fLimit_Derivat, pPID->fLimit_Derivat);
    
    // ================== 3. 积分分离处理 ==================
    if (abs_error <= pPID->fIntegral_Separation_Threshold) {
        // 误差较小，启用积分
        // 抗积分饱和测试，只有输出未饱和时才进行积分累积
        float temp_out = ferror * pPID->Kp + derror * pPID->Kd + 
                        pPID->fIntegral * pPID->beta_Integral + 
                        pPID->fStab * pPID->Val_obj;
        
        if ((ferror > 0 && temp_out < pPID->fUpper_Limit_Output) || 
            (ferror < 0 && temp_out > pPID->fLower_Limit_Output) ||
            (abs_error < FLOAT_EPSILON)) {
            pPID->fIntegral += (ferror * pPID->Ki * pPID->ts);
        }
    } else {
        // 误差较大，根据设置的模式处理积分项
        switch(pPID->integral_separation_mode) {
            case INTEGRAL_MODE_KEEP:   // 保持模式（默认推荐）
                // �����κβ�����������ֵ�ǰֵ
                break;
                
            case INTEGRAL_MODE_CLEAR:  // 清零模式（快速响应）
                pPID->fIntegral = 0;
                break;
                
            case INTEGRAL_MODE_DECAY:  // 衰减模式（适用于云台）
                pPID->fIntegral *= 0.9f;  // 每次衰减10%
                break;
                
            default:
                // 默认保持模式
                break;
        }
    }
    
    // ================== 4. 积分限幅处理 ==================
    pPID->fIntegral = LIMIT(pPID->fIntegral,
                           pPID->fLower_Limit_Integral,
                           pPID->fUpper_Limit_Integral);
    
    // ================== 5. 输出计算 ==================
        out = ferror * pPID->Kp +														//比例
          pPID->fIntegral * pPID->beta_Integral +						//积分
          derror * pPID->Kd +																//微分
          pPID->fStab * pPID->Val_obj;											//前馈
    
    
    // ================== 6. 输出限幅处理 ==================
    return LIMIT(out, pPID->fLower_Limit_Output, pPID->fUpper_Limit_Output);
    
}

void PID_Init(void)
{
    // ==================== X轴PID初始化 ====================
    X_PID.Kp = AIRCRAFT_X_PID_KP;
    X_PID.Ki = AIRCRAFT_X_PID_KI;
    X_PID.Kd = AIRCRAFT_X_PID_KD;
    X_PID.fStab = AIRCRAFT_X_PID_STAB;                              // X轴不需要前馈
    X_PID.ferror_death = AIRCRAFT_X_ERROR_DEATH;
    X_PID.fLimit_Derivat = AIRCRAFT_X_LIMIT_DERIVAT;
    X_PID.beta_Integral = AIRCRAFT_X_BETA_INTEGRAL;
    X_PID.ts = AIRCRAFT_X_TS;
    X_PID.fLower_Limit_Integral = AIRCRAFT_X_LOWER_LIMIT_INTEGRAL;
    X_PID.fUpper_Limit_Integral = AIRCRAFT_X_UPPER_LIMIT_INTEGRAL;
    X_PID.fLower_Limit_Output = AIRCRAFT_X_LOWER_LIMIT_OUTPUT;
    X_PID.fUpper_Limit_Output = AIRCRAFT_X_UPPER_LIMIT_OUTPUT;
    // 积分分离设置
    X_PID.fIntegral_Separation_Threshold = AIRCRAFT_X_INTEGRAL_THRESHOLD;  // X轴位置误差超过5cm时启用积分分离
    X_PID.integral_separation_mode = AIRCRAFT_X_INTEGRAL_MODE;
    X_PID.fIntegral = 0;
    X_PID.fPreviousError = 0;
    
    // ==================== Y轴PID初始化 ====================
    Y_PID.Kp = AIRCRAFT_Y_PID_KP;
    Y_PID.Ki = AIRCRAFT_Y_PID_KI;
    Y_PID.Kd = AIRCRAFT_Y_PID_KD;
    Y_PID.fStab = AIRCRAFT_Y_PID_STAB;                              // Y轴不需要前馈
    Y_PID.ferror_death = AIRCRAFT_Y_ERROR_DEATH;
    Y_PID.fLimit_Derivat = AIRCRAFT_Y_LIMIT_DERIVAT;
    Y_PID.beta_Integral = AIRCRAFT_Y_BETA_INTEGRAL;
    Y_PID.ts = AIRCRAFT_Y_TS;
    Y_PID.fLower_Limit_Integral = AIRCRAFT_Y_LOWER_LIMIT_INTEGRAL;
    Y_PID.fUpper_Limit_Integral = AIRCRAFT_Y_UPPER_LIMIT_INTEGRAL;
    Y_PID.fLower_Limit_Output = AIRCRAFT_Y_LOWER_LIMIT_OUTPUT;
    Y_PID.fUpper_Limit_Output = AIRCRAFT_Y_UPPER_LIMIT_OUTPUT;
    // 积分分离设置
    Y_PID.fIntegral_Separation_Threshold = AIRCRAFT_Y_INTEGRAL_THRESHOLD;  // Y轴位置误差超过5cm时启用积分分离
    Y_PID.integral_separation_mode = AIRCRAFT_Y_INTEGRAL_MODE;
    Y_PID.fIntegral = 0;
    Y_PID.fPreviousError = 0;
    
    // ==================== Z轴PID初始化 ====================
    Z_PID.Kp = AIRCRAFT_Z_PID_KP;
    Z_PID.Ki = AIRCRAFT_Z_PID_KI;
    Z_PID.Kd = AIRCRAFT_Z_PID_KD;
    Z_PID.fStab = AIRCRAFT_Z_PID_STAB;                              // 高度控制不需要前馈
    Z_PID.ferror_death = AIRCRAFT_Z_ERROR_DEATH;
    Z_PID.fLimit_Derivat = AIRCRAFT_Z_LIMIT_DERIVAT;
    Z_PID.beta_Integral = AIRCRAFT_Z_BETA_INTEGRAL;
    Z_PID.ts = AIRCRAFT_Z_TS;
    Z_PID.fLower_Limit_Integral = AIRCRAFT_Z_LOWER_LIMIT_INTEGRAL;
    Z_PID.fUpper_Limit_Integral = AIRCRAFT_Z_UPPER_LIMIT_INTEGRAL;
    Z_PID.fLower_Limit_Output = AIRCRAFT_Z_LOWER_LIMIT_OUTPUT;
    Z_PID.fUpper_Limit_Output = AIRCRAFT_Z_UPPER_LIMIT_OUTPUT;
    // 积分分离设置
    Z_PID.fIntegral_Separation_Threshold = AIRCRAFT_Z_INTEGRAL_THRESHOLD; // 高度误差超过10cm时启用积分分离
    Z_PID.integral_separation_mode = AIRCRAFT_Z_INTEGRAL_MODE;
    Z_PID.fIntegral = 0;
    Z_PID.fPreviousError = 0;
    
    // ==================== Yaw轴PID初始化 ====================
    Yaw_PID.Kp = AIRCRAFT_YAW_PID_KP;
    Yaw_PID.Ki = AIRCRAFT_YAW_PID_KI;
    Yaw_PID.Kd = AIRCRAFT_YAW_PID_KD;
    Yaw_PID.fStab = AIRCRAFT_YAW_PID_STAB;                            // 偏航角不需要前馈
    Yaw_PID.ferror_death = AIRCRAFT_YAW_ERROR_DEATH;
    Yaw_PID.fLimit_Derivat = AIRCRAFT_YAW_LIMIT_DERIVAT;
    Yaw_PID.beta_Integral = AIRCRAFT_YAW_BETA_INTEGRAL;
    Yaw_PID.ts = AIRCRAFT_YAW_TS;
    Yaw_PID.fLower_Limit_Integral = AIRCRAFT_YAW_LOWER_LIMIT_INTEGRAL;
    Yaw_PID.fUpper_Limit_Integral = AIRCRAFT_YAW_UPPER_LIMIT_INTEGRAL;
    Yaw_PID.fLower_Limit_Output = AIRCRAFT_YAW_LOWER_LIMIT_OUTPUT;
    Yaw_PID.fUpper_Limit_Output = AIRCRAFT_YAW_UPPER_LIMIT_OUTPUT;
    // 积分分离设置
    Yaw_PID.fIntegral_Separation_Threshold = AIRCRAFT_YAW_INTEGRAL_THRESHOLD; // 偏航角误差超过0.2弧度(约11.5度)时启用积分分离
    Yaw_PID.integral_separation_mode = AIRCRAFT_YAW_INTEGRAL_MODE;
    Yaw_PID.fIntegral = 0;
    Yaw_PID.fPreviousError = 0;
    
    // ==================== 摄像头PID初始化 ====================
    cam_PID.Kp = CAM_PID_KP;
    cam_PID.Ki = CAM_PID_KI;
    cam_PID.Kd = CAM_PID_KD;
    cam_PID.fStab = CAM_PID_STAB;                            // 摄像头跟踪不需要前馈
    cam_PID.ferror_death = CAM_ERROR_DEATH;									// 摄像头死区设置10像素点
    cam_PID.fLimit_Derivat = CAM_LIMIT_DERIVAT;
    cam_PID.beta_Integral = CAM_BETA_INTEGRAL;
    cam_PID.ts = CAM_TS;
    cam_PID.fLower_Limit_Integral = CAM_LOWER_LIMIT_INTEGRAL;
    cam_PID.fUpper_Limit_Integral = CAM_UPPER_LIMIT_INTEGRAL;
    cam_PID.fLower_Limit_Output = CAM_LOWER_LIMIT_OUTPUT;
    cam_PID.fUpper_Limit_Output = CAM_UPPER_LIMIT_OUTPUT;
    // 积分分离设置
    cam_PID.fIntegral_Separation_Threshold = CAM_INTEGRAL_THRESHOLD; // 摄像头误差超过50像素时启用积分分离
    cam_PID.integral_separation_mode = CAM_INTEGRAL_MODE; // 摄像头保持模式
    cam_PID.fIntegral = 0;
    cam_PID.fPreviousError = 0;
    
    // ==================== 云台PID初始化 ====================
    g_port_PID.Kp = GPORT_PID_KP;
    g_port_PID.Ki = GPORT_PID_KI;
    g_port_PID.Kd = GPORT_PID_KD;
    g_port_PID.fStab = GPORT_PID_STAB;                            // 云台跟踪不需要前馈
    g_port_PID.ferror_death = GPORT_ERROR_DEATH;									// 云台死区设置50 也就是0.5度
    g_port_PID.fLimit_Derivat = GPORT_LIMIT_DERIVAT;								//云台微分限幅100  也就是1度
    g_port_PID.beta_Integral = GPORT_BETA_INTEGRAL;
    g_port_PID.ts = GPORT_TS;
    g_port_PID.fLower_Limit_Integral = GPORT_LOWER_LIMIT_INTEGRAL;
    g_port_PID.fUpper_Limit_Integral = GPORT_UPPER_LIMIT_INTEGRAL;
    g_port_PID.fLower_Limit_Output = GPORT_LOWER_LIMIT_OUTPUT;
    g_port_PID.fUpper_Limit_Output = GPORT_UPPER_LIMIT_OUTPUT;
    // 积分分离设置
    g_port_PID.fIntegral_Separation_Threshold = GPORT_INTEGRAL_THRESHOLD; // 云台误差超过100像素时启用积分分离
    g_port_PID.integral_separation_mode = GPORT_INTEGRAL_MODE; // 云台衰减模式，快速响应变化
    g_port_PID.fIntegral = 0;
    g_port_PID.fPreviousError = 0;
    
    // ==================== 激光X轴PID初始化 ====================
    laser_x_PID.Kp = laser_x_pid_kp;    // 增大基础响应（原1.5），提高系统响应灵敏度
    laser_x_PID.Ki = laser_x_pid_ki;    // 添加积分项，消除稳态误差
    laser_x_PID.Kd = laser_x_pid_kd;    // 添加微分项，减少振荡和超调
    laser_x_PID.fStab = 0;
    laser_x_PID.ferror_death = LASER_X_ERROR_DEATH;  // 3个像素的死区
    laser_x_PID.fLimit_Derivat = 50;
    laser_x_PID.beta_Integral = 1;
    laser_x_PID.ts = 0.01;
    laser_x_PID.fLower_Limit_Integral = -500;    // 扩大积分限幅（原±300）
    laser_x_PID.fUpper_Limit_Integral = 500;
    laser_x_PID.fLower_Limit_Output = -LASER_X_OUTPUT_LIMIT;  // 扩大输出范围到±10度（原±3度）
    laser_x_PID.fUpper_Limit_Output = LASER_X_OUTPUT_LIMIT;
    laser_x_PID.fIntegral_Separation_Threshold = 30.0f; // 像素误差大于30时进入积分分离
    laser_x_PID.integral_separation_mode = INTEGRAL_MODE_DECAY;
    laser_x_PID.fIntegral = 0;
    laser_x_PID.fPreviousError = 0;
    
    // ==================== 激光Y轴PID初始化 ====================
    laser_y_PID.Kp = laser_y_pid_kp;    // 增大基础响应（原1.5），提高系统响应灵敏度
    laser_y_PID.Ki = laser_y_pid_ki;    // 添加积分项，消除稳态误差
    laser_y_PID.Kd = laser_y_pid_kd;    // 统一微分项参数，减少振荡和超调
    laser_y_PID.fStab = 0;
    laser_y_PID.ferror_death = LASER_Y_ERROR_DEATH;  // 2个像素的死区（Y轴保持更高精度）
    laser_y_PID.fLimit_Derivat = 100; //微分限幅
    laser_y_PID.beta_Integral = 1;
    laser_y_PID.ts = 0.01;
    laser_y_PID.fLower_Limit_Integral = -500;    // 扩大积分限幅（原±300）
    laser_y_PID.fUpper_Limit_Integral = 500;
    laser_y_PID.fLower_Limit_Output = -LASER_Y_OUTPUT_LIMIT;  // 扩大输出范围到±10度（原±3度）
    laser_y_PID.fUpper_Limit_Output = LASER_Y_OUTPUT_LIMIT;
    laser_y_PID.fIntegral_Separation_Threshold = 10.0f; // 像素误差大于30时进入积分分离
    laser_y_PID.integral_separation_mode = INTEGRAL_MODE_DECAY;
    laser_y_PID.fIntegral = 0;
    laser_y_PID.fPreviousError = 0;
}


/**
 * @brief 三角函数缓存结构体
 */
typedef struct {
    s16 cached_yaw;
    float cos_value;
    float sin_value;
    uint8_t valid;
} trig_cache_t;

static trig_cache_t yaw_cache = {0, 1.0f, 0.0f, 0};

/**
 * @brief 获取缓存的三角函数值
 * @param yaw 角度值
 * @param cos_val 余弦值输出指针
 * @param sin_val 正弦值输出指针
 */
static inline void get_cached_trig(s16 yaw, float* cos_val, float* sin_val)
{
    if (!yaw_cache.valid || yaw_cache.cached_yaw != yaw) {
        yaw_cache.cos_value = my_cos_deg(yaw);
        yaw_cache.sin_value = my_sin_deg(yaw);
        yaw_cache.cached_yaw = yaw;
        yaw_cache.valid = 1;
    }

    *cos_val = yaw_cache.cos_value;
    *sin_val = yaw_cache.sin_value;
}

/*******************************************************
	函数名称：OutLoop_Control_height
	输  入:
	输  出:
	功能说明：高度环PID
 不使用直接传参的原因是： 目标值可能变化的地方需要调用
********************************************************/
void OutLoop_Control_Z(void)
{
    if(flag_Control[2] == 1) {
        error_pos[2] = target_pos[2] - g_tfmini_sensor.distance_cm; //目标高度和当前高度差

        PID_V[2] = PID_Calc(&Z_PID, error_pos[2]);	//获取飞机机体Z轴向PID速度(厘米/秒)
    } else {
        error_pos[2] = 0; //目标高度和当前高度差
        PID_V[2] = 0;	//获取飞机机体Z轴向PID速度(厘米/秒)
    }
}

/*******************************************************
	函数名称：OutLoop_Control_XY
	输  入:
	输  出:
	功能说明：外环位置-机体速度控制 (支持独立控制)
	控制模式：
	- 同时控制XY轴：flag_Control[0] == 1 && flag_Control[1] == 1
	- 仅控制X轴：flag_Control[0] == 1 && flag_Control[1] == 0
	- 仅控制Y轴：flag_Control[0] == 0 && flag_Control[1] == 1
	- 全部关闭：flag_Control[0] == 0 && flag_Control[1] == 0
********************************************************/
void OutLoop_Control_XY(void)
{
    // 检查是否有任何轴需要控制
    uint8_t x_enable = flag_Control[0];
    uint8_t y_enable = flag_Control[1];

    if (x_enable || y_enable) {
        // 缓存传感器数据，减少重复访问
        s16 pose_x = mid360.pose_x_cm;
        s16 pose_y = mid360.pose_y_cm;
        s16 pose_yaw = mid360.pose_yaw;

        // 计算世界坐标系位置误差（仅计算启用轴）
        if (x_enable) {
            error_pos[0] = target_pos[0] - pose_x;
        } else {
            error_pos[0] = 0;  // X轴禁用时清零
        }

        if (y_enable) {
            error_pos[1] = target_pos[1] - pose_y;
        } else {
            error_pos[1] = 0;  // Y轴禁用时清零
        }

        // 获取缓存的三角函数值（只计算一次，两轴共用）
        float cos_yaw, sin_yaw;
        get_cached_trig(pose_yaw, &cos_yaw, &sin_yaw);

        // 坐标系变换：世界坐标系 -> 机体坐标系
        // 注意：即使某轴禁用，也需要参与坐标变换计算，因为XY轴是耦合的
        error_body[0] = error_pos[0] * cos_yaw + error_pos[1] * sin_yaw;
        error_body[1] = -error_pos[0] * sin_yaw + error_pos[1] * cos_yaw;

        // 独立的PID控制（仅对启用轴进行控制）
        if (x_enable) {
            PID_V[0] = PID_Calc(&X_PID, error_body[0]);
        } else {
            PID_V[0] = 0;  // X轴禁用时PID输出清零
        }

        if (y_enable) {
            PID_V[1] = PID_Calc(&Y_PID, error_body[1]);
        } else {
            PID_V[1] = 0;  // Y轴禁用时PID输出清零
        }

    } else {
        // 全部轴都禁用时，快速清零所有相关变量
        error_pos[0] = 0;
        error_pos[1] = 0;
        error_body[0] = 0;
        error_body[1] = 0;
        PID_V[0] = 0;
        PID_V[1] = 0;
    }
}

/*******************************************************
	函数名称：OutLoop_Control_yaw
	输  入:
	输  出:
	功能说明：外环位置-机体角速度控制
********************************************************/
void OutLoop_Control_yaw(void)
{
    if(flag_Control[3] == 1) {
        // 计算角度误差，处理角度环绕问题（-180°和+180°是同一个角度）
        // 使用range_to_180deg宏确保误差在[-180°, +180°]范围内，选择最短路径
        error_pos[3] = range_to_180deg(target_pos[3] - mid360.pose_yaw);

        PID_V[3] = PID_Calc(&Yaw_PID, error_pos[3]);	//获取飞机机体YAW的PID角速度(rad/s)
    } else {
        yaw_turn_flag = 0;
        error_pos[3] = 0;
        PID_V[3] = 0;	//获取飞机机体YAW的PID角速度(rad/s)
    }
}

/*******************************************************
	函数名称：is_visual_data_valid
	输  入: 无
	输  出: bool - true:数据有效, false:数据无效
	功能说明：检查视觉数据的有效性
********************************************************/
bool is_visual_data_valid(void)
{
    // 1. 检查数据时效性
    if((GetSysRunTimeMs() - maixcam.last_update_ms) > VISUAL_DATA_TIMEOUT_MS) {
        return false;
    }

    // 2. 检查坐标范围
    if(maixcam.x < 0 || maixcam.x > 340 || maixcam.y < 0 || maixcam.y > 240) {
        return false;
    }

    // 3. 检查数据有效标志
    if(maixcam.data_valid == 0) {
        return false;
    }

    return true;
}

/*******************************************************
	函数名称：handle_visual_data_loss
	输  入: 无
	输  出: 无
	功能说明：处理视觉数据丢失情况
********************************************************/
void handle_visual_data_loss(void)
{
    static u16 data_loss_counter = 0;

    data_loss_counter++;

    if(data_loss_counter > 50) {  // 500ms无有效数据
        // 进入安全模式：保持当前位置
        target_pos[0] = mid360.pose_x_cm;
        target_pos[1] = mid360.pose_y_cm;

        // 重置偏移量
        visual_offset_x = 0;
        visual_offset_y = 0;

        // 重置计数器
        data_loss_counter = 0;
    }
}

/*******************************************************
	函数名称：limit_visual_offset_values
	输  入: 无
	输  出: 无
	功能说明：限制视觉偏移量数值，防止PID输出过大
********************************************************/
void limit_visual_offset_values(void)
{
    // 限制单次偏移量，防止位置跳变
    visual_offset_x = LIMIT(visual_offset_x, -VISUAL_MAX_OFFSET_CM, VISUAL_MAX_OFFSET_CM);
    visual_offset_y = LIMIT(visual_offset_y, -VISUAL_MAX_OFFSET_CM, VISUAL_MAX_OFFSET_CM);
}

/*******************************************************
	函数名称：limit_target_position
	输  入: 无
	输  出: 无
	功能说明：限制目标位置的合理范围，确保飞行安全
********************************************************/
void limit_target_position(void)
{
    // 限制目标位置的合理范围（基于当前位置的合理偏移）

    target_pos[0] = LIMIT(target_pos[0], target_pos[0] - 100, target_pos[0] + 100);
    target_pos[1] = LIMIT(target_pos[1], target_pos[1] - 100, target_pos[1] + 100);
}

/*******************************************************
	函数名称：limit_visual_offset (保持向后兼容)
	输  入: 无
	输  出: 无
	功能说明：限制视觉偏移量，防止位置跳变（兼容性函数）
********************************************************/
void limit_visual_offset(void)
{
    // 为了向后兼容，保留此函数，但建议使用拆分后的函数
    limit_visual_offset_values();
    limit_target_position();
}

/*******************************************************
	函数名称：OutLoop_Control_cam
	输  入:
	输  出:
	功能说明：摄像头跟踪控制
********************************************************/
void OutLoop_Control_cam(void)
{
    // 保持原有的直接速度控制模式（向后兼容）
    if(flag_cam == 1) {
        error_cam[0] = target_cam[0] - maixcam.x;  //屏幕分辨率  320 240
        error_cam[1] = target_cam[1] - maixcam.y;

        cam_V[0] = -PID_Calc(&cam_PID, error_cam[0]);
        cam_V[1] = -PID_Calc(&cam_PID, error_cam[1]);
        rt_tar.st_data.vel_x = cam_V[0];
        rt_tar.st_data.vel_y = cam_V[1];
    } else {
        error_cam[0] = 0;  //屏幕分辨率  320 240
        error_cam[1] = 0;
        cam_V[0] = 0;
        cam_V[1] = 0;
    }
}

/*******************************************************
	函数名称：Enhanced_Visual_Control
	输  入: 无
	输  出: 无
	功能说明：改进的视觉PID控制函数（位置环参考架构）
	控制模式：位置环参考架构
	调用频率：100Hz（与其他PID控制函数同频）
********************************************************/
void Enhanced_Visual_Control(void)
{
    if(flag_visual_pid == 1 && maixcam.data_valid == 1) {
        // ========== 第一阶段：数据有效性检查 ==========
        if(!is_visual_data_valid()) {
            handle_visual_data_loss();
            return;
        }

        // ========== 第二阶段：像素误差计算 ==========
        error_cam[0] = target_cam[0] - maixcam.x;  // X轴像素误差
        error_cam[1] = target_cam[1] - maixcam.y;  // Y轴像素误差

        // ========== 第三阶段：PID控制计算 ==========
        // 使用现有cam_PID结构体，输出单位：厘米
        visual_offset_x = PID_Calc(&cam_PID, error_cam[0]);
        visual_offset_y = PID_Calc(&cam_PID, error_cam[1]);

        // ========== 第四阶段：偏移量数值限制 ==========
        // 限制PID输出的偏移量，防止单次调整过大
        limit_visual_offset_values();

        // ========== 第五阶段：位置环参考更新 ==========
        // 关键创新：基于当前目标位置进行视觉校准
        target_pos[0] += visual_offset_x;
        target_pos[1] += visual_offset_y;

        // ========== 第六阶段：目标位置安全限制 ==========
        // 基于当前计算的target_pos进行最终安全检查
        limit_target_position();

        // ========== 第七阶段：位置环激活 ==========
        // 确保位置环处于激活状态
        if(flag_Control[0] == 0 || flag_Control[1] == 0) {
            XY_flag_Control(1, 1);
        }

        // 更新视觉数据时间戳
        visual_data_last_update_ms = GetSysRunTimeMs();

    } else {
        // ========== 禁用状态处理 ==========
        visual_offset_x = 0;
        visual_offset_y = 0;
        // 注意：不主动关闭位置环，避免影响其他控制模式
    }
}

/*******************************************************
	函数名称：visual_pid_flag_Control
	输  入: flag - 1开启 0关闭
	输  出: 无
	功能说明：视觉PID控制开关
********************************************************/
void visual_pid_flag_Control(u8 flag)
{
    if(flag == 1) {
        flag_visual_pid = 1;
        // 禁用直接的摄像头速度控制，避免冲突
        flag_cam = 0;
    } else {
        flag_visual_pid = 0;
        // 关闭时重置偏移量
        visual_offset_x = 0;
        visual_offset_y = 0;
    }
}




/*******************************************************
函数名称：OutLoop_Control_g_port	输  入: 无
	输  出: 无
	功能说明：统一的云台控制函数 - 支持两种混合模式
    模式1：云台位置控制 (flag_g_port=1)
    模式2：激光跟踪控制 (flag_laser=1)
    注意：两种模式互斥，优先级：云台位置控制 > 激光跟踪控制
		功能说明：云台位置控制 - 将像素误差直接转换为云台角度调整
		控制思路：通过PID控制器将像素误差转换为云台角度调整
		不需要知道确切的目标角度，只需要知道误差大小
********************************************************/
void OutLoop_Control_g_port(void)
{
    if(flag_g_port == 1) {
        // ========== 模式1：云台绝对位置控制 ==========
        // 1. 计算云台角度误差
        error_g_port[0] = target_g_port[0] - gimbal_angle_data.hall_yaw;  
        error_g_port[1] = target_g_port[1] - gimbal_angle_data.hall_pitch;
        

        // 2. 云台PID控制
        g_port_V[0] = (s16)PID_Calc(&g_port_PID, error_g_port[0]);
        g_port_V[1] = (s16)PID_Calc(&g_port_PID, error_g_port[1]);		
        
				
        // 3. 最终控制：目标角度 + 云台PID调整
        s16 final_yaw = target_g_port[0] + g_port_V[0];
        s16 final_pitch = target_g_port[1] + g_port_V[1];
        
        // 4. 调用云台控制函数 (转换为度并发送)
        GimbalAngleControl(final_yaw/100.0f, final_pitch/100.0f, 0);
        
        // 5. 清零激光相关变量（确保模式切换时状态清晰）
        error_laser[0] = 0;
        error_laser[1] = 0;
        laser_V[0] = 0;
        laser_V[1] = 0;
        
    } else if(flag_laser == 1) {
        // ========== 模式2：激光跟踪控制（目标位置累积策略） ==========
        // 目标位置累积策略：使用全局变量维护累积目标位置，便于串口调试和上位机监控
        // 全局变量：target_yaw_accumulated, target_pitch_accumulated, target_initialized
        // 全局变量：filtered_yaw, filtered_pitch, filter_initialized

        // 低通滤波器系数（局部常量）- X轴和Y轴独立配置
        // 方案选择：采用方案B（独立滤波器），实现X轴和Y轴完全独立的滤波配置
        // 优势：可针对不同轴的机械特性和控制需求进行精细调优
        static float filter_alpha_x = LASER_X_FILTER_ALPHA;  // X轴滤波系数（0-1），越大滤波效果越强
        static float filter_alpha_y = LASER_Y_FILTER_ALPHA;  // Y轴滤波系数（0-1），越大滤波效果越强

        // 1. 首次启动初始化：以当前云台位置为初始目标位置
        // 检查复位请求
        if (laser_reset_request) {
            target_initialized = 0;
            filter_initialized = 0;  // 同时复位滤波器状态
            laser_reset_request = 0;
        }

        if (!target_initialized) {
            // 数据有效性检查：确保hall角度数据在合理范围内
            if (ABS(gimbal_angle_data.hall_yaw) <= 3000 && ABS(gimbal_angle_data.hall_pitch) <= 3000) {
                target_yaw_accumulated = gimbal_angle_data.hall_yaw;
                target_pitch_accumulated = gimbal_angle_data.hall_pitch;
                target_initialized = 1;
            } else {
                // 如果hall数据异常，使用安全的默认位置（中心位置）
                target_yaw_accumulated = 0;
                target_pitch_accumulated = 0;
                target_initialized = 1;
            }
        }

        // 2. 计算像素误差（目标位置 - 当前激光位置）
        // 激光像素数据范围检查：确保数据在摄像头分辨率范围内
        if (maixcam.x >= 0 && maixcam.x <= 340 && maixcam.y >= 0 && maixcam.y <= 240) {
            error_laser[0] = target_laser_pixel[0] - maixcam.x;  // X轴像素误差
            error_laser[1] = target_laser_pixel[1] - maixcam.y;  // Y轴像素误差
        } else {
            // 如果激光像素数据超出范围，保持上次的误差值，避免异常跳变
            // error_laser保持不变，等待有效数据
        }

        // 3. PID控制计算角度调整量
        // 输出单位：0.01度 (即输出100表示1度)
        laser_V[0] = (s16)PID_Calc(&laser_x_PID, error_laser[0]);
        laser_V[1] = (s16)PID_Calc(&laser_y_PID, error_laser[1]);


        // 4. 累积调整目标位置（核心创新：避免大数值+小数值问题）
        // 保存调整前的位置用于异常检测
        s16 prev_yaw = target_yaw_accumulated;
        s16 prev_pitch = target_pitch_accumulated;

        target_yaw_accumulated += laser_V[0];
        target_pitch_accumulated += laser_V[1];

        // 异常跳变检测：如果单次调整幅度过大，可能存在异常
        if (ABS(target_yaw_accumulated - prev_yaw) > 500 || ABS(target_pitch_accumulated - prev_pitch) > 500) {
            // 单次调整超过5度，可能存在异常，限制调整幅度
            target_yaw_accumulated = prev_yaw + (laser_V[0] > 0 ? 500 : -500);
            target_pitch_accumulated = prev_pitch + (laser_V[1] > 0 ? 500 : -500);
        }

        // 5. 限制累积目标位置范围 (根据云台规格调整)
				target_yaw_accumulated =  LIMIT(target_yaw_accumulated, -3000, 3000); // 限制yaw ±30度
				target_pitch_accumulated =  LIMIT(target_yaw_accumulated, -3000, 3000);  // 限制pitch ±30度
				
        // 6. 低通滤波器处理：减少高频抖动，提高系统稳定性
        // 滤波器初始化：首次运行时使用当前累积位置作为滤波器初值
        if (!filter_initialized) {
            filtered_yaw = (float)target_yaw_accumulated;
            filtered_pitch = (float)target_pitch_accumulated;
            filter_initialized = 1;
        }

        // 一阶低通滤波器：filtered = α * filtered_prev + (1-α) * input
        // α越大滤波效果越强但响应越慢，X轴和Y轴可独立调整滤波系数
        filtered_yaw = filter_alpha_x * filtered_yaw + (1.0f - filter_alpha_x) * (float)target_yaw_accumulated;
        filtered_pitch = filter_alpha_y * filtered_pitch + (1.0f - filter_alpha_y) * (float)target_pitch_accumulated;

        // 9. 发送滤波后的目标位置给云台
        GimbalAngleControl(filtered_yaw/100.0f, filtered_pitch/100.0f, 0);
				
        // 10. 清零云台PID相关变量（确保模式切换时状态清晰）
        error_g_port[0] = 0;
        error_g_port[1] = 0;
        g_port_V[0] = 0;
        g_port_V[1] = 0;
        
    } else {
        // ========== 无控制模式：清零所有变量 ==========
        error_laser[0] = 0;
        error_laser[1] = 0;
        laser_V[0] = 0;
        laser_V[1] = 0;
        error_g_port[0] = 0;
        error_g_port[1] = 0;
        g_port_V[0] = 0;
        g_port_V[1] = 0;

        // 设置激光控制状态复位请求，下次启动激光控制时将重新初始化
        laser_reset_request = 1;
    }
}

/*******************************************************
	函数名称：XY_flag_Control
	输  入: flag1 - X轴控制标志 (1开启 0关闭)
	       flag2 - Y轴控制标志 (1开启 0关闭)
	输  出:
	功能说明：独立设置X轴Y轴位置控制标志
	控制模式：
	- XY_flag_Control(1, 1): 同时控制XY轴
	- XY_flag_Control(1, 0): 仅控制X轴
	- XY_flag_Control(0, 1): 仅控制Y轴
	- XY_flag_Control(0, 0): 全部关闭
********************************************************/
void XY_flag_Control(u8 flag1, u8 flag2)
{
    flag_Control[0] = flag1;  // X轴控制标志
    flag_Control[1] = flag2;  // Y轴控制标志
}

/*******************************************************
	YAW_flag_Control
	输  入: 1 开 0关
	输  出:
	功能说明：开启转向环
********************************************************/
void YAW_flag_Control(u8 flag)
{
    if(flag == 1) {
        flag_Control[3] = 1;
    } else {
        flag_Control[3] = 0;
    }
}

/*******************************************************
	Z_flag_Control
	输  入: 1 开 0关
	输  出:
	功能说明：开启高度环
********************************************************/
void Z_flag_Control(u8 flag)
{
    if(flag == 1) {
        flag_Control[2] = 1;
    } else {
        flag_Control[2] = 0;
    }
}

/*******************************************************
	cam_flag_Control
	输  入: 1 开 0关
	输  出:
	功能说明：开启相机跟踪环
********************************************************/
void cam_flag_Control(u8 flag)
{
    if(flag == 1) {
        flag_cam = 1;
    } else {
        flag_cam = 0;
    }
}

/*******************************************************
	g_port_flag_Control
	输  入: 1 开 0关
	输  出:
	功能说明：开启云台跟踪环
********************************************************/
void g_port_flag_Control(u8 flag)
{
    if(flag == 1) {
        flag_g_port = 1;
    } else {
        flag_g_port = 0;
    }
}

/*******************************************************
	laser_flag_Control
	输  入: 1 开 0关
	输  出:
	功能说明：开启激光控制环
********************************************************/
void laser_flag_Control(u8 flag)
{
    if(flag == 1) {
        flag_laser = 1;
    } else {
        flag_laser = 0;
        // 关闭激光控制时清零角度调整量
        laser_V[0] = 0;
        laser_V[1] = 0;
    }
}

/*******************************************************
    函数名：Laser_Set_Target_Pixel
    输  入: target_x - 目标X像素位置 (0-640)
           target_y - 目标Y像素位置 (0-480)
    输  出: 无
    功能说明：设置激光目标像素位置
********************************************************/
void Laser_Set_Target_Pixel(s16 target_x, s16 target_y)
{
    // 限制目标位置在有效范围内
    if(target_x < 0) target_x = 0;
    if(target_x > 640) target_x = 640;
    if(target_y < 0) target_y = 0;
    if(target_y > 480) target_y = 480;
    
    target_laser_pixel[0] = target_x;
    target_laser_pixel[1] = target_y;
}

/*******************************************************
    函数名：Laser_Set_Target_Center
    输  入: 无
    输  出: 无
    功能说明：设置激光目标到屏幕中心
********************************************************/
void Laser_Set_Target_Center(void)
{
    target_laser_pixel[0] = 170;  // 340/2
    target_laser_pixel[1] = 120;  // 240/2
}

/*******************************************************
    函数名：Laser_Get_Pixel_Error_X
    输  入: 无
    输  出: X轴像素误差
    功能说明：获取激光X轴像素误差
********************************************************/
s16 Laser_Get_Pixel_Error_X(void)
{
    return error_laser[0];
}

/*******************************************************
    函数名：Laser_Get_Pixel_Error_Y
    输  入: 无
    输  出: Y轴像素误差
    功能说明：获取激光Y轴像素误差
********************************************************/
s16 Laser_Get_Pixel_Error_Y(void)
{
    return error_laser[1];
}

/*******************************************************
    函数名：Laser_Is_On_Target
    输  入: tolerance - 允许的像素误差容忍度
    输  出: 1-到达目标，0-未到达目标
    功能说明：判断激光是否到达目标位置
********************************************************/
u8 Laser_Is_On_Target(s16 tolerance)
{
    if(ABS(error_laser[0]) <= tolerance && ABS(error_laser[1]) <= tolerance) {
        return 1;  // 到达目标
    }
    return 0;  // 未到达目标
}

/*******************************************************
	函数名称：all_flag_reset
	输  入:
	输  出:
	功能说明：复位所有控制器状态
********************************************************/
void all_flag_reset(void)
{
    // 清零误差值
    error_pos[0] = 0;
    error_pos[1] = 0;
    error_pos[2] = 0;
    error_pos[3] = 0;
    error_cam[0] = 0;
    error_cam[1] = 0;
    error_g_port[0] = 0;  
    error_g_port[1] = 0;
    error_laser[0] = 0;
    error_laser[1] = 0;
	  target_pos[0] = 0;
		target_pos[1] = 0;
	  target_pos[2] = 0;
	  target_pos[3] = 0;

    
    // 清零控制输出
    PID_V[0] = 0;
    PID_V[1] = 0;
    PID_V[2] = 0;
    PID_V[3] = 0;
    cam_V[0] = 0;
    cam_V[1] = 0;
    g_port_V[0] = 0;
    g_port_V[1] = 0;
    laser_V[0] = 0;
    laser_V[1] = 0;
    
    // 清零飞行控制输出
    rt_tar.st_data.vel_x = 0;
    rt_tar.st_data.vel_y = 0;
    rt_tar.st_data.vel_z = 0;
    rt_tar.st_data.yaw_dps = 0;
    
    // 关闭所有控制标志
    flag_Control[0] = 0;
    flag_Control[1] = 0;
    flag_Control[2] = 0;
    flag_Control[3] = 0;
    flag_cam = 0;
    yaw_turn_flag = 0;
    flag_g_port = 0;
    flag_laser = 0;
    flag_visual_pid = 0;  // 关闭视觉PID控制
    
    // 重置所有PID状态
    X_PID.fIntegral = 0;
    X_PID.fPreviousError = 0;
    Y_PID.fIntegral = 0;
    Y_PID.fPreviousError = 0;
    Z_PID.fIntegral = 0;
    Z_PID.fPreviousError = 0;
    Yaw_PID.fIntegral = 0;
    Yaw_PID.fPreviousError = 0;
    cam_PID.fIntegral = 0;
    cam_PID.fPreviousError = 0;
    g_port_PID.fIntegral = 0;
    g_port_PID.fPreviousError = 0;
    laser_x_PID.fIntegral = 0;
    laser_x_PID.fPreviousError = 0;
    laser_y_PID.fIntegral = 0;
    laser_y_PID.fPreviousError = 0;

    // 重置视觉控制相关变量
    visual_offset_x = 0;
    visual_offset_y = 0;
    visual_data_last_update_ms = 0;
}


