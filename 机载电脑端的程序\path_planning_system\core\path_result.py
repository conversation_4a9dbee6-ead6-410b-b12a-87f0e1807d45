#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径规划结果数据结构
版权信息：米醋电子工作室
创建日期：2025-07-30

功能描述：
封装路径规划算法的结果，包含路径序列、统计信息和性能指标。
提供标准化的结果格式，便于算法对比和数据传输。
"""

import time
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass

@dataclass
class PathStatistics:
    """路径统计信息"""
    total_length: float = 0.0          # 总路径长度
    total_points: int = 0              # 路径点数量
    computation_time_ms: float = 0.0   # 计算时间(毫秒)
    algorithm_type: str = ""           # 使用的算法类型
    memory_usage_mb: float = 0.0       # 内存使用量(MB)
    success: bool = False              # 规划是否成功

class PathResult:
    """
    路径规划结果封装类
    
    提供统一的结果格式，支持多种算法的结果对比和分析。
    基于NumPy优化，提供高效的数据处理能力。
    """
    
    def __init__(self, algorithm_name: str = "Unknown"):
        """
        初始化路径结果
        
        参数:
            algorithm_name: 算法名称
        """
        self.algorithm_name = algorithm_name
        self.path_sequence: List[int] = []  # position_code序列
        self.grid_path: List[Tuple[int, int]] = []  # 网格坐标路径
        self.statistics = PathStatistics()
        
        # 性能优化：预分配NumPy缓冲区
        self._path_buffer = np.zeros(63, dtype=np.uint16)  # 最大63个点
        self._coord_buffer = np.zeros((63, 2), dtype=np.int32)
        
        # 计时器
        self._start_time = None
        self._end_time = None
    
    def start_timing(self):
        """开始计时"""
        self._start_time = time.perf_counter()
    
    def end_timing(self):
        """结束计时并记录"""
        if self._start_time is not None:
            self._end_time = time.perf_counter()
            self.statistics.computation_time_ms = (self._end_time - self._start_time) * 1000.0
    
    def set_path_sequence(self, path_sequence: List[int]):
        """
        设置路径序列
        
        参数:
            path_sequence: position_code序列
        """
        self.path_sequence = path_sequence.copy()
        self.statistics.total_points = len(path_sequence)
        self.statistics.algorithm_type = self.algorithm_name
        
        # 更新成功状态
        self.statistics.success = len(path_sequence) > 0
    
    def set_grid_path(self, grid_path: List[Tuple[int, int]]):
        """
        设置网格坐标路径
        
        参数:
            grid_path: 网格坐标序列
        """
        self.grid_path = grid_path.copy()
        
        # 计算路径长度
        if len(grid_path) > 1:
            total_length = 0.0
            for i in range(len(grid_path) - 1):
                dx = grid_path[i+1][0] - grid_path[i][0]
                dy = grid_path[i+1][1] - grid_path[i][1]
                total_length += np.sqrt(dx * dx + dy * dy)
            self.statistics.total_length = total_length
    
    def calculate_path_length(self) -> float:
        """
        计算路径总长度
        
        返回:
            float: 路径长度
        """
        if len(self.grid_path) < 2:
            return 0.0
        
        # 使用NumPy向量化计算提升性能
        path_array = np.array(self.grid_path)
        diff = np.diff(path_array, axis=0)
        distances = np.sqrt(np.sum(diff ** 2, axis=1))
        return np.sum(distances)
    
    def get_path_as_numpy(self) -> np.ndarray:
        """
        获取路径的NumPy数组表示
        
        返回:
            np.ndarray: 路径数组 [N, 2]
        """
        if not self.grid_path:
            return np.empty((0, 2), dtype=np.int32)
        
        return np.array(self.grid_path, dtype=np.int32)
    
    def get_position_codes_as_numpy(self) -> np.ndarray:
        """
        获取position_code序列的NumPy数组
        
        返回:
            np.ndarray: position_code数组
        """
        if not self.path_sequence:
            return np.empty(0, dtype=np.uint16)
        
        return np.array(self.path_sequence, dtype=np.uint16)
    
    def is_valid(self) -> bool:
        """检查结果是否有效"""
        return (self.statistics.success and 
                len(self.path_sequence) > 0 and 
                len(self.grid_path) > 0)
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取结果摘要
        
        返回:
            Dict: 结果摘要信息
        """
        return {
            'algorithm': self.algorithm_name,
            'success': self.statistics.success,
            'path_length': self.statistics.total_length,
            'point_count': self.statistics.total_points,
            'computation_time_ms': self.statistics.computation_time_ms,
            'memory_usage_mb': self.statistics.memory_usage_mb,
            'path_sequence': self.path_sequence.copy(),
            'efficiency_score': self._calculate_efficiency_score()
        }
    
    def _calculate_efficiency_score(self) -> float:
        """
        计算效率评分
        
        返回:
            float: 效率评分 (0-100)
        """
        if not self.statistics.success:
            return 0.0
        
        # 基于路径长度和计算时间的综合评分
        length_score = max(0, 100 - self.statistics.total_length * 2)  # 路径越短分数越高
        time_score = max(0, 100 - self.statistics.computation_time_ms / 10)  # 时间越短分数越高
        
        return (length_score + time_score) / 2
    
    def compare_with(self, other: 'PathResult') -> Dict[str, Any]:
        """
        与另一个结果进行对比
        
        参数:
            other: 另一个PathResult对象
            
        返回:
            Dict: 对比结果
        """
        if not isinstance(other, PathResult):
            raise TypeError("对比对象必须是PathResult类型")
        
        return {
            'length_diff': self.statistics.total_length - other.statistics.total_length,
            'time_diff': self.statistics.computation_time_ms - other.statistics.computation_time_ms,
            'point_diff': self.statistics.total_points - other.statistics.total_points,
            'better_length': self.statistics.total_length < other.statistics.total_length,
            'better_time': self.statistics.computation_time_ms < other.statistics.computation_time_ms,
            'overall_better': self._calculate_efficiency_score() > other._calculate_efficiency_score()
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"PathResult({self.algorithm_name}): "
                f"Success={self.statistics.success}, "
                f"Points={self.statistics.total_points}, "
                f"Length={self.statistics.total_length:.2f}, "
                f"Time={self.statistics.computation_time_ms:.2f}ms")
