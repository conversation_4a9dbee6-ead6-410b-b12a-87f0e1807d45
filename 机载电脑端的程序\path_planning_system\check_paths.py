#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查看路径50、52、90、92的数据
"""

import json

def check_specific_paths():
    """检查特定路径的数据"""
    
    # 加载安全修正后的数据
    with open('safe_optimized_return_paths.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    path_data = data['path_data']
    
    # 要检查的路径索引 (路径编号-1)
    paths_to_check = [49, 51, 89, 91]  # 路径50, 52, 90, 92
    path_names = ['路径50', '路径52', '路径90', '路径92']
    
    for i, path_idx in enumerate(paths_to_check):
        if path_idx < len(path_data):
            path = path_data[path_idx]
            print(f"\n=== {path_names[i]} (索引{path_idx}) ===")
            print(f"禁飞区: {path['no_fly_zones']}")
            print(f"巡查路径长度: {path['patrol_path_length']}")
            print(f"巡查路径: {path['patrol_path_sequence']}")
            print(f"返航路径长度: {path['return_path_length']}")
            print(f"返航路径: {path['return_path_sequence']}")
            print(f"总飞行时间: {path['total_flight_time']:.1f}秒")
            print(f"覆盖率: {path['coverage_rate']}%")
            print(f"有效性: {path['is_valid']}")
        else:
            print(f"\n=== {path_names[i]} ===")
            print("路径不存在")

if __name__ == "__main__":
    check_specific_paths()
