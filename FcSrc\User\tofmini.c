/*==========================================================================
 * 版权    ：米醋电子工作室
 * 文件名  ：tofmini.c
 * 传感器  ：TFmini单点激光雷达传感器驱动
 * 创建时间：2024年
 * 作者    ：Alex (工程师)
 * 功能描述：TFmini单点激光传感器驱动实现
 *
 * 【传感器特性说明】
 * - 传感器类型：单点激光雷达（仅支持单点测距）
 * - 测距范围：0-1200cm，标准精度测距
 * - 通信方式：被动接收模式，连续输出数据
 * - 数据更新：连续输出，无需查询指令
 * - 传感器数量：单传感器设计
 * - 滤波算法：支持移动平均、中位数、最小值滤波
 * - 协议格式：9字节简单帧结构
 *
 * 【适用场景】
 * - 简单的定高控制
 * - 基础避障功能
 * - 单点测距应用
 * - 对功耗和成本敏感的场合
 * - 快速集成和简单配置需求
 *
 * 【与Tofsense-M传感器的主要差异】
 * 1. 架构差异：
 *    - TFmini：单传感器结构，仅支持单点测距
 *    - Tofsense-M：多传感器数组结构，支持双传感器
 *
 * 2. 通信协议：
 *    - TFmini：9字节简单帧，被动接收模式
 *    - Tofsense-M：57字节复杂帧，主动查询模式
 *
 * 3. 功能特性：
 *    - TFmini：单点测距、简化滤波、单传感器设计
 *    - Tofsense-M：多像素阵列、复杂滤波、双传感器配置
 *
 * 4. 性能特点：
 *    - TFmini：简单易用、功耗低、配置简单、成本低
 *    - Tofsense-M：高精度、多功能、配置复杂、成本高
 *
 * 【如果要切换到TFmini传感器，需要修改以下位置】
 *
 * 1. 工程文件配置（Keil工程）：
 *    - 在Keil工程中包含 tofmini.c 文件
 *    - 排除或删除 Tofsense-m.c 文件
 *    - 确保 tofmini.h 被正确包含
 *
 * 2. 头文件包含修改（LX_ExtSensor.c）：
 *    - 将 #include "Tofsense-m.h" 改为 #include "tofmini.h"
 *
 * 3. 函数调用修改（LX_ExtSensor.c）：
 *    - 将 tof_is_distance_valid(0) 改为 tofmini_is_distance_valid()
 *    - 将 tof_get_distance_cm(0) 改为 tofmini_get_distance_cm()
 *    - 注意：TFmini无需sensor_id参数，直接调用即可
 *
 * 4. 初始化代码修改（User_Task.c或main.c）：
 *    - 将 tof_init() 改为 tofmini_init()
 *    - 移除 tof_update() 调用（TFmini被动接收，无需查询）
 *
 * 5. 状态检查修改：
 *    - 将 tof_check_state(0.001f) 改为 tofmini_check_state(0.001f)
 *
 * 6. 字节接收修改（串口中断）：
 *    - 将 TOF_RecvOneByte() 改为 TOFmini_RecvOneByte()
 *
 * 7. 滤波配置（如需要）：
 *    - 调用 tofmini_set_filter() 配置滤波算法
 *    - 支持 TFMINI_FILTER_MOVING_AVERAGE、TFMINI_FILTER_MEDIAN、TFMINI_FILTER_MINIMUM
 *
 * 【重要提醒】
 * - TFmini被动接收数据，无需在主循环中调用更新函数
 * - TFmini为单传感器，所有函数调用无需sensor_id参数
 * - TFmini不支持像素模式和双传感器配置
 * - 切换后需要重新编译整个工程
 * - TFmini功耗更低，适合电池供电应用
 *
 * 【协议符合性】
 * ✅ 完全符合TFmini-S协议规范
 * ✅ 正确实现9字节数据帧格式
 * ✅ 校验和计算完全正确
 * ✅ 异常值处理符合规范
===========================================================================*/

#include "tofmini.h"
#include <stddef.h>  // 包含NULL定义

/*==============================================================================
 * TFmini全局数据结构 - 单点传感器专用
 *============================================================================*/
static tfmini_parser_t g_tfmini_parser;        // 协议解析器
 tfmini_sensor_data_t g_tfmini_sensor;   // 传感器数据

/*==============================================================================
 * TFmini核心API函数实现 - 简化接口设计
 *============================================================================*/

/**
 * @brief 初始化TFmini传感器系统
 */
void tofmini_init(void)
{
    // 初始化协议解析器
    tfmini_parser_init(&g_tfmini_parser);
    
    // 初始化传感器数据
    g_tfmini_sensor.is_initialized = true;
    g_tfmini_sensor.distance_cm = 0;
    g_tfmini_sensor.is_distance_valid = false;
    g_tfmini_sensor.signal_strength = 0;
    g_tfmini_sensor.data_quality = 0;
    
    // 初始化状态检查
    g_tfmini_sensor.link_sta = 0;  // 初始状态：未连接
    g_tfmini_sensor.work_sta = 0;  // 初始状态：异常
    g_tfmini_sensor.timeout_counter_ms = 0;
    
    // 初始化滤波配置 - 仅使用移动平均滤波
    g_tfmini_sensor.filter_type = TFMINI_FILTER_MOVING_AVERAGE;
    g_tfmini_sensor.filter_index = 0;
    g_tfmini_sensor.filter_size = TFMINI_FILTER_SIZE_DEFAULT;
    for (uint8_t i = 0; i < TFMINI_FILTER_SIZE_MAX; i++)
    {
        g_tfmini_sensor.filter_buffer[i] = 0;
    }
    
    // 初始化统计信息
    g_tfmini_sensor.frame_count = 0;
    g_tfmini_sensor.error_count = 0;
}


/**
 * @brief 获取TFmini距离值
 * @return 距离值(cm)，无效时返回0
 */
uint16_t tofmini_get_distance_cm(void)
{
    return g_tfmini_sensor.distance_cm;
}

/**
 * @brief 获取TFmini距离数据有效性
 * @return true-数据有效, false-数据无效
 */
bool tofmini_is_distance_valid(void)
{
    return g_tfmini_sensor.is_distance_valid;
}

/**
 * @brief TFmini状态检查函数
 * @param dT_s 时间间隔（秒），通常为0.001f（1ms）
 */
void tofmini_check_state(float dT_s)
{
    // 避免未使用参数警告
    (void)dT_s;
    
    // 500ms超时检查机制
    if (g_tfmini_sensor.timeout_counter_ms < 500)
    {
        g_tfmini_sensor.timeout_counter_ms++;
        g_tfmini_sensor.link_sta = 1;  // 连接正常
        g_tfmini_sensor.work_sta = 1;  // 工作正常
    }
    else
    {
        // 超时处理
        g_tfmini_sensor.link_sta = 0;  // 连接超时
        g_tfmini_sensor.work_sta = 0;  // 工作异常
        g_tfmini_sensor.is_distance_valid = false;  // 数据无效
    }
}

/**
 * @brief TFmini字节接收处理函数
 * @param link_type 链路类型（兼容参数，实际忽略）
 * @param byte 接收的字节
 */
void TOFmini_RecvOneByte(uint8_t link_type, uint8_t byte)
{
    // 避免未使用参数警告
    (void)link_type;
    
    // 使用TFmini协议解析器处理字节
    if (tfmini_parse_byte(&g_tfmini_parser, byte))
    {
        // 完整帧解析完成，处理数据
        tfmini_process_frame(&g_tfmini_parser.frame_data);
    }
}

/*==============================================================================
 * TFmini配置函数实现
 *============================================================================*/

/**
 * @brief 设置TFmini滤波算法（简化版，仅支持移动平均）
 * @param filter_algorithm 滤波算法类型（仅支持TFMINI_FILTER_MOVING_AVERAGE）
 * @param temporal_filter_points 时域滤波点数(1-8)
 */
void tofmini_set_filter(tfmini_filter_algorithm_t filter_algorithm, uint8_t temporal_filter_points)
{
    // 参数有效性检查
    if (temporal_filter_points == 0 || temporal_filter_points > TFMINI_FILTER_SIZE_MAX) 
        return;
    
    // 单点传感器只支持移动平均滤波
    g_tfmini_sensor.filter_type = TFMINI_FILTER_MOVING_AVERAGE;
    g_tfmini_sensor.filter_size = temporal_filter_points;
    
    // 重置滤波缓冲区
    g_tfmini_sensor.filter_index = 0;
    for (uint8_t i = 0; i < TFMINI_FILTER_SIZE_MAX; i++)
    {
        g_tfmini_sensor.filter_buffer[i] = 0;
    }
    
    // 避免未使用参数警告
    (void)filter_algorithm;
}

/**
 * @brief 获取TFmini传感器状态信息
 * @return 传感器数据结构指针
 */
const tfmini_sensor_data_t* tofmini_get_sensor_info(void)
{
    return &g_tfmini_sensor;
}

/*==============================================================================
 * TFmini内部处理函数实现
 *============================================================================*/

/**
 * @brief 处理TFmini数据帧
 * @param frame_data TFmini数据帧指针
 */
void tfmini_process_frame(const tfmini_frame_data_t *frame_data)
{
    if (frame_data == NULL) return;
    
    // 重置超时计数器 - 表示收到有效数据
    g_tfmini_sensor.timeout_counter_ms = 0;
    
    // 转换TFmini数据
    uint16_t distance_cm = tfmini_convert_distance_cm(frame_data->distance, frame_data->strength);
    bool is_valid = tfmini_validate_frame(frame_data) && (distance_cm > 0);
    
    // 更新传感器数据
    g_tfmini_sensor.signal_strength = frame_data->strength;
    

    // 应用时域滤波
    if (is_valid)
    {
        g_tfmini_sensor.distance_cm = tfmini_temporal_filter(distance_cm);
        g_tfmini_sensor.is_distance_valid = true;
        g_tfmini_sensor.frame_count++;
    }
    else
    {
        g_tfmini_sensor.is_distance_valid = false;
        g_tfmini_sensor.error_count++;
    }
}

/**
 * @brief TFmini距离计算
 * @return 滤波后的距离(cm)
 */
uint16_t tfmini_calculate_distance(void)
{
    return g_tfmini_sensor.distance_cm;
}

/**
 * @brief TFmini时域滤波（仅移动平均）
 * @param new_value 新的距离值
 * @return 滤波后的距离值
 */
uint16_t tfmini_temporal_filter(uint16_t new_value)
{
    // 数据有效性检查
    if (new_value == 0 || new_value > TFMINI_MAX_DISTANCE)
    {
        return g_tfmini_sensor.distance_cm; // 返回上次有效值
    }
    
    // 更新滤波缓冲区
    g_tfmini_sensor.filter_buffer[g_tfmini_sensor.filter_index] = new_value;
    g_tfmini_sensor.filter_index = (g_tfmini_sensor.filter_index + 1) % g_tfmini_sensor.filter_size;
    
    // 移动平均滤波
    uint32_t sum = 0;
    for (uint8_t i = 0; i < g_tfmini_sensor.filter_size; i++)
    {
        sum += g_tfmini_sensor.filter_buffer[i];
    }
    
    return (uint16_t)(sum / g_tfmini_sensor.filter_size);
}

/*==============================================================================
 * TFmini协议解析函数实现
 *============================================================================*/

/**
 * @brief 初始化TFmini协议解析器
 * @param parser 解析器指针
 */
void tfmini_parser_init(tfmini_parser_t *parser)
{
    if (parser == NULL) return;

    // 重置状态机
    parser->state = TFMINI_STATE_WAIT_HEADER1;
    parser->data_index = 0;
    parser->calculated_checksum = 0;

    // 清空缓冲区
    for (uint8_t i = 0; i < TFMINI_FRAME_LENGTH; i++)
    {
        parser->frame_buffer[i] = 0;
    }

    // 初始化数据帧
    parser->frame_data.distance = 0;
    parser->frame_data.strength = 0;
    parser->frame_data.temperature = 0;
    parser->frame_data.checksum = 0;
    parser->frame_data.is_valid = false;

    // 初始化统计信息
    parser->frame_count = 0;
    parser->error_count = 0;
    parser->checksum_error_count = 0;
}

/**
 * @brief 计算TFmini校验和
 * @param data 数据缓冲区指针
 * @param length 数据长度
 * @return 校验和值
 */
uint8_t tfmini_calculate_checksum(const uint8_t *data, uint8_t length)
{
    uint16_t sum = 0;

    // 使用循环展开优化（8字节固定长度）
    if (length == 8)
    {
        sum = data[0] + data[1] + data[2] + data[3] +
              data[4] + data[5] + data[6] + data[7];
    }
    else
    {
        // 通用版本
        for (uint8_t i = 0; i < length; i++)
        {
            sum += data[i];
        }
    }

    return (uint8_t)(sum & 0xFF);
}

/**
 * @brief TFmini协议解析状态机 - 处理单个字节
 * @param parser 解析器指针
 * @param byte 接收的字节
 * @return true-完整帧解析完成, false-继续等待数据
 */
bool tfmini_parse_byte(tfmini_parser_t *parser, uint8_t byte)
{
    if (parser == NULL) return false;

    switch (parser->state)
    {
        case TFMINI_STATE_WAIT_HEADER1:
            if (byte == TFMINI_FRAME_HEADER1)
            {
                parser->frame_buffer[0] = byte;
                parser->data_index = 1;
                parser->state = TFMINI_STATE_WAIT_HEADER2;
            }
            break;

        case TFMINI_STATE_WAIT_HEADER2:
            if (byte == TFMINI_FRAME_HEADER2)
            {
                parser->frame_buffer[1] = byte;
                parser->data_index = 2;
                parser->state = TFMINI_STATE_DIST_LOW;
            }
            else
            {
                parser->state = TFMINI_STATE_WAIT_HEADER1;
                parser->error_count++;
            }
            break;

        case TFMINI_STATE_DIST_LOW:
            parser->frame_buffer[2] = byte;
            parser->data_index = 3;
            parser->state = TFMINI_STATE_DIST_HIGH;
            break;

        case TFMINI_STATE_DIST_HIGH:
            parser->frame_buffer[3] = byte;
            parser->data_index = 4;
            parser->state = TFMINI_STATE_STRENGTH_LOW;
            break;

        case TFMINI_STATE_STRENGTH_LOW:
            parser->frame_buffer[4] = byte;
            parser->data_index = 5;
            parser->state = TFMINI_STATE_STRENGTH_HIGH;
            break;

        case TFMINI_STATE_STRENGTH_HIGH:
            parser->frame_buffer[5] = byte;
            parser->data_index = 6;
            parser->state = TFMINI_STATE_TEMP_LOW;
            break;

        case TFMINI_STATE_TEMP_LOW:
            parser->frame_buffer[6] = byte;
            parser->data_index = 7;
            parser->state = TFMINI_STATE_TEMP_HIGH;
            break;

        case TFMINI_STATE_TEMP_HIGH:
            parser->frame_buffer[7] = byte;
            parser->data_index = 8;
            parser->state = TFMINI_STATE_CHECKSUM;
            break;

        case TFMINI_STATE_CHECKSUM:
            parser->frame_buffer[8] = byte;

            // 计算校验和
            parser->calculated_checksum = tfmini_calculate_checksum(parser->frame_buffer, 8);

            if (byte == parser->calculated_checksum)
            {
                // 校验和正确，解析数据帧
                parser->frame_data.distance = (uint16_t)((parser->frame_buffer[3] << 8) | parser->frame_buffer[2]);
                parser->frame_data.strength = (uint16_t)((parser->frame_buffer[5] << 8) | parser->frame_buffer[4]);
                parser->frame_data.temperature = (uint16_t)((parser->frame_buffer[7] << 8) | parser->frame_buffer[6]);
                parser->frame_data.checksum = byte;
                parser->frame_data.is_valid = tfmini_validate_frame(&parser->frame_data);

                parser->frame_count++;

                // 重置状态机准备下一帧
                parser->state = TFMINI_STATE_WAIT_HEADER1;
                parser->data_index = 0;

                return true; // 完整帧解析完成
            }
            else
            {
                // 校验和错误
                parser->checksum_error_count++;
                parser->error_count++;
                parser->state = TFMINI_STATE_WAIT_HEADER1;
                parser->data_index = 0;
            }
            break;

        default:
            // 未知状态，重置状态机
            parser->state = TFMINI_STATE_WAIT_HEADER1;
            parser->data_index = 0;
            parser->error_count++;
            break;
    }

    return false; // 继续等待数据
}

/**
 * @brief 验证TFmini数据帧有效性
 * @param frame_data 数据帧指针
 * @return true-数据有效, false-数据无效
 */
bool tfmini_validate_frame(const tfmini_frame_data_t *frame_data)
{
    if (frame_data == NULL) return false;

    // 检查信号强度异常
    if (frame_data->strength < TFMINI_MIN_STRENGTH ||
        frame_data->strength == TFMINI_STRENGTH_INVALID)
    {
        return false;
    }

    // 检查距离异常值
    if (frame_data->distance == TFMINI_DIST_STRENGTH_LOW ||
        frame_data->distance == TFMINI_DIST_STRENGTH_SAT ||
        frame_data->distance == TFMINI_DIST_AMBIENT_SAT)
    {
        return false;
    }

    // 检查距离范围（0-1200cm为有效范围）
    if (frame_data->distance > TFMINI_MAX_DISTANCE)
    {
        return false;
    }

    return true;
}


/**
 * @brief 转换TFmini温度值为摄氏度
 * @param raw_temperature 原始温度值
 * @return 温度值(摄氏度)
 */
int16_t tfmini_convert_temperature_celsius(uint16_t raw_temperature)
{
    // 根据TFmini规范：摄氏度 = Temp/8 - 256
    return (int16_t)(raw_temperature / 8 - 256);
}

/**
 * @brief 重置TFmini协议解析器状态
 * @param parser 解析器指针
 */
void tfmini_parser_reset(tfmini_parser_t *parser)
{
    if (parser == NULL) return;

    // 重置状态机到初始状态
    parser->state = TFMINI_STATE_WAIT_HEADER1;
    parser->data_index = 0;
    parser->calculated_checksum = 0;

    // 清空当前数据帧
    parser->frame_data.distance = 0;
    parser->frame_data.strength = 0;
    parser->frame_data.temperature = 0;
    parser->frame_data.checksum = 0;
    parser->frame_data.is_valid = false;

    // 注意：不重置统计信息，保留用于调试
}

/*==============================================================================
 * TFmini缺失函数实现补充
 *============================================================================*/

/**
 * @brief 转换TFmini距离值为厘米
 * @param raw_distance 原始距离值
 * @param strength 信号强度
 * @return 距离值(cm)，异常时返回0
 */
uint16_t tfmini_convert_distance_cm(uint16_t raw_distance, uint16_t strength)
{
    // 检查异常距离值
    if (raw_distance == TFMINI_DIST_STRENGTH_LOW ||
        raw_distance == TFMINI_DIST_STRENGTH_SAT ||
        raw_distance == TFMINI_DIST_AMBIENT_SAT)
    {
        return 0; // 异常值返回0
    }

    // 检查信号强度
    if (strength < TFMINI_MIN_STRENGTH || strength == TFMINI_STRENGTH_INVALID)
    {
        return 0; // 信号强度不足返回0
    }

    // 检查距离范围
    if (raw_distance > TFMINI_MAX_DISTANCE)
    {
        return 0; // 超出范围返回0
    }

    // TFmini输出的距离值已经是厘米单位，直接返回
    return raw_distance;
}
