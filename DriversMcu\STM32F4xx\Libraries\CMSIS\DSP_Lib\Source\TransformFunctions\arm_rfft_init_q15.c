/* ----------------------------------------------------------------------    
* Copyright (C) 2010-2014 ARM Limited. All rights reserved.    
*    
* $Date:        19. March 2015 
* $Revision: 	V.1.4.5  
*    
* Project: 	    CMSIS DSP Library    
* Title:	    arm_rfft_init_q15.c    
*    
* Description:	RFFT & RIFFT Q15 initialisation function    
*    
* Target Processor: Cortex-M4/Cortex-M3/Cortex-M0
*  
* Redistribution and use in source and binary forms, with or without 
* modification, are permitted provided that the following conditions
* are met:
*   - Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*   - Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in
*     the documentation and/or other materials provided with the 
*     distribution.
*   - Neither the name of ARM LIMITED nor the names of its contributors
*     may be used to endorse or promote products derived from this
*     software without specific prior written permission.
*
* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
* "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE 
* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.  
* -------------------------------------------------------------------- */


#include "arm_math.h"
#include "arm_common_tables.h"
#include "arm_const_structs.h"

/**    
* @ingroup groupTransforms    
*/

/**    
* @addtogroup RealFFT    
* @{    
*/



/**    
* \par    
* Generation fixed-point realCoefAQ15 array in Q15 format:    
* \par    
* n = 4096    
* <pre>for (i = 0; i < n; i++)    
*  {    
*    pATable[2 * i] = 0.5 * (1.0 - sin (2 * PI / (double) (2 * n) * (double) i));    
*    pATable[2 * i + 1] = 0.5 * (-1.0 * cos (2 * PI / (double) (2 * n) * (double) i));    
*  } </pre>    
* \par    
* Convert to fixed point Q15 format    
*       round(pATable[i] * pow(2, 15))    
*/
static const q15_t ALIGN4 realCoefAQ15[8192] = {
    0x4000, 0xc000, 0x3ff3, 0xc000, 0x3fe7, 0xc000, 0x3fda, 0xc000,
    0x3fce, 0xc000, 0x3fc1, 0xc000, 0x3fb5, 0xc000, 0x3fa8, 0xc000,
    0x3f9b, 0xc000, 0x3f8f, 0xc000, 0x3f82, 0xc000, 0x3f76, 0xc001,
    0x3f69, 0xc001, 0x3f5d, 0xc001, 0x3f50, 0xc001, 0x3f44, 0xc001,
    0x3f37, 0xc001, 0x3f2a, 0xc001, 0x3f1e, 0xc002, 0x3f11, 0xc002,
    0x3f05, 0xc002, 0x3ef8, 0xc002, 0x3eec, 0xc002, 0x3edf, 0xc003,
    0x3ed2, 0xc003, 0x3ec6, 0xc003, 0x3eb9, 0xc003, 0x3ead, 0xc004,
    0x3ea0, 0xc004, 0x3e94, 0xc004, 0x3e87, 0xc004, 0x3e7a, 0xc005,
    0x3e6e, 0xc005, 0x3e61, 0xc005, 0x3e55, 0xc006, 0x3e48, 0xc006,
    0x3e3c, 0xc006, 0x3e2f, 0xc007, 0x3e23, 0xc007, 0x3e16, 0xc007,
    0x3e09, 0xc008, 0x3dfd, 0xc008, 0x3df0, 0xc009, 0x3de4, 0xc009,
    0x3dd7, 0xc009, 0x3dcb, 0xc00a, 0x3dbe, 0xc00a, 0x3db2, 0xc00b,
    0x3da5, 0xc00b, 0x3d98, 0xc00c, 0x3d8c, 0xc00c, 0x3d7f, 0xc00d,
    0x3d73, 0xc00d, 0x3d66, 0xc00e, 0x3d5a, 0xc00e, 0x3d4d, 0xc00f,
    0x3d40, 0xc00f, 0x3d34, 0xc010, 0x3d27, 0xc010, 0x3d1b, 0xc011,
    0x3d0e, 0xc011, 0x3d02, 0xc012, 0x3cf5, 0xc013, 0x3ce9, 0xc013,
    0x3cdc, 0xc014, 0x3cd0, 0xc014, 0x3cc3, 0xc015, 0x3cb6, 0xc016,
    0x3caa, 0xc016, 0x3c9d, 0xc017, 0x3c91, 0xc018, 0x3c84, 0xc018,
    0x3c78, 0xc019, 0x3c6b, 0xc01a, 0x3c5f, 0xc01a, 0x3c52, 0xc01b,
    0x3c45, 0xc01c, 0x3c39, 0xc01d, 0x3c2c, 0xc01d, 0x3c20, 0xc01e,
    0x3c13, 0xc01f, 0x3c07, 0xc020, 0x3bfa, 0xc020, 0x3bee, 0xc021,
    0x3be1, 0xc022, 0x3bd5, 0xc023, 0x3bc8, 0xc024, 0x3bbc, 0xc024,
    0x3baf, 0xc025, 0x3ba2, 0xc026, 0x3b96, 0xc027, 0x3b89, 0xc028,
    0x3b7d, 0xc029, 0x3b70, 0xc02a, 0x3b64, 0xc02b, 0x3b57, 0xc02b,
    0x3b4b, 0xc02c, 0x3b3e, 0xc02d, 0x3b32, 0xc02e, 0x3b25, 0xc02f,
    0x3b19, 0xc030, 0x3b0c, 0xc031, 0x3b00, 0xc032, 0x3af3, 0xc033,
    0x3ae6, 0xc034, 0x3ada, 0xc035, 0x3acd, 0xc036, 0x3ac1, 0xc037,
    0x3ab4, 0xc038, 0x3aa8, 0xc039, 0x3a9b, 0xc03a, 0x3a8f, 0xc03b,
    0x3a82, 0xc03c, 0x3a76, 0xc03d, 0x3a69, 0xc03f, 0x3a5d, 0xc040,
    0x3a50, 0xc041, 0x3a44, 0xc042, 0x3a37, 0xc043, 0x3a2b, 0xc044,
    0x3a1e, 0xc045, 0x3a12, 0xc047, 0x3a05, 0xc048, 0x39f9, 0xc049,
    0x39ec, 0xc04a, 0x39e0, 0xc04b, 0x39d3, 0xc04c, 0x39c7, 0xc04e,
    0x39ba, 0xc04f, 0x39ae, 0xc050, 0x39a1, 0xc051, 0x3995, 0xc053,
    0x3988, 0xc054, 0x397c, 0xc055, 0x396f, 0xc056, 0x3963, 0xc058,
    0x3956, 0xc059, 0x394a, 0xc05a, 0x393d, 0xc05c, 0x3931, 0xc05d,
    0x3924, 0xc05e, 0x3918, 0xc060, 0x390b, 0xc061, 0x38ff, 0xc062,
    0x38f2, 0xc064, 0x38e6, 0xc065, 0x38d9, 0xc067, 0x38cd, 0xc068,
    0x38c0, 0xc069, 0x38b4, 0xc06b, 0x38a7, 0xc06c, 0x389b, 0xc06e,
    0x388e, 0xc06f, 0x3882, 0xc071, 0x3875, 0xc072, 0x3869, 0xc074,
    0x385c, 0xc075, 0x3850, 0xc077, 0x3843, 0xc078, 0x3837, 0xc07a,
    0x382a, 0xc07b, 0x381e, 0xc07d, 0x3811, 0xc07e, 0x3805, 0xc080,
    0x37f9, 0xc081, 0x37ec, 0xc083, 0x37e0, 0xc085, 0x37d3, 0xc086,
    0x37c7, 0xc088, 0x37ba, 0xc089, 0x37ae, 0xc08b, 0x37a1, 0xc08d,
    0x3795, 0xc08e, 0x3788, 0xc090, 0x377c, 0xc092, 0x376f, 0xc093,
    0x3763, 0xc095, 0x3757, 0xc097, 0x374a, 0xc098, 0x373e, 0xc09a,
    0x3731, 0xc09c, 0x3725, 0xc09e, 0x3718, 0xc09f, 0x370c, 0xc0a1,
    0x36ff, 0xc0a3, 0x36f3, 0xc0a5, 0x36e7, 0xc0a6, 0x36da, 0xc0a8,
    0x36ce, 0xc0aa, 0x36c1, 0xc0ac, 0x36b5, 0xc0ae, 0x36a8, 0xc0af,
    0x369c, 0xc0b1, 0x3690, 0xc0b3, 0x3683, 0xc0b5, 0x3677, 0xc0b7,
    0x366a, 0xc0b9, 0x365e, 0xc0bb, 0x3651, 0xc0bd, 0x3645, 0xc0be,
    0x3639, 0xc0c0, 0x362c, 0xc0c2, 0x3620, 0xc0c4, 0x3613, 0xc0c6,
    0x3607, 0xc0c8, 0x35fa, 0xc0ca, 0x35ee, 0xc0cc, 0x35e2, 0xc0ce,
    0x35d5, 0xc0d0, 0x35c9, 0xc0d2, 0x35bc, 0xc0d4, 0x35b0, 0xc0d6,
    0x35a4, 0xc0d8, 0x3597, 0xc0da, 0x358b, 0xc0dc, 0x357e, 0xc0de,
    0x3572, 0xc0e0, 0x3566, 0xc0e2, 0x3559, 0xc0e4, 0x354d, 0xc0e7,
    0x3540, 0xc0e9, 0x3534, 0xc0eb, 0x3528, 0xc0ed, 0x351b, 0xc0ef,
    0x350f, 0xc0f1, 0x3503, 0xc0f3, 0x34f6, 0xc0f6, 0x34ea, 0xc0f8,
    0x34dd, 0xc0fa, 0x34d1, 0xc0fc, 0x34c5, 0xc0fe, 0x34b8, 0xc100,
    0x34ac, 0xc103, 0x34a0, 0xc105, 0x3493, 0xc107, 0x3487, 0xc109,
    0x347b, 0xc10c, 0x346e, 0xc10e, 0x3462, 0xc110, 0x3455, 0xc113,
    0x3449, 0xc115, 0x343d, 0xc117, 0x3430, 0xc119, 0x3424, 0xc11c,
    0x3418, 0xc11e, 0x340b, 0xc120, 0x33ff, 0xc123, 0x33f3, 0xc125,
    0x33e6, 0xc128, 0x33da, 0xc12a, 0x33ce, 0xc12c, 0x33c1, 0xc12f,
    0x33b5, 0xc131, 0x33a9, 0xc134, 0x339c, 0xc136, 0x3390, 0xc138,
    0x3384, 0xc13b, 0x3377, 0xc13d, 0x336b, 0xc140, 0x335f, 0xc142,
    0x3352, 0xc145, 0x3346, 0xc147, 0x333a, 0xc14a, 0x332d, 0xc14c,
    0x3321, 0xc14f, 0x3315, 0xc151, 0x3308, 0xc154, 0x32fc, 0xc156,
    0x32f0, 0xc159, 0x32e4, 0xc15b, 0x32d7, 0xc15e, 0x32cb, 0xc161,
    0x32bf, 0xc163, 0x32b2, 0xc166, 0x32a6, 0xc168, 0x329a, 0xc16b,
    0x328e, 0xc16e, 0x3281, 0xc170, 0x3275, 0xc173, 0x3269, 0xc176,
    0x325c, 0xc178, 0x3250, 0xc17b, 0x3244, 0xc17e, 0x3238, 0xc180,
    0x322b, 0xc183, 0x321f, 0xc186, 0x3213, 0xc189, 0x3207, 0xc18b,
    0x31fa, 0xc18e, 0x31ee, 0xc191, 0x31e2, 0xc194, 0x31d5, 0xc196,
    0x31c9, 0xc199, 0x31bd, 0xc19c, 0x31b1, 0xc19f, 0x31a4, 0xc1a2,
    0x3198, 0xc1a4, 0x318c, 0xc1a7, 0x3180, 0xc1aa, 0x3174, 0xc1ad,
    0x3167, 0xc1b0, 0x315b, 0xc1b3, 0x314f, 0xc1b6, 0x3143, 0xc1b8,
    0x3136, 0xc1bb, 0x312a, 0xc1be, 0x311e, 0xc1c1, 0x3112, 0xc1c4,
    0x3105, 0xc1c7, 0x30f9, 0xc1ca, 0x30ed, 0xc1cd, 0x30e1, 0xc1d0,
    0x30d5, 0xc1d3, 0x30c8, 0xc1d6, 0x30bc, 0xc1d9, 0x30b0, 0xc1dc,
    0x30a4, 0xc1df, 0x3098, 0xc1e2, 0x308b, 0xc1e5, 0x307f, 0xc1e8,
    0x3073, 0xc1eb, 0x3067, 0xc1ee, 0x305b, 0xc1f1, 0x304e, 0xc1f4,
    0x3042, 0xc1f7, 0x3036, 0xc1fa, 0x302a, 0xc1fd, 0x301e, 0xc201,
    0x3012, 0xc204, 0x3005, 0xc207, 0x2ff9, 0xc20a, 0x2fed, 0xc20d,
    0x2fe1, 0xc210, 0x2fd5, 0xc213, 0x2fc9, 0xc217, 0x2fbc, 0xc21a,
    0x2fb0, 0xc21d, 0x2fa4, 0xc220, 0x2f98, 0xc223, 0x2f8c, 0xc227,
    0x2f80, 0xc22a, 0x2f74, 0xc22d, 0x2f67, 0xc230, 0x2f5b, 0xc234,
    0x2f4f, 0xc237, 0x2f43, 0xc23a, 0x2f37, 0xc23e, 0x2f2b, 0xc241,
    0x2f1f, 0xc244, 0x2f13, 0xc247, 0x2f06, 0xc24b, 0x2efa, 0xc24e,
    0x2eee, 0xc251, 0x2ee2, 0xc255, 0x2ed6, 0xc258, 0x2eca, 0xc25c,
    0x2ebe, 0xc25f, 0x2eb2, 0xc262, 0x2ea6, 0xc266, 0x2e99, 0xc269,
    0x2e8d, 0xc26d, 0x2e81, 0xc270, 0x2e75, 0xc273, 0x2e69, 0xc277,
    0x2e5d, 0xc27a, 0x2e51, 0xc27e, 0x2e45, 0xc281, 0x2e39, 0xc285,
    0x2e2d, 0xc288, 0x2e21, 0xc28c, 0x2e15, 0xc28f, 0x2e09, 0xc293,
    0x2dfc, 0xc296, 0x2df0, 0xc29a, 0x2de4, 0xc29d, 0x2dd8, 0xc2a1,
    0x2dcc, 0xc2a5, 0x2dc0, 0xc2a8, 0x2db4, 0xc2ac, 0x2da8, 0xc2af,
    0x2d9c, 0xc2b3, 0x2d90, 0xc2b7, 0x2d84, 0xc2ba, 0x2d78, 0xc2be,
    0x2d6c, 0xc2c1, 0x2d60, 0xc2c5, 0x2d54, 0xc2c9, 0x2d48, 0xc2cc,
    0x2d3c, 0xc2d0, 0x2d30, 0xc2d4, 0x2d24, 0xc2d8, 0x2d18, 0xc2db,
    0x2d0c, 0xc2df, 0x2d00, 0xc2e3, 0x2cf4, 0xc2e6, 0x2ce8, 0xc2ea,
    0x2cdc, 0xc2ee, 0x2cd0, 0xc2f2, 0x2cc4, 0xc2f5, 0x2cb8, 0xc2f9,
    0x2cac, 0xc2fd, 0x2ca0, 0xc301, 0x2c94, 0xc305, 0x2c88, 0xc308,
    0x2c7c, 0xc30c, 0x2c70, 0xc310, 0x2c64, 0xc314, 0x2c58, 0xc318,
    0x2c4c, 0xc31c, 0x2c40, 0xc320, 0x2c34, 0xc323, 0x2c28, 0xc327,
    0x2c1c, 0xc32b, 0x2c10, 0xc32f, 0x2c05, 0xc333, 0x2bf9, 0xc337,
    0x2bed, 0xc33b, 0x2be1, 0xc33f, 0x2bd5, 0xc343, 0x2bc9, 0xc347,
    0x2bbd, 0xc34b, 0x2bb1, 0xc34f, 0x2ba5, 0xc353, 0x2b99, 0xc357,
    0x2b8d, 0xc35b, 0x2b81, 0xc35f, 0x2b75, 0xc363, 0x2b6a, 0xc367,
    0x2b5e, 0xc36b, 0x2b52, 0xc36f, 0x2b46, 0xc373, 0x2b3a, 0xc377,
    0x2b2e, 0xc37b, 0x2b22, 0xc37f, 0x2b16, 0xc383, 0x2b0a, 0xc387,
    0x2aff, 0xc38c, 0x2af3, 0xc390, 0x2ae7, 0xc394, 0x2adb, 0xc398,
    0x2acf, 0xc39c, 0x2ac3, 0xc3a0, 0x2ab7, 0xc3a5, 0x2aac, 0xc3a9,
    0x2aa0, 0xc3ad, 0x2a94, 0xc3b1, 0x2a88, 0xc3b5, 0x2a7c, 0xc3ba,
    0x2a70, 0xc3be, 0x2a65, 0xc3c2, 0x2a59, 0xc3c6, 0x2a4d, 0xc3ca,
    0x2a41, 0xc3cf, 0x2a35, 0xc3d3, 0x2a29, 0xc3d7, 0x2a1e, 0xc3dc,
    0x2a12, 0xc3e0, 0x2a06, 0xc3e4, 0x29fa, 0xc3e9, 0x29ee, 0xc3ed,
    0x29e3, 0xc3f1, 0x29d7, 0xc3f6, 0x29cb, 0xc3fa, 0x29bf, 0xc3fe,
    0x29b4, 0xc403, 0x29a8, 0xc407, 0x299c, 0xc40b, 0x2990, 0xc410,
    0x2984, 0xc414, 0x2979, 0xc419, 0x296d, 0xc41d, 0x2961, 0xc422,
    0x2955, 0xc426, 0x294a, 0xc42a, 0x293e, 0xc42f, 0x2932, 0xc433,
    0x2926, 0xc438, 0x291b, 0xc43c, 0x290f, 0xc441, 0x2903, 0xc445,
    0x28f7, 0xc44a, 0x28ec, 0xc44e, 0x28e0, 0xc453, 0x28d4, 0xc457,
    0x28c9, 0xc45c, 0x28bd, 0xc461, 0x28b1, 0xc465, 0x28a5, 0xc46a,
    0x289a, 0xc46e, 0x288e, 0xc473, 0x2882, 0xc478, 0x2877, 0xc47c,
    0x286b, 0xc481, 0x285f, 0xc485, 0x2854, 0xc48a, 0x2848, 0xc48f,
    0x283c, 0xc493, 0x2831, 0xc498, 0x2825, 0xc49d, 0x2819, 0xc4a1,
    0x280e, 0xc4a6, 0x2802, 0xc4ab, 0x27f6, 0xc4b0, 0x27eb, 0xc4b4,
    0x27df, 0xc4b9, 0x27d3, 0xc4be, 0x27c8, 0xc4c2, 0x27bc, 0xc4c7,
    0x27b1, 0xc4cc, 0x27a5, 0xc4d1, 0x2799, 0xc4d6, 0x278e, 0xc4da,
    0x2782, 0xc4df, 0x2777, 0xc4e4, 0x276b, 0xc4e9, 0x275f, 0xc4ee,
    0x2754, 0xc4f2, 0x2748, 0xc4f7, 0x273d, 0xc4fc, 0x2731, 0xc501,
    0x2725, 0xc506, 0x271a, 0xc50b, 0x270e, 0xc510, 0x2703, 0xc515,
    0x26f7, 0xc51a, 0x26ec, 0xc51e, 0x26e0, 0xc523, 0x26d4, 0xc528,
    0x26c9, 0xc52d, 0x26bd, 0xc532, 0x26b2, 0xc537, 0x26a6, 0xc53c,
    0x269b, 0xc541, 0x268f, 0xc546, 0x2684, 0xc54b, 0x2678, 0xc550,
    0x266d, 0xc555, 0x2661, 0xc55a, 0x2656, 0xc55f, 0x264a, 0xc564,
    0x263f, 0xc569, 0x2633, 0xc56e, 0x2628, 0xc573, 0x261c, 0xc578,
    0x2611, 0xc57e, 0x2605, 0xc583, 0x25fa, 0xc588, 0x25ee, 0xc58d,
    0x25e3, 0xc592, 0x25d7, 0xc597, 0x25cc, 0xc59c, 0x25c0, 0xc5a1,
    0x25b5, 0xc5a7, 0x25a9, 0xc5ac, 0x259e, 0xc5b1, 0x2592, 0xc5b6,
    0x2587, 0xc5bb, 0x257c, 0xc5c1, 0x2570, 0xc5c6, 0x2565, 0xc5cb,
    0x2559, 0xc5d0, 0x254e, 0xc5d5, 0x2542, 0xc5db, 0x2537, 0xc5e0,
    0x252c, 0xc5e5, 0x2520, 0xc5ea, 0x2515, 0xc5f0, 0x2509, 0xc5f5,
    0x24fe, 0xc5fa, 0x24f3, 0xc600, 0x24e7, 0xc605, 0x24dc, 0xc60a,
    0x24d0, 0xc610, 0x24c5, 0xc615, 0x24ba, 0xc61a, 0x24ae, 0xc620,
    0x24a3, 0xc625, 0x2498, 0xc62a, 0x248c, 0xc630, 0x2481, 0xc635,
    0x2476, 0xc63b, 0x246a, 0xc640, 0x245f, 0xc645, 0x2454, 0xc64b,
    0x2448, 0xc650, 0x243d, 0xc656, 0x2432, 0xc65b, 0x2426, 0xc661,
    0x241b, 0xc666, 0x2410, 0xc66c, 0x2404, 0xc671, 0x23f9, 0xc677,
    0x23ee, 0xc67c, 0x23e2, 0xc682, 0x23d7, 0xc687, 0x23cc, 0xc68d,
    0x23c1, 0xc692, 0x23b5, 0xc698, 0x23aa, 0xc69d, 0x239f, 0xc6a3,
    0x2394, 0xc6a8, 0x2388, 0xc6ae, 0x237d, 0xc6b4, 0x2372, 0xc6b9,
    0x2367, 0xc6bf, 0x235b, 0xc6c5, 0x2350, 0xc6ca, 0x2345, 0xc6d0,
    0x233a, 0xc6d5, 0x232e, 0xc6db, 0x2323, 0xc6e1, 0x2318, 0xc6e6,
    0x230d, 0xc6ec, 0x2301, 0xc6f2, 0x22f6, 0xc6f7, 0x22eb, 0xc6fd,
    0x22e0, 0xc703, 0x22d5, 0xc709, 0x22ca, 0xc70e, 0x22be, 0xc714,
    0x22b3, 0xc71a, 0x22a8, 0xc720, 0x229d, 0xc725, 0x2292, 0xc72b,
    0x2287, 0xc731, 0x227b, 0xc737, 0x2270, 0xc73d, 0x2265, 0xc742,
    0x225a, 0xc748, 0x224f, 0xc74e, 0x2244, 0xc754, 0x2239, 0xc75a,
    0x222d, 0xc75f, 0x2222, 0xc765, 0x2217, 0xc76b, 0x220c, 0xc771,
    0x2201, 0xc777, 0x21f6, 0xc77d, 0x21eb, 0xc783, 0x21e0, 0xc789,
    0x21d5, 0xc78f, 0x21ca, 0xc795, 0x21be, 0xc79a, 0x21b3, 0xc7a0,
    0x21a8, 0xc7a6, 0x219d, 0xc7ac, 0x2192, 0xc7b2, 0x2187, 0xc7b8,
    0x217c, 0xc7be, 0x2171, 0xc7c4, 0x2166, 0xc7ca, 0x215b, 0xc7d0,
    0x2150, 0xc7d6, 0x2145, 0xc7dc, 0x213a, 0xc7e2, 0x212f, 0xc7e8,
    0x2124, 0xc7ee, 0x2119, 0xc7f5, 0x210e, 0xc7fb, 0x2103, 0xc801,
    0x20f8, 0xc807, 0x20ed, 0xc80d, 0x20e2, 0xc813, 0x20d7, 0xc819,
    0x20cc, 0xc81f, 0x20c1, 0xc825, 0x20b6, 0xc82b, 0x20ab, 0xc832,
    0x20a0, 0xc838, 0x2095, 0xc83e, 0x208a, 0xc844, 0x207f, 0xc84a,
    0x2074, 0xc850, 0x2069, 0xc857, 0x205e, 0xc85d, 0x2054, 0xc863,
    0x2049, 0xc869, 0x203e, 0xc870, 0x2033, 0xc876, 0x2028, 0xc87c,
    0x201d, 0xc882, 0x2012, 0xc889, 0x2007, 0xc88f, 0x1ffc, 0xc895,
    0x1ff1, 0xc89b, 0x1fe7, 0xc8a2, 0x1fdc, 0xc8a8, 0x1fd1, 0xc8ae,
    0x1fc6, 0xc8b5, 0x1fbb, 0xc8bb, 0x1fb0, 0xc8c1, 0x1fa5, 0xc8c8,
    0x1f9b, 0xc8ce, 0x1f90, 0xc8d4, 0x1f85, 0xc8db, 0x1f7a, 0xc8e1,
    0x1f6f, 0xc8e8, 0x1f65, 0xc8ee, 0x1f5a, 0xc8f4, 0x1f4f, 0xc8fb,
    0x1f44, 0xc901, 0x1f39, 0xc908, 0x1f2f, 0xc90e, 0x1f24, 0xc915,
    0x1f19, 0xc91b, 0x1f0e, 0xc921, 0x1f03, 0xc928, 0x1ef9, 0xc92e,
    0x1eee, 0xc935, 0x1ee3, 0xc93b, 0x1ed8, 0xc942, 0x1ece, 0xc948,
    0x1ec3, 0xc94f, 0x1eb8, 0xc955, 0x1ead, 0xc95c, 0x1ea3, 0xc963,
    0x1e98, 0xc969, 0x1e8d, 0xc970, 0x1e83, 0xc976, 0x1e78, 0xc97d,
    0x1e6d, 0xc983, 0x1e62, 0xc98a, 0x1e58, 0xc991, 0x1e4d, 0xc997,
    0x1e42, 0xc99e, 0x1e38, 0xc9a4, 0x1e2d, 0xc9ab, 0x1e22, 0xc9b2,
    0x1e18, 0xc9b8, 0x1e0d, 0xc9bf, 0x1e02, 0xc9c6, 0x1df8, 0xc9cc,
    0x1ded, 0xc9d3, 0x1de2, 0xc9da, 0x1dd8, 0xc9e0, 0x1dcd, 0xc9e7,
    0x1dc3, 0xc9ee, 0x1db8, 0xc9f5, 0x1dad, 0xc9fb, 0x1da3, 0xca02,
    0x1d98, 0xca09, 0x1d8e, 0xca10, 0x1d83, 0xca16, 0x1d78, 0xca1d,
    0x1d6e, 0xca24, 0x1d63, 0xca2b, 0x1d59, 0xca32, 0x1d4e, 0xca38,
    0x1d44, 0xca3f, 0x1d39, 0xca46, 0x1d2e, 0xca4d, 0x1d24, 0xca54,
    0x1d19, 0xca5b, 0x1d0f, 0xca61, 0x1d04, 0xca68, 0x1cfa, 0xca6f,
    0x1cef, 0xca76, 0x1ce5, 0xca7d, 0x1cda, 0xca84, 0x1cd0, 0xca8b,
    0x1cc5, 0xca92, 0x1cbb, 0xca99, 0x1cb0, 0xca9f, 0x1ca6, 0xcaa6,
    0x1c9b, 0xcaad, 0x1c91, 0xcab4, 0x1c86, 0xcabb, 0x1c7c, 0xcac2,
    0x1c72, 0xcac9, 0x1c67, 0xcad0, 0x1c5d, 0xcad7, 0x1c52, 0xcade,
    0x1c48, 0xcae5, 0x1c3d, 0xcaec, 0x1c33, 0xcaf3, 0x1c29, 0xcafa,
    0x1c1e, 0xcb01, 0x1c14, 0xcb08, 0x1c09, 0xcb0f, 0x1bff, 0xcb16,
    0x1bf5, 0xcb1e, 0x1bea, 0xcb25, 0x1be0, 0xcb2c, 0x1bd5, 0xcb33,
    0x1bcb, 0xcb3a, 0x1bc1, 0xcb41, 0x1bb6, 0xcb48, 0x1bac, 0xcb4f,
    0x1ba2, 0xcb56, 0x1b97, 0xcb5e, 0x1b8d, 0xcb65, 0x1b83, 0xcb6c,
    0x1b78, 0xcb73, 0x1b6e, 0xcb7a, 0x1b64, 0xcb81, 0x1b59, 0xcb89,
    0x1b4f, 0xcb90, 0x1b45, 0xcb97, 0x1b3b, 0xcb9e, 0x1b30, 0xcba5,
    0x1b26, 0xcbad, 0x1b1c, 0xcbb4, 0x1b11, 0xcbbb, 0x1b07, 0xcbc2,
    0x1afd, 0xcbca, 0x1af3, 0xcbd1, 0x1ae8, 0xcbd8, 0x1ade, 0xcbe0,
    0x1ad4, 0xcbe7, 0x1aca, 0xcbee, 0x1abf, 0xcbf5, 0x1ab5, 0xcbfd,
    0x1aab, 0xcc04, 0x1aa1, 0xcc0b, 0x1a97, 0xcc13, 0x1a8c, 0xcc1a,
    0x1a82, 0xcc21, 0x1a78, 0xcc29, 0x1a6e, 0xcc30, 0x1a64, 0xcc38,
    0x1a5a, 0xcc3f, 0x1a4f, 0xcc46, 0x1a45, 0xcc4e, 0x1a3b, 0xcc55,
    0x1a31, 0xcc5d, 0x1a27, 0xcc64, 0x1a1d, 0xcc6b, 0x1a13, 0xcc73,
    0x1a08, 0xcc7a, 0x19fe, 0xcc82, 0x19f4, 0xcc89, 0x19ea, 0xcc91,
    0x19e0, 0xcc98, 0x19d6, 0xcca0, 0x19cc, 0xcca7, 0x19c2, 0xccaf,
    0x19b8, 0xccb6, 0x19ae, 0xccbe, 0x19a4, 0xccc5, 0x199a, 0xcccd,
    0x198f, 0xccd4, 0x1985, 0xccdc, 0x197b, 0xcce3, 0x1971, 0xcceb,
    0x1967, 0xccf3, 0x195d, 0xccfa, 0x1953, 0xcd02, 0x1949, 0xcd09,
    0x193f, 0xcd11, 0x1935, 0xcd19, 0x192b, 0xcd20, 0x1921, 0xcd28,
    0x1917, 0xcd30, 0x190d, 0xcd37, 0x1903, 0xcd3f, 0x18f9, 0xcd46,
    0x18ef, 0xcd4e, 0x18e6, 0xcd56, 0x18dc, 0xcd5d, 0x18d2, 0xcd65,
    0x18c8, 0xcd6d, 0x18be, 0xcd75, 0x18b4, 0xcd7c, 0x18aa, 0xcd84,
    0x18a0, 0xcd8c, 0x1896, 0xcd93, 0x188c, 0xcd9b, 0x1882, 0xcda3,
    0x1878, 0xcdab, 0x186f, 0xcdb2, 0x1865, 0xcdba, 0x185b, 0xcdc2,
    0x1851, 0xcdca, 0x1847, 0xcdd2, 0x183d, 0xcdd9, 0x1833, 0xcde1,
    0x182a, 0xcde9, 0x1820, 0xcdf1, 0x1816, 0xcdf9, 0x180c, 0xce01,
    0x1802, 0xce08, 0x17f8, 0xce10, 0x17ef, 0xce18, 0x17e5, 0xce20,
    0x17db, 0xce28, 0x17d1, 0xce30, 0x17c8, 0xce38, 0x17be, 0xce40,
    0x17b4, 0xce47, 0x17aa, 0xce4f, 0x17a0, 0xce57, 0x1797, 0xce5f,
    0x178d, 0xce67, 0x1783, 0xce6f, 0x177a, 0xce77, 0x1770, 0xce7f,
    0x1766, 0xce87, 0x175c, 0xce8f, 0x1753, 0xce97, 0x1749, 0xce9f,
    0x173f, 0xcea7, 0x1736, 0xceaf, 0x172c, 0xceb7, 0x1722, 0xcebf,
    0x1719, 0xcec7, 0x170f, 0xcecf, 0x1705, 0xced7, 0x16fc, 0xcedf,
    0x16f2, 0xcee7, 0x16e8, 0xceef, 0x16df, 0xcef7, 0x16d5, 0xceff,
    0x16cb, 0xcf07, 0x16c2, 0xcf10, 0x16b8, 0xcf18, 0x16af, 0xcf20,
    0x16a5, 0xcf28, 0x169b, 0xcf30, 0x1692, 0xcf38, 0x1688, 0xcf40,
    0x167f, 0xcf48, 0x1675, 0xcf51, 0x166c, 0xcf59, 0x1662, 0xcf61,
    0x1659, 0xcf69, 0x164f, 0xcf71, 0x1645, 0xcf79, 0x163c, 0xcf82,
    0x1632, 0xcf8a, 0x1629, 0xcf92, 0x161f, 0xcf9a, 0x1616, 0xcfa3,
    0x160c, 0xcfab, 0x1603, 0xcfb3, 0x15f9, 0xcfbb, 0x15f0, 0xcfc4,
    0x15e6, 0xcfcc, 0x15dd, 0xcfd4, 0x15d4, 0xcfdc, 0x15ca, 0xcfe5,
    0x15c1, 0xcfed, 0x15b7, 0xcff5, 0x15ae, 0xcffe, 0x15a4, 0xd006,
    0x159b, 0xd00e, 0x1592, 0xd016, 0x1588, 0xd01f, 0x157f, 0xd027,
    0x1575, 0xd030, 0x156c, 0xd038, 0x1563, 0xd040, 0x1559, 0xd049,
    0x1550, 0xd051, 0x1547, 0xd059, 0x153d, 0xd062, 0x1534, 0xd06a,
    0x152a, 0xd073, 0x1521, 0xd07b, 0x1518, 0xd083, 0x150e, 0xd08c,
    0x1505, 0xd094, 0x14fc, 0xd09d, 0x14f3, 0xd0a5, 0x14e9, 0xd0ae,
    0x14e0, 0xd0b6, 0x14d7, 0xd0bf, 0x14cd, 0xd0c7, 0x14c4, 0xd0d0,
    0x14bb, 0xd0d8, 0x14b2, 0xd0e0, 0x14a8, 0xd0e9, 0x149f, 0xd0f2,
    0x1496, 0xd0fa, 0x148d, 0xd103, 0x1483, 0xd10b, 0x147a, 0xd114,
    0x1471, 0xd11c, 0x1468, 0xd125, 0x145f, 0xd12d, 0x1455, 0xd136,
    0x144c, 0xd13e, 0x1443, 0xd147, 0x143a, 0xd150, 0x1431, 0xd158,
    0x1428, 0xd161, 0x141e, 0xd169, 0x1415, 0xd172, 0x140c, 0xd17b,
    0x1403, 0xd183, 0x13fa, 0xd18c, 0x13f1, 0xd195, 0x13e8, 0xd19d,
    0x13df, 0xd1a6, 0x13d5, 0xd1af, 0x13cc, 0xd1b7, 0x13c3, 0xd1c0,
    0x13ba, 0xd1c9, 0x13b1, 0xd1d1, 0x13a8, 0xd1da, 0x139f, 0xd1e3,
    0x1396, 0xd1eb, 0x138d, 0xd1f4, 0x1384, 0xd1fd, 0x137b, 0xd206,
    0x1372, 0xd20e, 0x1369, 0xd217, 0x1360, 0xd220, 0x1357, 0xd229,
    0x134e, 0xd231, 0x1345, 0xd23a, 0x133c, 0xd243, 0x1333, 0xd24c,
    0x132a, 0xd255, 0x1321, 0xd25d, 0x1318, 0xd266, 0x130f, 0xd26f,
    0x1306, 0xd278, 0x12fd, 0xd281, 0x12f4, 0xd28a, 0x12eb, 0xd292,
    0x12e2, 0xd29b, 0x12d9, 0xd2a4, 0x12d1, 0xd2ad, 0x12c8, 0xd2b6,
    0x12bf, 0xd2bf, 0x12b6, 0xd2c8, 0x12ad, 0xd2d1, 0x12a4, 0xd2d9,
    0x129b, 0xd2e2, 0x1292, 0xd2eb, 0x128a, 0xd2f4, 0x1281, 0xd2fd,
    0x1278, 0xd306, 0x126f, 0xd30f, 0x1266, 0xd318, 0x125d, 0xd321,
    0x1255, 0xd32a, 0x124c, 0xd333, 0x1243, 0xd33c, 0x123a, 0xd345,
    0x1231, 0xd34e, 0x1229, 0xd357, 0x1220, 0xd360, 0x1217, 0xd369,
    0x120e, 0xd372, 0x1206, 0xd37b, 0x11fd, 0xd384, 0x11f4, 0xd38d,
    0x11eb, 0xd396, 0x11e3, 0xd39f, 0x11da, 0xd3a8, 0x11d1, 0xd3b1,
    0x11c9, 0xd3ba, 0x11c0, 0xd3c3, 0x11b7, 0xd3cc, 0x11af, 0xd3d5,
    0x11a6, 0xd3df, 0x119d, 0xd3e8, 0x1195, 0xd3f1, 0x118c, 0xd3fa,
    0x1183, 0xd403, 0x117b, 0xd40c, 0x1172, 0xd415, 0x1169, 0xd41e,
    0x1161, 0xd428, 0x1158, 0xd431, 0x1150, 0xd43a, 0x1147, 0xd443,
    0x113e, 0xd44c, 0x1136, 0xd455, 0x112d, 0xd45f, 0x1125, 0xd468,
    0x111c, 0xd471, 0x1114, 0xd47a, 0x110b, 0xd483, 0x1103, 0xd48d,
    0x10fa, 0xd496, 0x10f2, 0xd49f, 0x10e9, 0xd4a8, 0x10e0, 0xd4b2,
    0x10d8, 0xd4bb, 0x10d0, 0xd4c4, 0x10c7, 0xd4cd, 0x10bf, 0xd4d7,
    0x10b6, 0xd4e0, 0x10ae, 0xd4e9, 0x10a5, 0xd4f3, 0x109d, 0xd4fc,
    0x1094, 0xd505, 0x108c, 0xd50e, 0x1083, 0xd518, 0x107b, 0xd521,
    0x1073, 0xd52a, 0x106a, 0xd534, 0x1062, 0xd53d, 0x1059, 0xd547,
    0x1051, 0xd550, 0x1049, 0xd559, 0x1040, 0xd563, 0x1038, 0xd56c,
    0x1030, 0xd575, 0x1027, 0xd57f, 0x101f, 0xd588, 0x1016, 0xd592,
    0x100e, 0xd59b, 0x1006, 0xd5a4, 0xffe, 0xd5ae, 0xff5, 0xd5b7,
    0xfed, 0xd5c1, 0xfe5, 0xd5ca, 0xfdc, 0xd5d4, 0xfd4, 0xd5dd,
    0xfcc, 0xd5e6, 0xfc4, 0xd5f0, 0xfbb, 0xd5f9, 0xfb3, 0xd603,
    0xfab, 0xd60c, 0xfa3, 0xd616, 0xf9a, 0xd61f, 0xf92, 0xd629,
    0xf8a, 0xd632, 0xf82, 0xd63c, 0xf79, 0xd645, 0xf71, 0xd64f,
    0xf69, 0xd659, 0xf61, 0xd662, 0xf59, 0xd66c, 0xf51, 0xd675,
    0xf48, 0xd67f, 0xf40, 0xd688, 0xf38, 0xd692, 0xf30, 0xd69b,
    0xf28, 0xd6a5, 0xf20, 0xd6af, 0xf18, 0xd6b8, 0xf10, 0xd6c2,
    0xf07, 0xd6cb, 0xeff, 0xd6d5, 0xef7, 0xd6df, 0xeef, 0xd6e8,
    0xee7, 0xd6f2, 0xedf, 0xd6fc, 0xed7, 0xd705, 0xecf, 0xd70f,
    0xec7, 0xd719, 0xebf, 0xd722, 0xeb7, 0xd72c, 0xeaf, 0xd736,
    0xea7, 0xd73f, 0xe9f, 0xd749, 0xe97, 0xd753, 0xe8f, 0xd75c,
    0xe87, 0xd766, 0xe7f, 0xd770, 0xe77, 0xd77a, 0xe6f, 0xd783,
    0xe67, 0xd78d, 0xe5f, 0xd797, 0xe57, 0xd7a0, 0xe4f, 0xd7aa,
    0xe47, 0xd7b4, 0xe40, 0xd7be, 0xe38, 0xd7c8, 0xe30, 0xd7d1,
    0xe28, 0xd7db, 0xe20, 0xd7e5, 0xe18, 0xd7ef, 0xe10, 0xd7f8,
    0xe08, 0xd802, 0xe01, 0xd80c, 0xdf9, 0xd816, 0xdf1, 0xd820,
    0xde9, 0xd82a, 0xde1, 0xd833, 0xdd9, 0xd83d, 0xdd2, 0xd847,
    0xdca, 0xd851, 0xdc2, 0xd85b, 0xdba, 0xd865, 0xdb2, 0xd86f,
    0xdab, 0xd878, 0xda3, 0xd882, 0xd9b, 0xd88c, 0xd93, 0xd896,
    0xd8c, 0xd8a0, 0xd84, 0xd8aa, 0xd7c, 0xd8b4, 0xd75, 0xd8be,
    0xd6d, 0xd8c8, 0xd65, 0xd8d2, 0xd5d, 0xd8dc, 0xd56, 0xd8e6,
    0xd4e, 0xd8ef, 0xd46, 0xd8f9, 0xd3f, 0xd903, 0xd37, 0xd90d,
    0xd30, 0xd917, 0xd28, 0xd921, 0xd20, 0xd92b, 0xd19, 0xd935,
    0xd11, 0xd93f, 0xd09, 0xd949, 0xd02, 0xd953, 0xcfa, 0xd95d,
    0xcf3, 0xd967, 0xceb, 0xd971, 0xce3, 0xd97b, 0xcdc, 0xd985,
    0xcd4, 0xd98f, 0xccd, 0xd99a, 0xcc5, 0xd9a4, 0xcbe, 0xd9ae,
    0xcb6, 0xd9b8, 0xcaf, 0xd9c2, 0xca7, 0xd9cc, 0xca0, 0xd9d6,
    0xc98, 0xd9e0, 0xc91, 0xd9ea, 0xc89, 0xd9f4, 0xc82, 0xd9fe,
    0xc7a, 0xda08, 0xc73, 0xda13, 0xc6b, 0xda1d, 0xc64, 0xda27,
    0xc5d, 0xda31, 0xc55, 0xda3b, 0xc4e, 0xda45, 0xc46, 0xda4f,
    0xc3f, 0xda5a, 0xc38, 0xda64, 0xc30, 0xda6e, 0xc29, 0xda78,
    0xc21, 0xda82, 0xc1a, 0xda8c, 0xc13, 0xda97, 0xc0b, 0xdaa1,
    0xc04, 0xdaab, 0xbfd, 0xdab5, 0xbf5, 0xdabf, 0xbee, 0xdaca,
    0xbe7, 0xdad4, 0xbe0, 0xdade, 0xbd8, 0xdae8, 0xbd1, 0xdaf3,
    0xbca, 0xdafd, 0xbc2, 0xdb07, 0xbbb, 0xdb11, 0xbb4, 0xdb1c,
    0xbad, 0xdb26, 0xba5, 0xdb30, 0xb9e, 0xdb3b, 0xb97, 0xdb45,
    0xb90, 0xdb4f, 0xb89, 0xdb59, 0xb81, 0xdb64, 0xb7a, 0xdb6e,
    0xb73, 0xdb78, 0xb6c, 0xdb83, 0xb65, 0xdb8d, 0xb5e, 0xdb97,
    0xb56, 0xdba2, 0xb4f, 0xdbac, 0xb48, 0xdbb6, 0xb41, 0xdbc1,
    0xb3a, 0xdbcb, 0xb33, 0xdbd5, 0xb2c, 0xdbe0, 0xb25, 0xdbea,
    0xb1e, 0xdbf5, 0xb16, 0xdbff, 0xb0f, 0xdc09, 0xb08, 0xdc14,
    0xb01, 0xdc1e, 0xafa, 0xdc29, 0xaf3, 0xdc33, 0xaec, 0xdc3d,
    0xae5, 0xdc48, 0xade, 0xdc52, 0xad7, 0xdc5d, 0xad0, 0xdc67,
    0xac9, 0xdc72, 0xac2, 0xdc7c, 0xabb, 0xdc86, 0xab4, 0xdc91,
    0xaad, 0xdc9b, 0xaa6, 0xdca6, 0xa9f, 0xdcb0, 0xa99, 0xdcbb,
    0xa92, 0xdcc5, 0xa8b, 0xdcd0, 0xa84, 0xdcda, 0xa7d, 0xdce5,
    0xa76, 0xdcef, 0xa6f, 0xdcfa, 0xa68, 0xdd04, 0xa61, 0xdd0f,
    0xa5b, 0xdd19, 0xa54, 0xdd24, 0xa4d, 0xdd2e, 0xa46, 0xdd39,
    0xa3f, 0xdd44, 0xa38, 0xdd4e, 0xa32, 0xdd59, 0xa2b, 0xdd63,
    0xa24, 0xdd6e, 0xa1d, 0xdd78, 0xa16, 0xdd83, 0xa10, 0xdd8e,
    0xa09, 0xdd98, 0xa02, 0xdda3, 0x9fb, 0xddad, 0x9f5, 0xddb8,
    0x9ee, 0xddc3, 0x9e7, 0xddcd, 0x9e0, 0xddd8, 0x9da, 0xdde2,
    0x9d3, 0xdded, 0x9cc, 0xddf8, 0x9c6, 0xde02, 0x9bf, 0xde0d,
    0x9b8, 0xde18, 0x9b2, 0xde22, 0x9ab, 0xde2d, 0x9a4, 0xde38,
    0x99e, 0xde42, 0x997, 0xde4d, 0x991, 0xde58, 0x98a, 0xde62,
    0x983, 0xde6d, 0x97d, 0xde78, 0x976, 0xde83, 0x970, 0xde8d,
    0x969, 0xde98, 0x963, 0xdea3, 0x95c, 0xdead, 0x955, 0xdeb8,
    0x94f, 0xdec3, 0x948, 0xdece, 0x942, 0xded8, 0x93b, 0xdee3,
    0x935, 0xdeee, 0x92e, 0xdef9, 0x928, 0xdf03, 0x921, 0xdf0e,
    0x91b, 0xdf19, 0x915, 0xdf24, 0x90e, 0xdf2f, 0x908, 0xdf39,
    0x901, 0xdf44, 0x8fb, 0xdf4f, 0x8f4, 0xdf5a, 0x8ee, 0xdf65,
    0x8e8, 0xdf6f, 0x8e1, 0xdf7a, 0x8db, 0xdf85, 0x8d4, 0xdf90,
    0x8ce, 0xdf9b, 0x8c8, 0xdfa5, 0x8c1, 0xdfb0, 0x8bb, 0xdfbb,
    0x8b5, 0xdfc6, 0x8ae, 0xdfd1, 0x8a8, 0xdfdc, 0x8a2, 0xdfe7,
    0x89b, 0xdff1, 0x895, 0xdffc, 0x88f, 0xe007, 0x889, 0xe012,
    0x882, 0xe01d, 0x87c, 0xe028, 0x876, 0xe033, 0x870, 0xe03e,
    0x869, 0xe049, 0x863, 0xe054, 0x85d, 0xe05e, 0x857, 0xe069,
    0x850, 0xe074, 0x84a, 0xe07f, 0x844, 0xe08a, 0x83e, 0xe095,
    0x838, 0xe0a0, 0x832, 0xe0ab, 0x82b, 0xe0b6, 0x825, 0xe0c1,
    0x81f, 0xe0cc, 0x819, 0xe0d7, 0x813, 0xe0e2, 0x80d, 0xe0ed,
    0x807, 0xe0f8, 0x801, 0xe103, 0x7fb, 0xe10e, 0x7f5, 0xe119,
    0x7ee, 0xe124, 0x7e8, 0xe12f, 0x7e2, 0xe13a, 0x7dc, 0xe145,
    0x7d6, 0xe150, 0x7d0, 0xe15b, 0x7ca, 0xe166, 0x7c4, 0xe171,
    0x7be, 0xe17c, 0x7b8, 0xe187, 0x7b2, 0xe192, 0x7ac, 0xe19d,
    0x7a6, 0xe1a8, 0x7a0, 0xe1b3, 0x79a, 0xe1be, 0x795, 0xe1ca,
    0x78f, 0xe1d5, 0x789, 0xe1e0, 0x783, 0xe1eb, 0x77d, 0xe1f6,
    0x777, 0xe201, 0x771, 0xe20c, 0x76b, 0xe217, 0x765, 0xe222,
    0x75f, 0xe22d, 0x75a, 0xe239, 0x754, 0xe244, 0x74e, 0xe24f,
    0x748, 0xe25a, 0x742, 0xe265, 0x73d, 0xe270, 0x737, 0xe27b,
    0x731, 0xe287, 0x72b, 0xe292, 0x725, 0xe29d, 0x720, 0xe2a8,
    0x71a, 0xe2b3, 0x714, 0xe2be, 0x70e, 0xe2ca, 0x709, 0xe2d5,
    0x703, 0xe2e0, 0x6fd, 0xe2eb, 0x6f7, 0xe2f6, 0x6f2, 0xe301,
    0x6ec, 0xe30d, 0x6e6, 0xe318, 0x6e1, 0xe323, 0x6db, 0xe32e,
    0x6d5, 0xe33a, 0x6d0, 0xe345, 0x6ca, 0xe350, 0x6c5, 0xe35b,
    0x6bf, 0xe367, 0x6b9, 0xe372, 0x6b4, 0xe37d, 0x6ae, 0xe388,
    0x6a8, 0xe394, 0x6a3, 0xe39f, 0x69d, 0xe3aa, 0x698, 0xe3b5,
    0x692, 0xe3c1, 0x68d, 0xe3cc, 0x687, 0xe3d7, 0x682, 0xe3e2,
    0x67c, 0xe3ee, 0x677, 0xe3f9, 0x671, 0xe404, 0x66c, 0xe410,
    0x666, 0xe41b, 0x661, 0xe426, 0x65b, 0xe432, 0x656, 0xe43d,
    0x650, 0xe448, 0x64b, 0xe454, 0x645, 0xe45f, 0x640, 0xe46a,
    0x63b, 0xe476, 0x635, 0xe481, 0x630, 0xe48c, 0x62a, 0xe498,
    0x625, 0xe4a3, 0x620, 0xe4ae, 0x61a, 0xe4ba, 0x615, 0xe4c5,
    0x610, 0xe4d0, 0x60a, 0xe4dc, 0x605, 0xe4e7, 0x600, 0xe4f3,
    0x5fa, 0xe4fe, 0x5f5, 0xe509, 0x5f0, 0xe515, 0x5ea, 0xe520,
    0x5e5, 0xe52c, 0x5e0, 0xe537, 0x5db, 0xe542, 0x5d5, 0xe54e,
    0x5d0, 0xe559, 0x5cb, 0xe565, 0x5c6, 0xe570, 0x5c1, 0xe57c,
    0x5bb, 0xe587, 0x5b6, 0xe592, 0x5b1, 0xe59e, 0x5ac, 0xe5a9,
    0x5a7, 0xe5b5, 0x5a1, 0xe5c0, 0x59c, 0xe5cc, 0x597, 0xe5d7,
    0x592, 0xe5e3, 0x58d, 0xe5ee, 0x588, 0xe5fa, 0x583, 0xe605,
    0x57e, 0xe611, 0x578, 0xe61c, 0x573, 0xe628, 0x56e, 0xe633,
    0x569, 0xe63f, 0x564, 0xe64a, 0x55f, 0xe656, 0x55a, 0xe661,
    0x555, 0xe66d, 0x550, 0xe678, 0x54b, 0xe684, 0x546, 0xe68f,
    0x541, 0xe69b, 0x53c, 0xe6a6, 0x537, 0xe6b2, 0x532, 0xe6bd,
    0x52d, 0xe6c9, 0x528, 0xe6d4, 0x523, 0xe6e0, 0x51e, 0xe6ec,
    0x51a, 0xe6f7, 0x515, 0xe703, 0x510, 0xe70e, 0x50b, 0xe71a,
    0x506, 0xe725, 0x501, 0xe731, 0x4fc, 0xe73d, 0x4f7, 0xe748,
    0x4f2, 0xe754, 0x4ee, 0xe75f, 0x4e9, 0xe76b, 0x4e4, 0xe777,
    0x4df, 0xe782, 0x4da, 0xe78e, 0x4d6, 0xe799, 0x4d1, 0xe7a5,
    0x4cc, 0xe7b1, 0x4c7, 0xe7bc, 0x4c2, 0xe7c8, 0x4be, 0xe7d3,
    0x4b9, 0xe7df, 0x4b4, 0xe7eb, 0x4b0, 0xe7f6, 0x4ab, 0xe802,
    0x4a6, 0xe80e, 0x4a1, 0xe819, 0x49d, 0xe825, 0x498, 0xe831,
    0x493, 0xe83c, 0x48f, 0xe848, 0x48a, 0xe854, 0x485, 0xe85f,
    0x481, 0xe86b, 0x47c, 0xe877, 0x478, 0xe882, 0x473, 0xe88e,
    0x46e, 0xe89a, 0x46a, 0xe8a5, 0x465, 0xe8b1, 0x461, 0xe8bd,
    0x45c, 0xe8c9, 0x457, 0xe8d4, 0x453, 0xe8e0, 0x44e, 0xe8ec,
    0x44a, 0xe8f7, 0x445, 0xe903, 0x441, 0xe90f, 0x43c, 0xe91b,
    0x438, 0xe926, 0x433, 0xe932, 0x42f, 0xe93e, 0x42a, 0xe94a,
    0x426, 0xe955, 0x422, 0xe961, 0x41d, 0xe96d, 0x419, 0xe979,
    0x414, 0xe984, 0x410, 0xe990, 0x40b, 0xe99c, 0x407, 0xe9a8,
    0x403, 0xe9b4, 0x3fe, 0xe9bf, 0x3fa, 0xe9cb, 0x3f6, 0xe9d7,
    0x3f1, 0xe9e3, 0x3ed, 0xe9ee, 0x3e9, 0xe9fa, 0x3e4, 0xea06,
    0x3e0, 0xea12, 0x3dc, 0xea1e, 0x3d7, 0xea29, 0x3d3, 0xea35,
    0x3cf, 0xea41, 0x3ca, 0xea4d, 0x3c6, 0xea59, 0x3c2, 0xea65,
    0x3be, 0xea70, 0x3ba, 0xea7c, 0x3b5, 0xea88, 0x3b1, 0xea94,
    0x3ad, 0xeaa0, 0x3a9, 0xeaac, 0x3a5, 0xeab7, 0x3a0, 0xeac3,
    0x39c, 0xeacf, 0x398, 0xeadb, 0x394, 0xeae7, 0x390, 0xeaf3,
    0x38c, 0xeaff, 0x387, 0xeb0a, 0x383, 0xeb16, 0x37f, 0xeb22,
    0x37b, 0xeb2e, 0x377, 0xeb3a, 0x373, 0xeb46, 0x36f, 0xeb52,
    0x36b, 0xeb5e, 0x367, 0xeb6a, 0x363, 0xeb75, 0x35f, 0xeb81,
    0x35b, 0xeb8d, 0x357, 0xeb99, 0x353, 0xeba5, 0x34f, 0xebb1,
    0x34b, 0xebbd, 0x347, 0xebc9, 0x343, 0xebd5, 0x33f, 0xebe1,
    0x33b, 0xebed, 0x337, 0xebf9, 0x333, 0xec05, 0x32f, 0xec10,
    0x32b, 0xec1c, 0x327, 0xec28, 0x323, 0xec34, 0x320, 0xec40,
    0x31c, 0xec4c, 0x318, 0xec58, 0x314, 0xec64, 0x310, 0xec70,
    0x30c, 0xec7c, 0x308, 0xec88, 0x305, 0xec94, 0x301, 0xeca0,
    0x2fd, 0xecac, 0x2f9, 0xecb8, 0x2f5, 0xecc4, 0x2f2, 0xecd0,
    0x2ee, 0xecdc, 0x2ea, 0xece8, 0x2e6, 0xecf4, 0x2e3, 0xed00,
    0x2df, 0xed0c, 0x2db, 0xed18, 0x2d8, 0xed24, 0x2d4, 0xed30,
    0x2d0, 0xed3c, 0x2cc, 0xed48, 0x2c9, 0xed54, 0x2c5, 0xed60,
    0x2c1, 0xed6c, 0x2be, 0xed78, 0x2ba, 0xed84, 0x2b7, 0xed90,
    0x2b3, 0xed9c, 0x2af, 0xeda8, 0x2ac, 0xedb4, 0x2a8, 0xedc0,
    0x2a5, 0xedcc, 0x2a1, 0xedd8, 0x29d, 0xede4, 0x29a, 0xedf0,
    0x296, 0xedfc, 0x293, 0xee09, 0x28f, 0xee15, 0x28c, 0xee21,
    0x288, 0xee2d, 0x285, 0xee39, 0x281, 0xee45, 0x27e, 0xee51,
    0x27a, 0xee5d, 0x277, 0xee69, 0x273, 0xee75, 0x270, 0xee81,
    0x26d, 0xee8d, 0x269, 0xee99, 0x266, 0xeea6, 0x262, 0xeeb2,
    0x25f, 0xeebe, 0x25c, 0xeeca, 0x258, 0xeed6, 0x255, 0xeee2,
    0x251, 0xeeee, 0x24e, 0xeefa, 0x24b, 0xef06, 0x247, 0xef13,
    0x244, 0xef1f, 0x241, 0xef2b, 0x23e, 0xef37, 0x23a, 0xef43,
    0x237, 0xef4f, 0x234, 0xef5b, 0x230, 0xef67, 0x22d, 0xef74,
    0x22a, 0xef80, 0x227, 0xef8c, 0x223, 0xef98, 0x220, 0xefa4,
    0x21d, 0xefb0, 0x21a, 0xefbc, 0x217, 0xefc9, 0x213, 0xefd5,
    0x210, 0xefe1, 0x20d, 0xefed, 0x20a, 0xeff9, 0x207, 0xf005,
    0x204, 0xf012, 0x201, 0xf01e, 0x1fd, 0xf02a, 0x1fa, 0xf036,
    0x1f7, 0xf042, 0x1f4, 0xf04e, 0x1f1, 0xf05b, 0x1ee, 0xf067,
    0x1eb, 0xf073, 0x1e8, 0xf07f, 0x1e5, 0xf08b, 0x1e2, 0xf098,
    0x1df, 0xf0a4, 0x1dc, 0xf0b0, 0x1d9, 0xf0bc, 0x1d6, 0xf0c8,
    0x1d3, 0xf0d5, 0x1d0, 0xf0e1, 0x1cd, 0xf0ed, 0x1ca, 0xf0f9,
    0x1c7, 0xf105, 0x1c4, 0xf112, 0x1c1, 0xf11e, 0x1be, 0xf12a,
    0x1bb, 0xf136, 0x1b8, 0xf143, 0x1b6, 0xf14f, 0x1b3, 0xf15b,
    0x1b0, 0xf167, 0x1ad, 0xf174, 0x1aa, 0xf180, 0x1a7, 0xf18c,
    0x1a4, 0xf198, 0x1a2, 0xf1a4, 0x19f, 0xf1b1, 0x19c, 0xf1bd,
    0x199, 0xf1c9, 0x196, 0xf1d5, 0x194, 0xf1e2, 0x191, 0xf1ee,
    0x18e, 0xf1fa, 0x18b, 0xf207, 0x189, 0xf213, 0x186, 0xf21f,
    0x183, 0xf22b, 0x180, 0xf238, 0x17e, 0xf244, 0x17b, 0xf250,
    0x178, 0xf25c, 0x176, 0xf269, 0x173, 0xf275, 0x170, 0xf281,
    0x16e, 0xf28e, 0x16b, 0xf29a, 0x168, 0xf2a6, 0x166, 0xf2b2,
    0x163, 0xf2bf, 0x161, 0xf2cb, 0x15e, 0xf2d7, 0x15b, 0xf2e4,
    0x159, 0xf2f0, 0x156, 0xf2fc, 0x154, 0xf308, 0x151, 0xf315,
    0x14f, 0xf321, 0x14c, 0xf32d, 0x14a, 0xf33a, 0x147, 0xf346,
    0x145, 0xf352, 0x142, 0xf35f, 0x140, 0xf36b, 0x13d, 0xf377,
    0x13b, 0xf384, 0x138, 0xf390, 0x136, 0xf39c, 0x134, 0xf3a9,
    0x131, 0xf3b5, 0x12f, 0xf3c1, 0x12c, 0xf3ce, 0x12a, 0xf3da,
    0x128, 0xf3e6, 0x125, 0xf3f3, 0x123, 0xf3ff, 0x120, 0xf40b,
    0x11e, 0xf418, 0x11c, 0xf424, 0x119, 0xf430, 0x117, 0xf43d,
    0x115, 0xf449, 0x113, 0xf455, 0x110, 0xf462, 0x10e, 0xf46e,
    0x10c, 0xf47b, 0x109, 0xf487, 0x107, 0xf493, 0x105, 0xf4a0,
    0x103, 0xf4ac, 0x100, 0xf4b8, 0xfe, 0xf4c5, 0xfc, 0xf4d1,
    0xfa, 0xf4dd, 0xf8, 0xf4ea, 0xf6, 0xf4f6, 0xf3, 0xf503,
    0xf1, 0xf50f, 0xef, 0xf51b, 0xed, 0xf528, 0xeb, 0xf534,
    0xe9, 0xf540, 0xe7, 0xf54d, 0xe4, 0xf559, 0xe2, 0xf566,
    0xe0, 0xf572, 0xde, 0xf57e, 0xdc, 0xf58b, 0xda, 0xf597,
    0xd8, 0xf5a4, 0xd6, 0xf5b0, 0xd4, 0xf5bc, 0xd2, 0xf5c9,
    0xd0, 0xf5d5, 0xce, 0xf5e2, 0xcc, 0xf5ee, 0xca, 0xf5fa,
    0xc8, 0xf607, 0xc6, 0xf613, 0xc4, 0xf620, 0xc2, 0xf62c,
    0xc0, 0xf639, 0xbe, 0xf645, 0xbd, 0xf651, 0xbb, 0xf65e,
    0xb9, 0xf66a, 0xb7, 0xf677, 0xb5, 0xf683, 0xb3, 0xf690,
    0xb1, 0xf69c, 0xaf, 0xf6a8, 0xae, 0xf6b5, 0xac, 0xf6c1,
    0xaa, 0xf6ce, 0xa8, 0xf6da, 0xa6, 0xf6e7, 0xa5, 0xf6f3,
    0xa3, 0xf6ff, 0xa1, 0xf70c, 0x9f, 0xf718, 0x9e, 0xf725,
    0x9c, 0xf731, 0x9a, 0xf73e, 0x98, 0xf74a, 0x97, 0xf757,
    0x95, 0xf763, 0x93, 0xf76f, 0x92, 0xf77c, 0x90, 0xf788,
    0x8e, 0xf795, 0x8d, 0xf7a1, 0x8b, 0xf7ae, 0x89, 0xf7ba,
    0x88, 0xf7c7, 0x86, 0xf7d3, 0x85, 0xf7e0, 0x83, 0xf7ec,
    0x81, 0xf7f9, 0x80, 0xf805, 0x7e, 0xf811, 0x7d, 0xf81e,
    0x7b, 0xf82a, 0x7a, 0xf837, 0x78, 0xf843, 0x77, 0xf850,
    0x75, 0xf85c, 0x74, 0xf869, 0x72, 0xf875, 0x71, 0xf882,
    0x6f, 0xf88e, 0x6e, 0xf89b, 0x6c, 0xf8a7, 0x6b, 0xf8b4,
    0x69, 0xf8c0, 0x68, 0xf8cd, 0x67, 0xf8d9, 0x65, 0xf8e6,
    0x64, 0xf8f2, 0x62, 0xf8ff, 0x61, 0xf90b, 0x60, 0xf918,
    0x5e, 0xf924, 0x5d, 0xf931, 0x5c, 0xf93d, 0x5a, 0xf94a,
    0x59, 0xf956, 0x58, 0xf963, 0x56, 0xf96f, 0x55, 0xf97c,
    0x54, 0xf988, 0x53, 0xf995, 0x51, 0xf9a1, 0x50, 0xf9ae,
    0x4f, 0xf9ba, 0x4e, 0xf9c7, 0x4c, 0xf9d3, 0x4b, 0xf9e0,
    0x4a, 0xf9ec, 0x49, 0xf9f9, 0x48, 0xfa05, 0x47, 0xfa12,
    0x45, 0xfa1e, 0x44, 0xfa2b, 0x43, 0xfa37, 0x42, 0xfa44,
    0x41, 0xfa50, 0x40, 0xfa5d, 0x3f, 0xfa69, 0x3d, 0xfa76,
    0x3c, 0xfa82, 0x3b, 0xfa8f, 0x3a, 0xfa9b, 0x39, 0xfaa8,
    0x38, 0xfab4, 0x37, 0xfac1, 0x36, 0xfacd, 0x35, 0xfada,
    0x34, 0xfae6, 0x33, 0xfaf3, 0x32, 0xfb00, 0x31, 0xfb0c,
    0x30, 0xfb19, 0x2f, 0xfb25, 0x2e, 0xfb32, 0x2d, 0xfb3e,
    0x2c, 0xfb4b, 0x2b, 0xfb57, 0x2b, 0xfb64, 0x2a, 0xfb70,
    0x29, 0xfb7d, 0x28, 0xfb89, 0x27, 0xfb96, 0x26, 0xfba2,
    0x25, 0xfbaf, 0x24, 0xfbbc, 0x24, 0xfbc8, 0x23, 0xfbd5,
    0x22, 0xfbe1, 0x21, 0xfbee, 0x20, 0xfbfa, 0x20, 0xfc07,
    0x1f, 0xfc13, 0x1e, 0xfc20, 0x1d, 0xfc2c, 0x1d, 0xfc39,
    0x1c, 0xfc45, 0x1b, 0xfc52, 0x1a, 0xfc5f, 0x1a, 0xfc6b,
    0x19, 0xfc78, 0x18, 0xfc84, 0x18, 0xfc91, 0x17, 0xfc9d,
    0x16, 0xfcaa, 0x16, 0xfcb6, 0x15, 0xfcc3, 0x14, 0xfcd0,
    0x14, 0xfcdc, 0x13, 0xfce9, 0x13, 0xfcf5, 0x12, 0xfd02,
    0x11, 0xfd0e, 0x11, 0xfd1b, 0x10, 0xfd27, 0x10, 0xfd34,
    0xf, 0xfd40, 0xf, 0xfd4d, 0xe, 0xfd5a, 0xe, 0xfd66,
    0xd, 0xfd73, 0xd, 0xfd7f, 0xc, 0xfd8c, 0xc, 0xfd98,
    0xb, 0xfda5, 0xb, 0xfdb2, 0xa, 0xfdbe, 0xa, 0xfdcb,
    0x9, 0xfdd7, 0x9, 0xfde4, 0x9, 0xfdf0, 0x8, 0xfdfd,
    0x8, 0xfe09, 0x7, 0xfe16, 0x7, 0xfe23, 0x7, 0xfe2f,
    0x6, 0xfe3c, 0x6, 0xfe48, 0x6, 0xfe55, 0x5, 0xfe61,
    0x5, 0xfe6e, 0x5, 0xfe7a, 0x4, 0xfe87, 0x4, 0xfe94,
    0x4, 0xfea0, 0x4, 0xfead, 0x3, 0xfeb9, 0x3, 0xfec6,
    0x3, 0xfed2, 0x3, 0xfedf, 0x2, 0xfeec, 0x2, 0xfef8,
    0x2, 0xff05, 0x2, 0xff11, 0x2, 0xff1e, 0x1, 0xff2a,
    0x1, 0xff37, 0x1, 0xff44, 0x1, 0xff50, 0x1, 0xff5d,
    0x1, 0xff69, 0x1, 0xff76, 0x0, 0xff82, 0x0, 0xff8f,
    0x0, 0xff9b, 0x0, 0xffa8, 0x0, 0xffb5, 0x0, 0xffc1,
    0x0, 0xffce, 0x0, 0xffda, 0x0, 0xffe7, 0x0, 0xfff3,
    0x0, 0x0, 0x0, 0xd, 0x0, 0x19, 0x0, 0x26,
    0x0, 0x32, 0x0, 0x3f, 0x0, 0x4b, 0x0, 0x58,
    0x0, 0x65, 0x0, 0x71, 0x0, 0x7e, 0x1, 0x8a,
    0x1, 0x97, 0x1, 0xa3, 0x1, 0xb0, 0x1, 0xbc,
    0x1, 0xc9, 0x1, 0xd6, 0x2, 0xe2, 0x2, 0xef,
    0x2, 0xfb, 0x2, 0x108, 0x2, 0x114, 0x3, 0x121,
    0x3, 0x12e, 0x3, 0x13a, 0x3, 0x147, 0x4, 0x153,
    0x4, 0x160, 0x4, 0x16c, 0x4, 0x179, 0x5, 0x186,
    0x5, 0x192, 0x5, 0x19f, 0x6, 0x1ab, 0x6, 0x1b8,
    0x6, 0x1c4, 0x7, 0x1d1, 0x7, 0x1dd, 0x7, 0x1ea,
    0x8, 0x1f7, 0x8, 0x203, 0x9, 0x210, 0x9, 0x21c,
    0x9, 0x229, 0xa, 0x235, 0xa, 0x242, 0xb, 0x24e,
    0xb, 0x25b, 0xc, 0x268, 0xc, 0x274, 0xd, 0x281,
    0xd, 0x28d, 0xe, 0x29a, 0xe, 0x2a6, 0xf, 0x2b3,
    0xf, 0x2c0, 0x10, 0x2cc, 0x10, 0x2d9, 0x11, 0x2e5,
    0x11, 0x2f2, 0x12, 0x2fe, 0x13, 0x30b, 0x13, 0x317,
    0x14, 0x324, 0x14, 0x330, 0x15, 0x33d, 0x16, 0x34a,
    0x16, 0x356, 0x17, 0x363, 0x18, 0x36f, 0x18, 0x37c,
    0x19, 0x388, 0x1a, 0x395, 0x1a, 0x3a1, 0x1b, 0x3ae,
    0x1c, 0x3bb, 0x1d, 0x3c7, 0x1d, 0x3d4, 0x1e, 0x3e0,
    0x1f, 0x3ed, 0x20, 0x3f9, 0x20, 0x406, 0x21, 0x412,
    0x22, 0x41f, 0x23, 0x42b, 0x24, 0x438, 0x24, 0x444,
    0x25, 0x451, 0x26, 0x45e, 0x27, 0x46a, 0x28, 0x477,
    0x29, 0x483, 0x2a, 0x490, 0x2b, 0x49c, 0x2b, 0x4a9,
    0x2c, 0x4b5, 0x2d, 0x4c2, 0x2e, 0x4ce, 0x2f, 0x4db,
    0x30, 0x4e7, 0x31, 0x4f4, 0x32, 0x500, 0x33, 0x50d,
    0x34, 0x51a, 0x35, 0x526, 0x36, 0x533, 0x37, 0x53f,
    0x38, 0x54c, 0x39, 0x558, 0x3a, 0x565, 0x3b, 0x571,
    0x3c, 0x57e, 0x3d, 0x58a, 0x3f, 0x597, 0x40, 0x5a3,
    0x41, 0x5b0, 0x42, 0x5bc, 0x43, 0x5c9, 0x44, 0x5d5,
    0x45, 0x5e2, 0x47, 0x5ee, 0x48, 0x5fb, 0x49, 0x607,
    0x4a, 0x614, 0x4b, 0x620, 0x4c, 0x62d, 0x4e, 0x639,
    0x4f, 0x646, 0x50, 0x652, 0x51, 0x65f, 0x53, 0x66b,
    0x54, 0x678, 0x55, 0x684, 0x56, 0x691, 0x58, 0x69d,
    0x59, 0x6aa, 0x5a, 0x6b6, 0x5c, 0x6c3, 0x5d, 0x6cf,
    0x5e, 0x6dc, 0x60, 0x6e8, 0x61, 0x6f5, 0x62, 0x701,
    0x64, 0x70e, 0x65, 0x71a, 0x67, 0x727, 0x68, 0x733,
    0x69, 0x740, 0x6b, 0x74c, 0x6c, 0x759, 0x6e, 0x765,
    0x6f, 0x772, 0x71, 0x77e, 0x72, 0x78b, 0x74, 0x797,
    0x75, 0x7a4, 0x77, 0x7b0, 0x78, 0x7bd, 0x7a, 0x7c9,
    0x7b, 0x7d6, 0x7d, 0x7e2, 0x7e, 0x7ef, 0x80, 0x7fb,
    0x81, 0x807, 0x83, 0x814, 0x85, 0x820, 0x86, 0x82d,
    0x88, 0x839, 0x89, 0x846, 0x8b, 0x852, 0x8d, 0x85f,
    0x8e, 0x86b, 0x90, 0x878, 0x92, 0x884, 0x93, 0x891,
    0x95, 0x89d, 0x97, 0x8a9, 0x98, 0x8b6, 0x9a, 0x8c2,
    0x9c, 0x8cf, 0x9e, 0x8db, 0x9f, 0x8e8, 0xa1, 0x8f4,
    0xa3, 0x901, 0xa5, 0x90d, 0xa6, 0x919, 0xa8, 0x926,
    0xaa, 0x932, 0xac, 0x93f, 0xae, 0x94b, 0xaf, 0x958,
    0xb1, 0x964, 0xb3, 0x970, 0xb5, 0x97d, 0xb7, 0x989,
    0xb9, 0x996, 0xbb, 0x9a2, 0xbd, 0x9af, 0xbe, 0x9bb,
    0xc0, 0x9c7, 0xc2, 0x9d4, 0xc4, 0x9e0, 0xc6, 0x9ed,
    0xc8, 0x9f9, 0xca, 0xa06, 0xcc, 0xa12, 0xce, 0xa1e,
    0xd0, 0xa2b, 0xd2, 0xa37, 0xd4, 0xa44, 0xd6, 0xa50,
    0xd8, 0xa5c, 0xda, 0xa69, 0xdc, 0xa75, 0xde, 0xa82,
    0xe0, 0xa8e, 0xe2, 0xa9a, 0xe4, 0xaa7, 0xe7, 0xab3,
    0xe9, 0xac0, 0xeb, 0xacc, 0xed, 0xad8, 0xef, 0xae5,
    0xf1, 0xaf1, 0xf3, 0xafd, 0xf6, 0xb0a, 0xf8, 0xb16,
    0xfa, 0xb23, 0xfc, 0xb2f, 0xfe, 0xb3b, 0x100, 0xb48,
    0x103, 0xb54, 0x105, 0xb60, 0x107, 0xb6d, 0x109, 0xb79,
    0x10c, 0xb85, 0x10e, 0xb92, 0x110, 0xb9e, 0x113, 0xbab,
    0x115, 0xbb7, 0x117, 0xbc3, 0x119, 0xbd0, 0x11c, 0xbdc,
    0x11e, 0xbe8, 0x120, 0xbf5, 0x123, 0xc01, 0x125, 0xc0d,
    0x128, 0xc1a, 0x12a, 0xc26, 0x12c, 0xc32, 0x12f, 0xc3f,
    0x131, 0xc4b, 0x134, 0xc57, 0x136, 0xc64, 0x138, 0xc70,
    0x13b, 0xc7c, 0x13d, 0xc89, 0x140, 0xc95, 0x142, 0xca1,
    0x145, 0xcae, 0x147, 0xcba, 0x14a, 0xcc6, 0x14c, 0xcd3,
    0x14f, 0xcdf, 0x151, 0xceb, 0x154, 0xcf8, 0x156, 0xd04,
    0x159, 0xd10, 0x15b, 0xd1c, 0x15e, 0xd29, 0x161, 0xd35,
    0x163, 0xd41, 0x166, 0xd4e, 0x168, 0xd5a, 0x16b, 0xd66,
    0x16e, 0xd72, 0x170, 0xd7f, 0x173, 0xd8b, 0x176, 0xd97,
    0x178, 0xda4, 0x17b, 0xdb0, 0x17e, 0xdbc, 0x180, 0xdc8,
    0x183, 0xdd5, 0x186, 0xde1, 0x189, 0xded, 0x18b, 0xdf9,
    0x18e, 0xe06, 0x191, 0xe12, 0x194, 0xe1e, 0x196, 0xe2b,
    0x199, 0xe37, 0x19c, 0xe43, 0x19f, 0xe4f, 0x1a2, 0xe5c,
    0x1a4, 0xe68, 0x1a7, 0xe74, 0x1aa, 0xe80, 0x1ad, 0xe8c,
    0x1b0, 0xe99, 0x1b3, 0xea5, 0x1b6, 0xeb1, 0x1b8, 0xebd,
    0x1bb, 0xeca, 0x1be, 0xed6, 0x1c1, 0xee2, 0x1c4, 0xeee,
    0x1c7, 0xefb, 0x1ca, 0xf07, 0x1cd, 0xf13, 0x1d0, 0xf1f,
    0x1d3, 0xf2b, 0x1d6, 0xf38, 0x1d9, 0xf44, 0x1dc, 0xf50,
    0x1df, 0xf5c, 0x1e2, 0xf68, 0x1e5, 0xf75, 0x1e8, 0xf81,
    0x1eb, 0xf8d, 0x1ee, 0xf99, 0x1f1, 0xfa5, 0x1f4, 0xfb2,
    0x1f7, 0xfbe, 0x1fa, 0xfca, 0x1fd, 0xfd6, 0x201, 0xfe2,
    0x204, 0xfee, 0x207, 0xffb, 0x20a, 0x1007, 0x20d, 0x1013,
    0x210, 0x101f, 0x213, 0x102b, 0x217, 0x1037, 0x21a, 0x1044,
    0x21d, 0x1050, 0x220, 0x105c, 0x223, 0x1068, 0x227, 0x1074,
    0x22a, 0x1080, 0x22d, 0x108c, 0x230, 0x1099, 0x234, 0x10a5,
    0x237, 0x10b1, 0x23a, 0x10bd, 0x23e, 0x10c9, 0x241, 0x10d5,
    0x244, 0x10e1, 0x247, 0x10ed, 0x24b, 0x10fa, 0x24e, 0x1106,
    0x251, 0x1112, 0x255, 0x111e, 0x258, 0x112a, 0x25c, 0x1136,
    0x25f, 0x1142, 0x262, 0x114e, 0x266, 0x115a, 0x269, 0x1167,
    0x26d, 0x1173, 0x270, 0x117f, 0x273, 0x118b, 0x277, 0x1197,
    0x27a, 0x11a3, 0x27e, 0x11af, 0x281, 0x11bb, 0x285, 0x11c7,
    0x288, 0x11d3, 0x28c, 0x11df, 0x28f, 0x11eb, 0x293, 0x11f7,
    0x296, 0x1204, 0x29a, 0x1210, 0x29d, 0x121c, 0x2a1, 0x1228,
    0x2a5, 0x1234, 0x2a8, 0x1240, 0x2ac, 0x124c, 0x2af, 0x1258,
    0x2b3, 0x1264, 0x2b7, 0x1270, 0x2ba, 0x127c, 0x2be, 0x1288,
    0x2c1, 0x1294, 0x2c5, 0x12a0, 0x2c9, 0x12ac, 0x2cc, 0x12b8,
    0x2d0, 0x12c4, 0x2d4, 0x12d0, 0x2d8, 0x12dc, 0x2db, 0x12e8,
    0x2df, 0x12f4, 0x2e3, 0x1300, 0x2e6, 0x130c, 0x2ea, 0x1318,
    0x2ee, 0x1324, 0x2f2, 0x1330, 0x2f5, 0x133c, 0x2f9, 0x1348,
    0x2fd, 0x1354, 0x301, 0x1360, 0x305, 0x136c, 0x308, 0x1378,
    0x30c, 0x1384, 0x310, 0x1390, 0x314, 0x139c, 0x318, 0x13a8,
    0x31c, 0x13b4, 0x320, 0x13c0, 0x323, 0x13cc, 0x327, 0x13d8,
    0x32b, 0x13e4, 0x32f, 0x13f0, 0x333, 0x13fb, 0x337, 0x1407,
    0x33b, 0x1413, 0x33f, 0x141f, 0x343, 0x142b, 0x347, 0x1437,
    0x34b, 0x1443, 0x34f, 0x144f, 0x353, 0x145b, 0x357, 0x1467,
    0x35b, 0x1473, 0x35f, 0x147f, 0x363, 0x148b, 0x367, 0x1496,
    0x36b, 0x14a2, 0x36f, 0x14ae, 0x373, 0x14ba, 0x377, 0x14c6,
    0x37b, 0x14d2, 0x37f, 0x14de, 0x383, 0x14ea, 0x387, 0x14f6,
    0x38c, 0x1501, 0x390, 0x150d, 0x394, 0x1519, 0x398, 0x1525,
    0x39c, 0x1531, 0x3a0, 0x153d, 0x3a5, 0x1549, 0x3a9, 0x1554,
    0x3ad, 0x1560, 0x3b1, 0x156c, 0x3b5, 0x1578, 0x3ba, 0x1584,
    0x3be, 0x1590, 0x3c2, 0x159b, 0x3c6, 0x15a7, 0x3ca, 0x15b3,
    0x3cf, 0x15bf, 0x3d3, 0x15cb, 0x3d7, 0x15d7, 0x3dc, 0x15e2,
    0x3e0, 0x15ee, 0x3e4, 0x15fa, 0x3e9, 0x1606, 0x3ed, 0x1612,
    0x3f1, 0x161d, 0x3f6, 0x1629, 0x3fa, 0x1635, 0x3fe, 0x1641,
    0x403, 0x164c, 0x407, 0x1658, 0x40b, 0x1664, 0x410, 0x1670,
    0x414, 0x167c, 0x419, 0x1687, 0x41d, 0x1693, 0x422, 0x169f,
    0x426, 0x16ab, 0x42a, 0x16b6, 0x42f, 0x16c2, 0x433, 0x16ce,
    0x438, 0x16da, 0x43c, 0x16e5, 0x441, 0x16f1, 0x445, 0x16fd,
    0x44a, 0x1709, 0x44e, 0x1714, 0x453, 0x1720, 0x457, 0x172c,
    0x45c, 0x1737, 0x461, 0x1743, 0x465, 0x174f, 0x46a, 0x175b,
    0x46e, 0x1766, 0x473, 0x1772, 0x478, 0x177e, 0x47c, 0x1789,
    0x481, 0x1795, 0x485, 0x17a1, 0x48a, 0x17ac, 0x48f, 0x17b8,
    0x493, 0x17c4, 0x498, 0x17cf, 0x49d, 0x17db, 0x4a1, 0x17e7,
    0x4a6, 0x17f2, 0x4ab, 0x17fe, 0x4b0, 0x180a, 0x4b4, 0x1815,
    0x4b9, 0x1821, 0x4be, 0x182d, 0x4c2, 0x1838, 0x4c7, 0x1844,
    0x4cc, 0x184f, 0x4d1, 0x185b, 0x4d6, 0x1867, 0x4da, 0x1872,
    0x4df, 0x187e, 0x4e4, 0x1889, 0x4e9, 0x1895, 0x4ee, 0x18a1,
    0x4f2, 0x18ac, 0x4f7, 0x18b8, 0x4fc, 0x18c3, 0x501, 0x18cf,
    0x506, 0x18db, 0x50b, 0x18e6, 0x510, 0x18f2, 0x515, 0x18fd,
    0x51a, 0x1909, 0x51e, 0x1914, 0x523, 0x1920, 0x528, 0x192c,
    0x52d, 0x1937, 0x532, 0x1943, 0x537, 0x194e, 0x53c, 0x195a,
    0x541, 0x1965, 0x546, 0x1971, 0x54b, 0x197c, 0x550, 0x1988,
    0x555, 0x1993, 0x55a, 0x199f, 0x55f, 0x19aa, 0x564, 0x19b6,
    0x569, 0x19c1, 0x56e, 0x19cd, 0x573, 0x19d8, 0x578, 0x19e4,
    0x57e, 0x19ef, 0x583, 0x19fb, 0x588, 0x1a06, 0x58d, 0x1a12,
    0x592, 0x1a1d, 0x597, 0x1a29, 0x59c, 0x1a34, 0x5a1, 0x1a40,
    0x5a7, 0x1a4b, 0x5ac, 0x1a57, 0x5b1, 0x1a62, 0x5b6, 0x1a6e,
    0x5bb, 0x1a79, 0x5c1, 0x1a84, 0x5c6, 0x1a90, 0x5cb, 0x1a9b,
    0x5d0, 0x1aa7, 0x5d5, 0x1ab2, 0x5db, 0x1abe, 0x5e0, 0x1ac9,
    0x5e5, 0x1ad4, 0x5ea, 0x1ae0, 0x5f0, 0x1aeb, 0x5f5, 0x1af7,
    0x5fa, 0x1b02, 0x600, 0x1b0d, 0x605, 0x1b19, 0x60a, 0x1b24,
    0x610, 0x1b30, 0x615, 0x1b3b, 0x61a, 0x1b46, 0x620, 0x1b52,
    0x625, 0x1b5d, 0x62a, 0x1b68, 0x630, 0x1b74, 0x635, 0x1b7f,
    0x63b, 0x1b8a, 0x640, 0x1b96, 0x645, 0x1ba1, 0x64b, 0x1bac,
    0x650, 0x1bb8, 0x656, 0x1bc3, 0x65b, 0x1bce, 0x661, 0x1bda,
    0x666, 0x1be5, 0x66c, 0x1bf0, 0x671, 0x1bfc, 0x677, 0x1c07,
    0x67c, 0x1c12, 0x682, 0x1c1e, 0x687, 0x1c29, 0x68d, 0x1c34,
    0x692, 0x1c3f, 0x698, 0x1c4b, 0x69d, 0x1c56, 0x6a3, 0x1c61,
    0x6a8, 0x1c6c, 0x6ae, 0x1c78, 0x6b4, 0x1c83, 0x6b9, 0x1c8e,
    0x6bf, 0x1c99, 0x6c5, 0x1ca5, 0x6ca, 0x1cb0, 0x6d0, 0x1cbb,
    0x6d5, 0x1cc6, 0x6db, 0x1cd2, 0x6e1, 0x1cdd, 0x6e6, 0x1ce8,
    0x6ec, 0x1cf3, 0x6f2, 0x1cff, 0x6f7, 0x1d0a, 0x6fd, 0x1d15,
    0x703, 0x1d20, 0x709, 0x1d2b, 0x70e, 0x1d36, 0x714, 0x1d42,
    0x71a, 0x1d4d, 0x720, 0x1d58, 0x725, 0x1d63, 0x72b, 0x1d6e,
    0x731, 0x1d79, 0x737, 0x1d85, 0x73d, 0x1d90, 0x742, 0x1d9b,
    0x748, 0x1da6, 0x74e, 0x1db1, 0x754, 0x1dbc, 0x75a, 0x1dc7,
    0x75f, 0x1dd3, 0x765, 0x1dde, 0x76b, 0x1de9, 0x771, 0x1df4,
    0x777, 0x1dff, 0x77d, 0x1e0a, 0x783, 0x1e15, 0x789, 0x1e20,
    0x78f, 0x1e2b, 0x795, 0x1e36, 0x79a, 0x1e42, 0x7a0, 0x1e4d,
    0x7a6, 0x1e58, 0x7ac, 0x1e63, 0x7b2, 0x1e6e, 0x7b8, 0x1e79,
    0x7be, 0x1e84, 0x7c4, 0x1e8f, 0x7ca, 0x1e9a, 0x7d0, 0x1ea5,
    0x7d6, 0x1eb0, 0x7dc, 0x1ebb, 0x7e2, 0x1ec6, 0x7e8, 0x1ed1,
    0x7ee, 0x1edc, 0x7f5, 0x1ee7, 0x7fb, 0x1ef2, 0x801, 0x1efd,
    0x807, 0x1f08, 0x80d, 0x1f13, 0x813, 0x1f1e, 0x819, 0x1f29,
    0x81f, 0x1f34, 0x825, 0x1f3f, 0x82b, 0x1f4a, 0x832, 0x1f55,
    0x838, 0x1f60, 0x83e, 0x1f6b, 0x844, 0x1f76, 0x84a, 0x1f81,
    0x850, 0x1f8c, 0x857, 0x1f97, 0x85d, 0x1fa2, 0x863, 0x1fac,
    0x869, 0x1fb7, 0x870, 0x1fc2, 0x876, 0x1fcd, 0x87c, 0x1fd8,
    0x882, 0x1fe3, 0x889, 0x1fee, 0x88f, 0x1ff9, 0x895, 0x2004,
    0x89b, 0x200f, 0x8a2, 0x2019, 0x8a8, 0x2024, 0x8ae, 0x202f,
    0x8b5, 0x203a, 0x8bb, 0x2045, 0x8c1, 0x2050, 0x8c8, 0x205b,
    0x8ce, 0x2065, 0x8d4, 0x2070, 0x8db, 0x207b, 0x8e1, 0x2086,
    0x8e8, 0x2091, 0x8ee, 0x209b, 0x8f4, 0x20a6, 0x8fb, 0x20b1,
    0x901, 0x20bc, 0x908, 0x20c7, 0x90e, 0x20d1, 0x915, 0x20dc,
    0x91b, 0x20e7, 0x921, 0x20f2, 0x928, 0x20fd, 0x92e, 0x2107,
    0x935, 0x2112, 0x93b, 0x211d, 0x942, 0x2128, 0x948, 0x2132,
    0x94f, 0x213d, 0x955, 0x2148, 0x95c, 0x2153, 0x963, 0x215d,
    0x969, 0x2168, 0x970, 0x2173, 0x976, 0x217d, 0x97d, 0x2188,
    0x983, 0x2193, 0x98a, 0x219e, 0x991, 0x21a8, 0x997, 0x21b3,
    0x99e, 0x21be, 0x9a4, 0x21c8, 0x9ab, 0x21d3, 0x9b2, 0x21de,
    0x9b8, 0x21e8, 0x9bf, 0x21f3, 0x9c6, 0x21fe, 0x9cc, 0x2208,
    0x9d3, 0x2213, 0x9da, 0x221e, 0x9e0, 0x2228, 0x9e7, 0x2233,
    0x9ee, 0x223d, 0x9f5, 0x2248, 0x9fb, 0x2253, 0xa02, 0x225d,
    0xa09, 0x2268, 0xa10, 0x2272, 0xa16, 0x227d, 0xa1d, 0x2288,
    0xa24, 0x2292, 0xa2b, 0x229d, 0xa32, 0x22a7, 0xa38, 0x22b2,
    0xa3f, 0x22bc, 0xa46, 0x22c7, 0xa4d, 0x22d2, 0xa54, 0x22dc,
    0xa5b, 0x22e7, 0xa61, 0x22f1, 0xa68, 0x22fc, 0xa6f, 0x2306,
    0xa76, 0x2311, 0xa7d, 0x231b, 0xa84, 0x2326, 0xa8b, 0x2330,
    0xa92, 0x233b, 0xa99, 0x2345, 0xa9f, 0x2350, 0xaa6, 0x235a,
    0xaad, 0x2365, 0xab4, 0x236f, 0xabb, 0x237a, 0xac2, 0x2384,
    0xac9, 0x238e, 0xad0, 0x2399, 0xad7, 0x23a3, 0xade, 0x23ae,
    0xae5, 0x23b8, 0xaec, 0x23c3, 0xaf3, 0x23cd, 0xafa, 0x23d7,
    0xb01, 0x23e2, 0xb08, 0x23ec, 0xb0f, 0x23f7, 0xb16, 0x2401,
    0xb1e, 0x240b, 0xb25, 0x2416, 0xb2c, 0x2420, 0xb33, 0x242b,
    0xb3a, 0x2435, 0xb41, 0x243f, 0xb48, 0x244a, 0xb4f, 0x2454,
    0xb56, 0x245e, 0xb5e, 0x2469, 0xb65, 0x2473, 0xb6c, 0x247d,
    0xb73, 0x2488, 0xb7a, 0x2492, 0xb81, 0x249c, 0xb89, 0x24a7,
    0xb90, 0x24b1, 0xb97, 0x24bb, 0xb9e, 0x24c5, 0xba5, 0x24d0,
    0xbad, 0x24da, 0xbb4, 0x24e4, 0xbbb, 0x24ef, 0xbc2, 0x24f9,
    0xbca, 0x2503, 0xbd1, 0x250d, 0xbd8, 0x2518, 0xbe0, 0x2522,
    0xbe7, 0x252c, 0xbee, 0x2536, 0xbf5, 0x2541, 0xbfd, 0x254b,
    0xc04, 0x2555, 0xc0b, 0x255f, 0xc13, 0x2569, 0xc1a, 0x2574,
    0xc21, 0x257e, 0xc29, 0x2588, 0xc30, 0x2592, 0xc38, 0x259c,
    0xc3f, 0x25a6, 0xc46, 0x25b1, 0xc4e, 0x25bb, 0xc55, 0x25c5,
    0xc5d, 0x25cf, 0xc64, 0x25d9, 0xc6b, 0x25e3, 0xc73, 0x25ed,
    0xc7a, 0x25f8, 0xc82, 0x2602, 0xc89, 0x260c, 0xc91, 0x2616,
    0xc98, 0x2620, 0xca0, 0x262a, 0xca7, 0x2634, 0xcaf, 0x263e,
    0xcb6, 0x2648, 0xcbe, 0x2652, 0xcc5, 0x265c, 0xccd, 0x2666,
    0xcd4, 0x2671, 0xcdc, 0x267b, 0xce3, 0x2685, 0xceb, 0x268f,
    0xcf3, 0x2699, 0xcfa, 0x26a3, 0xd02, 0x26ad, 0xd09, 0x26b7,
    0xd11, 0x26c1, 0xd19, 0x26cb, 0xd20, 0x26d5, 0xd28, 0x26df,
    0xd30, 0x26e9, 0xd37, 0x26f3, 0xd3f, 0x26fd, 0xd46, 0x2707,
    0xd4e, 0x2711, 0xd56, 0x271a, 0xd5d, 0x2724, 0xd65, 0x272e,
    0xd6d, 0x2738, 0xd75, 0x2742, 0xd7c, 0x274c, 0xd84, 0x2756,
    0xd8c, 0x2760, 0xd93, 0x276a, 0xd9b, 0x2774, 0xda3, 0x277e,
    0xdab, 0x2788, 0xdb2, 0x2791, 0xdba, 0x279b, 0xdc2, 0x27a5,
    0xdca, 0x27af, 0xdd2, 0x27b9, 0xdd9, 0x27c3, 0xde1, 0x27cd,
    0xde9, 0x27d6, 0xdf1, 0x27e0, 0xdf9, 0x27ea, 0xe01, 0x27f4,
    0xe08, 0x27fe, 0xe10, 0x2808, 0xe18, 0x2811, 0xe20, 0x281b,
    0xe28, 0x2825, 0xe30, 0x282f, 0xe38, 0x2838, 0xe40, 0x2842,
    0xe47, 0x284c, 0xe4f, 0x2856, 0xe57, 0x2860, 0xe5f, 0x2869,
    0xe67, 0x2873, 0xe6f, 0x287d, 0xe77, 0x2886, 0xe7f, 0x2890,
    0xe87, 0x289a, 0xe8f, 0x28a4, 0xe97, 0x28ad, 0xe9f, 0x28b7,
    0xea7, 0x28c1, 0xeaf, 0x28ca, 0xeb7, 0x28d4, 0xebf, 0x28de,
    0xec7, 0x28e7, 0xecf, 0x28f1, 0xed7, 0x28fb, 0xedf, 0x2904,
    0xee7, 0x290e, 0xeef, 0x2918, 0xef7, 0x2921, 0xeff, 0x292b,
    0xf07, 0x2935, 0xf10, 0x293e, 0xf18, 0x2948, 0xf20, 0x2951,
    0xf28, 0x295b, 0xf30, 0x2965, 0xf38, 0x296e, 0xf40, 0x2978,
    0xf48, 0x2981, 0xf51, 0x298b, 0xf59, 0x2994, 0xf61, 0x299e,
    0xf69, 0x29a7, 0xf71, 0x29b1, 0xf79, 0x29bb, 0xf82, 0x29c4,
    0xf8a, 0x29ce, 0xf92, 0x29d7, 0xf9a, 0x29e1, 0xfa3, 0x29ea,
    0xfab, 0x29f4, 0xfb3, 0x29fd, 0xfbb, 0x2a07, 0xfc4, 0x2a10,
    0xfcc, 0x2a1a, 0xfd4, 0x2a23, 0xfdc, 0x2a2c, 0xfe5, 0x2a36,
    0xfed, 0x2a3f, 0xff5, 0x2a49, 0xffe, 0x2a52, 0x1006, 0x2a5c,
    0x100e, 0x2a65, 0x1016, 0x2a6e, 0x101f, 0x2a78, 0x1027, 0x2a81,
    0x1030, 0x2a8b, 0x1038, 0x2a94, 0x1040, 0x2a9d, 0x1049, 0x2aa7,
    0x1051, 0x2ab0, 0x1059, 0x2ab9, 0x1062, 0x2ac3, 0x106a, 0x2acc,
    0x1073, 0x2ad6, 0x107b, 0x2adf, 0x1083, 0x2ae8, 0x108c, 0x2af2,
    0x1094, 0x2afb, 0x109d, 0x2b04, 0x10a5, 0x2b0d, 0x10ae, 0x2b17,
    0x10b6, 0x2b20, 0x10bf, 0x2b29, 0x10c7, 0x2b33, 0x10d0, 0x2b3c,
    0x10d8, 0x2b45, 0x10e0, 0x2b4e, 0x10e9, 0x2b58, 0x10f2, 0x2b61,
    0x10fa, 0x2b6a, 0x1103, 0x2b73, 0x110b, 0x2b7d, 0x1114, 0x2b86,
    0x111c, 0x2b8f, 0x1125, 0x2b98, 0x112d, 0x2ba1, 0x1136, 0x2bab,
    0x113e, 0x2bb4, 0x1147, 0x2bbd, 0x1150, 0x2bc6, 0x1158, 0x2bcf,
    0x1161, 0x2bd8, 0x1169, 0x2be2, 0x1172, 0x2beb, 0x117b, 0x2bf4,
    0x1183, 0x2bfd, 0x118c, 0x2c06, 0x1195, 0x2c0f, 0x119d, 0x2c18,
    0x11a6, 0x2c21, 0x11af, 0x2c2b, 0x11b7, 0x2c34, 0x11c0, 0x2c3d,
    0x11c9, 0x2c46, 0x11d1, 0x2c4f, 0x11da, 0x2c58, 0x11e3, 0x2c61,
    0x11eb, 0x2c6a, 0x11f4, 0x2c73, 0x11fd, 0x2c7c, 0x1206, 0x2c85,
    0x120e, 0x2c8e, 0x1217, 0x2c97, 0x1220, 0x2ca0, 0x1229, 0x2ca9,
    0x1231, 0x2cb2, 0x123a, 0x2cbb, 0x1243, 0x2cc4, 0x124c, 0x2ccd,
    0x1255, 0x2cd6, 0x125d, 0x2cdf, 0x1266, 0x2ce8, 0x126f, 0x2cf1,
    0x1278, 0x2cfa, 0x1281, 0x2d03, 0x128a, 0x2d0c, 0x1292, 0x2d15,
    0x129b, 0x2d1e, 0x12a4, 0x2d27, 0x12ad, 0x2d2f, 0x12b6, 0x2d38,
    0x12bf, 0x2d41, 0x12c8, 0x2d4a, 0x12d1, 0x2d53, 0x12d9, 0x2d5c,
    0x12e2, 0x2d65, 0x12eb, 0x2d6e, 0x12f4, 0x2d76, 0x12fd, 0x2d7f,
    0x1306, 0x2d88, 0x130f, 0x2d91, 0x1318, 0x2d9a, 0x1321, 0x2da3,
    0x132a, 0x2dab, 0x1333, 0x2db4, 0x133c, 0x2dbd, 0x1345, 0x2dc6,
    0x134e, 0x2dcf, 0x1357, 0x2dd7, 0x1360, 0x2de0, 0x1369, 0x2de9,
    0x1372, 0x2df2, 0x137b, 0x2dfa, 0x1384, 0x2e03, 0x138d, 0x2e0c,
    0x1396, 0x2e15, 0x139f, 0x2e1d, 0x13a8, 0x2e26, 0x13b1, 0x2e2f,
    0x13ba, 0x2e37, 0x13c3, 0x2e40, 0x13cc, 0x2e49, 0x13d5, 0x2e51,
    0x13df, 0x2e5a, 0x13e8, 0x2e63, 0x13f1, 0x2e6b, 0x13fa, 0x2e74,
    0x1403, 0x2e7d, 0x140c, 0x2e85, 0x1415, 0x2e8e, 0x141e, 0x2e97,
    0x1428, 0x2e9f, 0x1431, 0x2ea8, 0x143a, 0x2eb0, 0x1443, 0x2eb9,
    0x144c, 0x2ec2, 0x1455, 0x2eca, 0x145f, 0x2ed3, 0x1468, 0x2edb,
    0x1471, 0x2ee4, 0x147a, 0x2eec, 0x1483, 0x2ef5, 0x148d, 0x2efd,
    0x1496, 0x2f06, 0x149f, 0x2f0e, 0x14a8, 0x2f17, 0x14b2, 0x2f20,
    0x14bb, 0x2f28, 0x14c4, 0x2f30, 0x14cd, 0x2f39, 0x14d7, 0x2f41,
    0x14e0, 0x2f4a, 0x14e9, 0x2f52, 0x14f3, 0x2f5b, 0x14fc, 0x2f63,
    0x1505, 0x2f6c, 0x150e, 0x2f74, 0x1518, 0x2f7d, 0x1521, 0x2f85,
    0x152a, 0x2f8d, 0x1534, 0x2f96, 0x153d, 0x2f9e, 0x1547, 0x2fa7,
    0x1550, 0x2faf, 0x1559, 0x2fb7, 0x1563, 0x2fc0, 0x156c, 0x2fc8,
    0x1575, 0x2fd0, 0x157f, 0x2fd9, 0x1588, 0x2fe1, 0x1592, 0x2fea,
    0x159b, 0x2ff2, 0x15a4, 0x2ffa, 0x15ae, 0x3002, 0x15b7, 0x300b,
    0x15c1, 0x3013, 0x15ca, 0x301b, 0x15d4, 0x3024, 0x15dd, 0x302c,
    0x15e6, 0x3034, 0x15f0, 0x303c, 0x15f9, 0x3045, 0x1603, 0x304d,
    0x160c, 0x3055, 0x1616, 0x305d, 0x161f, 0x3066, 0x1629, 0x306e,
    0x1632, 0x3076, 0x163c, 0x307e, 0x1645, 0x3087, 0x164f, 0x308f,
    0x1659, 0x3097, 0x1662, 0x309f, 0x166c, 0x30a7, 0x1675, 0x30af,
    0x167f, 0x30b8, 0x1688, 0x30c0, 0x1692, 0x30c8, 0x169b, 0x30d0,
    0x16a5, 0x30d8, 0x16af, 0x30e0, 0x16b8, 0x30e8, 0x16c2, 0x30f0,
    0x16cb, 0x30f9, 0x16d5, 0x3101, 0x16df, 0x3109, 0x16e8, 0x3111,
    0x16f2, 0x3119, 0x16fc, 0x3121, 0x1705, 0x3129, 0x170f, 0x3131,
    0x1719, 0x3139, 0x1722, 0x3141, 0x172c, 0x3149, 0x1736, 0x3151,
    0x173f, 0x3159, 0x1749, 0x3161, 0x1753, 0x3169, 0x175c, 0x3171,
    0x1766, 0x3179, 0x1770, 0x3181, 0x177a, 0x3189, 0x1783, 0x3191,
    0x178d, 0x3199, 0x1797, 0x31a1, 0x17a0, 0x31a9, 0x17aa, 0x31b1,
    0x17b4, 0x31b9, 0x17be, 0x31c0, 0x17c8, 0x31c8, 0x17d1, 0x31d0,
    0x17db, 0x31d8, 0x17e5, 0x31e0, 0x17ef, 0x31e8, 0x17f8, 0x31f0,
    0x1802, 0x31f8, 0x180c, 0x31ff, 0x1816, 0x3207, 0x1820, 0x320f,
    0x182a, 0x3217, 0x1833, 0x321f, 0x183d, 0x3227, 0x1847, 0x322e,
    0x1851, 0x3236, 0x185b, 0x323e, 0x1865, 0x3246, 0x186f, 0x324e,
    0x1878, 0x3255, 0x1882, 0x325d, 0x188c, 0x3265, 0x1896, 0x326d,
    0x18a0, 0x3274, 0x18aa, 0x327c, 0x18b4, 0x3284, 0x18be, 0x328b,
    0x18c8, 0x3293, 0x18d2, 0x329b, 0x18dc, 0x32a3, 0x18e6, 0x32aa,
    0x18ef, 0x32b2, 0x18f9, 0x32ba, 0x1903, 0x32c1, 0x190d, 0x32c9,
    0x1917, 0x32d0, 0x1921, 0x32d8, 0x192b, 0x32e0, 0x1935, 0x32e7,
    0x193f, 0x32ef, 0x1949, 0x32f7, 0x1953, 0x32fe, 0x195d, 0x3306,
    0x1967, 0x330d, 0x1971, 0x3315, 0x197b, 0x331d, 0x1985, 0x3324,
    0x198f, 0x332c, 0x199a, 0x3333, 0x19a4, 0x333b, 0x19ae, 0x3342,
    0x19b8, 0x334a, 0x19c2, 0x3351, 0x19cc, 0x3359, 0x19d6, 0x3360,
    0x19e0, 0x3368, 0x19ea, 0x336f, 0x19f4, 0x3377, 0x19fe, 0x337e,
    0x1a08, 0x3386, 0x1a13, 0x338d, 0x1a1d, 0x3395, 0x1a27, 0x339c,
    0x1a31, 0x33a3, 0x1a3b, 0x33ab, 0x1a45, 0x33b2, 0x1a4f, 0x33ba,
    0x1a5a, 0x33c1, 0x1a64, 0x33c8, 0x1a6e, 0x33d0, 0x1a78, 0x33d7,
    0x1a82, 0x33df, 0x1a8c, 0x33e6, 0x1a97, 0x33ed, 0x1aa1, 0x33f5,
    0x1aab, 0x33fc, 0x1ab5, 0x3403, 0x1abf, 0x340b, 0x1aca, 0x3412,
    0x1ad4, 0x3419, 0x1ade, 0x3420, 0x1ae8, 0x3428, 0x1af3, 0x342f,
    0x1afd, 0x3436, 0x1b07, 0x343e, 0x1b11, 0x3445, 0x1b1c, 0x344c,
    0x1b26, 0x3453, 0x1b30, 0x345b, 0x1b3b, 0x3462, 0x1b45, 0x3469,
    0x1b4f, 0x3470, 0x1b59, 0x3477, 0x1b64, 0x347f, 0x1b6e, 0x3486,
    0x1b78, 0x348d, 0x1b83, 0x3494, 0x1b8d, 0x349b, 0x1b97, 0x34a2,
    0x1ba2, 0x34aa, 0x1bac, 0x34b1, 0x1bb6, 0x34b8, 0x1bc1, 0x34bf,
    0x1bcb, 0x34c6, 0x1bd5, 0x34cd, 0x1be0, 0x34d4, 0x1bea, 0x34db,
    0x1bf5, 0x34e2, 0x1bff, 0x34ea, 0x1c09, 0x34f1, 0x1c14, 0x34f8,
    0x1c1e, 0x34ff, 0x1c29, 0x3506, 0x1c33, 0x350d, 0x1c3d, 0x3514,
    0x1c48, 0x351b, 0x1c52, 0x3522, 0x1c5d, 0x3529, 0x1c67, 0x3530,
    0x1c72, 0x3537, 0x1c7c, 0x353e, 0x1c86, 0x3545, 0x1c91, 0x354c,
    0x1c9b, 0x3553, 0x1ca6, 0x355a, 0x1cb0, 0x3561, 0x1cbb, 0x3567,
    0x1cc5, 0x356e, 0x1cd0, 0x3575, 0x1cda, 0x357c, 0x1ce5, 0x3583,
    0x1cef, 0x358a, 0x1cfa, 0x3591, 0x1d04, 0x3598, 0x1d0f, 0x359f,
    0x1d19, 0x35a5, 0x1d24, 0x35ac, 0x1d2e, 0x35b3, 0x1d39, 0x35ba,
    0x1d44, 0x35c1, 0x1d4e, 0x35c8, 0x1d59, 0x35ce, 0x1d63, 0x35d5,
    0x1d6e, 0x35dc, 0x1d78, 0x35e3, 0x1d83, 0x35ea, 0x1d8e, 0x35f0,
    0x1d98, 0x35f7, 0x1da3, 0x35fe, 0x1dad, 0x3605, 0x1db8, 0x360b,
    0x1dc3, 0x3612, 0x1dcd, 0x3619, 0x1dd8, 0x3620, 0x1de2, 0x3626,
    0x1ded, 0x362d, 0x1df8, 0x3634, 0x1e02, 0x363a, 0x1e0d, 0x3641,
    0x1e18, 0x3648, 0x1e22, 0x364e, 0x1e2d, 0x3655, 0x1e38, 0x365c,
    0x1e42, 0x3662, 0x1e4d, 0x3669, 0x1e58, 0x366f, 0x1e62, 0x3676,
    0x1e6d, 0x367d, 0x1e78, 0x3683, 0x1e83, 0x368a, 0x1e8d, 0x3690,
    0x1e98, 0x3697, 0x1ea3, 0x369d, 0x1ead, 0x36a4, 0x1eb8, 0x36ab,
    0x1ec3, 0x36b1, 0x1ece, 0x36b8, 0x1ed8, 0x36be, 0x1ee3, 0x36c5,
    0x1eee, 0x36cb, 0x1ef9, 0x36d2, 0x1f03, 0x36d8, 0x1f0e, 0x36df,
    0x1f19, 0x36e5, 0x1f24, 0x36eb, 0x1f2f, 0x36f2, 0x1f39, 0x36f8,
    0x1f44, 0x36ff, 0x1f4f, 0x3705, 0x1f5a, 0x370c, 0x1f65, 0x3712,
    0x1f6f, 0x3718, 0x1f7a, 0x371f, 0x1f85, 0x3725, 0x1f90, 0x372c,
    0x1f9b, 0x3732, 0x1fa5, 0x3738, 0x1fb0, 0x373f, 0x1fbb, 0x3745,
    0x1fc6, 0x374b, 0x1fd1, 0x3752, 0x1fdc, 0x3758, 0x1fe7, 0x375e,
    0x1ff1, 0x3765, 0x1ffc, 0x376b, 0x2007, 0x3771, 0x2012, 0x3777,
    0x201d, 0x377e, 0x2028, 0x3784, 0x2033, 0x378a, 0x203e, 0x3790,
    0x2049, 0x3797, 0x2054, 0x379d, 0x205e, 0x37a3, 0x2069, 0x37a9,
    0x2074, 0x37b0, 0x207f, 0x37b6, 0x208a, 0x37bc, 0x2095, 0x37c2,
    0x20a0, 0x37c8, 0x20ab, 0x37ce, 0x20b6, 0x37d5, 0x20c1, 0x37db,
    0x20cc, 0x37e1, 0x20d7, 0x37e7, 0x20e2, 0x37ed, 0x20ed, 0x37f3,
    0x20f8, 0x37f9, 0x2103, 0x37ff, 0x210e, 0x3805, 0x2119, 0x380b,
    0x2124, 0x3812, 0x212f, 0x3818, 0x213a, 0x381e, 0x2145, 0x3824,
    0x2150, 0x382a, 0x215b, 0x3830, 0x2166, 0x3836, 0x2171, 0x383c,
    0x217c, 0x3842, 0x2187, 0x3848, 0x2192, 0x384e, 0x219d, 0x3854,
    0x21a8, 0x385a, 0x21b3, 0x3860, 0x21be, 0x3866, 0x21ca, 0x386b,
    0x21d5, 0x3871, 0x21e0, 0x3877, 0x21eb, 0x387d, 0x21f6, 0x3883,
    0x2201, 0x3889, 0x220c, 0x388f, 0x2217, 0x3895, 0x2222, 0x389b,
    0x222d, 0x38a1, 0x2239, 0x38a6, 0x2244, 0x38ac, 0x224f, 0x38b2,
    0x225a, 0x38b8, 0x2265, 0x38be, 0x2270, 0x38c3, 0x227b, 0x38c9,
    0x2287, 0x38cf, 0x2292, 0x38d5, 0x229d, 0x38db, 0x22a8, 0x38e0,
    0x22b3, 0x38e6, 0x22be, 0x38ec, 0x22ca, 0x38f2, 0x22d5, 0x38f7,
    0x22e0, 0x38fd, 0x22eb, 0x3903, 0x22f6, 0x3909, 0x2301, 0x390e,
    0x230d, 0x3914, 0x2318, 0x391a, 0x2323, 0x391f, 0x232e, 0x3925,
    0x233a, 0x392b, 0x2345, 0x3930, 0x2350, 0x3936, 0x235b, 0x393b,
    0x2367, 0x3941, 0x2372, 0x3947, 0x237d, 0x394c, 0x2388, 0x3952,
    0x2394, 0x3958, 0x239f, 0x395d, 0x23aa, 0x3963, 0x23b5, 0x3968,
    0x23c1, 0x396e, 0x23cc, 0x3973, 0x23d7, 0x3979, 0x23e2, 0x397e,
    0x23ee, 0x3984, 0x23f9, 0x3989, 0x2404, 0x398f, 0x2410, 0x3994,
    0x241b, 0x399a, 0x2426, 0x399f, 0x2432, 0x39a5, 0x243d, 0x39aa,
    0x2448, 0x39b0, 0x2454, 0x39b5, 0x245f, 0x39bb, 0x246a, 0x39c0,
    0x2476, 0x39c5, 0x2481, 0x39cb, 0x248c, 0x39d0, 0x2498, 0x39d6,
    0x24a3, 0x39db, 0x24ae, 0x39e0, 0x24ba, 0x39e6, 0x24c5, 0x39eb,
    0x24d0, 0x39f0, 0x24dc, 0x39f6, 0x24e7, 0x39fb, 0x24f3, 0x3a00,
    0x24fe, 0x3a06, 0x2509, 0x3a0b, 0x2515, 0x3a10, 0x2520, 0x3a16,
    0x252c, 0x3a1b, 0x2537, 0x3a20, 0x2542, 0x3a25, 0x254e, 0x3a2b,
    0x2559, 0x3a30, 0x2565, 0x3a35, 0x2570, 0x3a3a, 0x257c, 0x3a3f,
    0x2587, 0x3a45, 0x2592, 0x3a4a, 0x259e, 0x3a4f, 0x25a9, 0x3a54,
    0x25b5, 0x3a59, 0x25c0, 0x3a5f, 0x25cc, 0x3a64, 0x25d7, 0x3a69,
    0x25e3, 0x3a6e, 0x25ee, 0x3a73, 0x25fa, 0x3a78, 0x2605, 0x3a7d,
    0x2611, 0x3a82, 0x261c, 0x3a88, 0x2628, 0x3a8d, 0x2633, 0x3a92,
    0x263f, 0x3a97, 0x264a, 0x3a9c, 0x2656, 0x3aa1, 0x2661, 0x3aa6,
    0x266d, 0x3aab, 0x2678, 0x3ab0, 0x2684, 0x3ab5, 0x268f, 0x3aba,
    0x269b, 0x3abf, 0x26a6, 0x3ac4, 0x26b2, 0x3ac9, 0x26bd, 0x3ace,
    0x26c9, 0x3ad3, 0x26d4, 0x3ad8, 0x26e0, 0x3add, 0x26ec, 0x3ae2,
    0x26f7, 0x3ae6, 0x2703, 0x3aeb, 0x270e, 0x3af0, 0x271a, 0x3af5,
    0x2725, 0x3afa, 0x2731, 0x3aff, 0x273d, 0x3b04, 0x2748, 0x3b09,
    0x2754, 0x3b0e, 0x275f, 0x3b12, 0x276b, 0x3b17, 0x2777, 0x3b1c,
    0x2782, 0x3b21, 0x278e, 0x3b26, 0x2799, 0x3b2a, 0x27a5, 0x3b2f,
    0x27b1, 0x3b34, 0x27bc, 0x3b39, 0x27c8, 0x3b3e, 0x27d3, 0x3b42,
    0x27df, 0x3b47, 0x27eb, 0x3b4c, 0x27f6, 0x3b50, 0x2802, 0x3b55,
    0x280e, 0x3b5a, 0x2819, 0x3b5f, 0x2825, 0x3b63, 0x2831, 0x3b68,
    0x283c, 0x3b6d, 0x2848, 0x3b71, 0x2854, 0x3b76, 0x285f, 0x3b7b,
    0x286b, 0x3b7f, 0x2877, 0x3b84, 0x2882, 0x3b88, 0x288e, 0x3b8d,
    0x289a, 0x3b92, 0x28a5, 0x3b96, 0x28b1, 0x3b9b, 0x28bd, 0x3b9f,
    0x28c9, 0x3ba4, 0x28d4, 0x3ba9, 0x28e0, 0x3bad, 0x28ec, 0x3bb2,
    0x28f7, 0x3bb6, 0x2903, 0x3bbb, 0x290f, 0x3bbf, 0x291b, 0x3bc4,
    0x2926, 0x3bc8, 0x2932, 0x3bcd, 0x293e, 0x3bd1, 0x294a, 0x3bd6,
    0x2955, 0x3bda, 0x2961, 0x3bde, 0x296d, 0x3be3, 0x2979, 0x3be7,
    0x2984, 0x3bec, 0x2990, 0x3bf0, 0x299c, 0x3bf5, 0x29a8, 0x3bf9,
    0x29b4, 0x3bfd, 0x29bf, 0x3c02, 0x29cb, 0x3c06, 0x29d7, 0x3c0a,
    0x29e3, 0x3c0f, 0x29ee, 0x3c13, 0x29fa, 0x3c17, 0x2a06, 0x3c1c,
    0x2a12, 0x3c20, 0x2a1e, 0x3c24, 0x2a29, 0x3c29, 0x2a35, 0x3c2d,
    0x2a41, 0x3c31, 0x2a4d, 0x3c36, 0x2a59, 0x3c3a, 0x2a65, 0x3c3e,
    0x2a70, 0x3c42, 0x2a7c, 0x3c46, 0x2a88, 0x3c4b, 0x2a94, 0x3c4f,
    0x2aa0, 0x3c53, 0x2aac, 0x3c57, 0x2ab7, 0x3c5b, 0x2ac3, 0x3c60,
    0x2acf, 0x3c64, 0x2adb, 0x3c68, 0x2ae7, 0x3c6c, 0x2af3, 0x3c70,
    0x2aff, 0x3c74, 0x2b0a, 0x3c79, 0x2b16, 0x3c7d, 0x2b22, 0x3c81,
    0x2b2e, 0x3c85, 0x2b3a, 0x3c89, 0x2b46, 0x3c8d, 0x2b52, 0x3c91,
    0x2b5e, 0x3c95, 0x2b6a, 0x3c99, 0x2b75, 0x3c9d, 0x2b81, 0x3ca1,
    0x2b8d, 0x3ca5, 0x2b99, 0x3ca9, 0x2ba5, 0x3cad, 0x2bb1, 0x3cb1,
    0x2bbd, 0x3cb5, 0x2bc9, 0x3cb9, 0x2bd5, 0x3cbd, 0x2be1, 0x3cc1,
    0x2bed, 0x3cc5, 0x2bf9, 0x3cc9, 0x2c05, 0x3ccd, 0x2c10, 0x3cd1,
    0x2c1c, 0x3cd5, 0x2c28, 0x3cd9, 0x2c34, 0x3cdd, 0x2c40, 0x3ce0,
    0x2c4c, 0x3ce4, 0x2c58, 0x3ce8, 0x2c64, 0x3cec, 0x2c70, 0x3cf0,
    0x2c7c, 0x3cf4, 0x2c88, 0x3cf8, 0x2c94, 0x3cfb, 0x2ca0, 0x3cff,
    0x2cac, 0x3d03, 0x2cb8, 0x3d07, 0x2cc4, 0x3d0b, 0x2cd0, 0x3d0e,
    0x2cdc, 0x3d12, 0x2ce8, 0x3d16, 0x2cf4, 0x3d1a, 0x2d00, 0x3d1d,
    0x2d0c, 0x3d21, 0x2d18, 0x3d25, 0x2d24, 0x3d28, 0x2d30, 0x3d2c,
    0x2d3c, 0x3d30, 0x2d48, 0x3d34, 0x2d54, 0x3d37, 0x2d60, 0x3d3b,
    0x2d6c, 0x3d3f, 0x2d78, 0x3d42, 0x2d84, 0x3d46, 0x2d90, 0x3d49,
    0x2d9c, 0x3d4d, 0x2da8, 0x3d51, 0x2db4, 0x3d54, 0x2dc0, 0x3d58,
    0x2dcc, 0x3d5b, 0x2dd8, 0x3d5f, 0x2de4, 0x3d63, 0x2df0, 0x3d66,
    0x2dfc, 0x3d6a, 0x2e09, 0x3d6d, 0x2e15, 0x3d71, 0x2e21, 0x3d74,
    0x2e2d, 0x3d78, 0x2e39, 0x3d7b, 0x2e45, 0x3d7f, 0x2e51, 0x3d82,
    0x2e5d, 0x3d86, 0x2e69, 0x3d89, 0x2e75, 0x3d8d, 0x2e81, 0x3d90,
    0x2e8d, 0x3d93, 0x2e99, 0x3d97, 0x2ea6, 0x3d9a, 0x2eb2, 0x3d9e,
    0x2ebe, 0x3da1, 0x2eca, 0x3da4, 0x2ed6, 0x3da8, 0x2ee2, 0x3dab,
    0x2eee, 0x3daf, 0x2efa, 0x3db2, 0x2f06, 0x3db5, 0x2f13, 0x3db9,
    0x2f1f, 0x3dbc, 0x2f2b, 0x3dbf, 0x2f37, 0x3dc2, 0x2f43, 0x3dc6,
    0x2f4f, 0x3dc9, 0x2f5b, 0x3dcc, 0x2f67, 0x3dd0, 0x2f74, 0x3dd3,
    0x2f80, 0x3dd6, 0x2f8c, 0x3dd9, 0x2f98, 0x3ddd, 0x2fa4, 0x3de0,
    0x2fb0, 0x3de3, 0x2fbc, 0x3de6, 0x2fc9, 0x3de9, 0x2fd5, 0x3ded,
    0x2fe1, 0x3df0, 0x2fed, 0x3df3, 0x2ff9, 0x3df6, 0x3005, 0x3df9,
    0x3012, 0x3dfc, 0x301e, 0x3dff, 0x302a, 0x3e03, 0x3036, 0x3e06,
    0x3042, 0x3e09, 0x304e, 0x3e0c, 0x305b, 0x3e0f, 0x3067, 0x3e12,
    0x3073, 0x3e15, 0x307f, 0x3e18, 0x308b, 0x3e1b, 0x3098, 0x3e1e,
    0x30a4, 0x3e21, 0x30b0, 0x3e24, 0x30bc, 0x3e27, 0x30c8, 0x3e2a,
    0x30d5, 0x3e2d, 0x30e1, 0x3e30, 0x30ed, 0x3e33, 0x30f9, 0x3e36,
    0x3105, 0x3e39, 0x3112, 0x3e3c, 0x311e, 0x3e3f, 0x312a, 0x3e42,
    0x3136, 0x3e45, 0x3143, 0x3e48, 0x314f, 0x3e4a, 0x315b, 0x3e4d,
    0x3167, 0x3e50, 0x3174, 0x3e53, 0x3180, 0x3e56, 0x318c, 0x3e59,
    0x3198, 0x3e5c, 0x31a4, 0x3e5e, 0x31b1, 0x3e61, 0x31bd, 0x3e64,
    0x31c9, 0x3e67, 0x31d5, 0x3e6a, 0x31e2, 0x3e6c, 0x31ee, 0x3e6f,
    0x31fa, 0x3e72, 0x3207, 0x3e75, 0x3213, 0x3e77, 0x321f, 0x3e7a,
    0x322b, 0x3e7d, 0x3238, 0x3e80, 0x3244, 0x3e82, 0x3250, 0x3e85,
    0x325c, 0x3e88, 0x3269, 0x3e8a, 0x3275, 0x3e8d, 0x3281, 0x3e90,
    0x328e, 0x3e92, 0x329a, 0x3e95, 0x32a6, 0x3e98, 0x32b2, 0x3e9a,
    0x32bf, 0x3e9d, 0x32cb, 0x3e9f, 0x32d7, 0x3ea2, 0x32e4, 0x3ea5,
    0x32f0, 0x3ea7, 0x32fc, 0x3eaa, 0x3308, 0x3eac, 0x3315, 0x3eaf,
    0x3321, 0x3eb1, 0x332d, 0x3eb4, 0x333a, 0x3eb6, 0x3346, 0x3eb9,
    0x3352, 0x3ebb, 0x335f, 0x3ebe, 0x336b, 0x3ec0, 0x3377, 0x3ec3,
    0x3384, 0x3ec5, 0x3390, 0x3ec8, 0x339c, 0x3eca, 0x33a9, 0x3ecc,
    0x33b5, 0x3ecf, 0x33c1, 0x3ed1, 0x33ce, 0x3ed4, 0x33da, 0x3ed6,
    0x33e6, 0x3ed8, 0x33f3, 0x3edb, 0x33ff, 0x3edd, 0x340b, 0x3ee0,
    0x3418, 0x3ee2, 0x3424, 0x3ee4, 0x3430, 0x3ee7, 0x343d, 0x3ee9,
    0x3449, 0x3eeb, 0x3455, 0x3eed, 0x3462, 0x3ef0, 0x346e, 0x3ef2,
    0x347b, 0x3ef4, 0x3487, 0x3ef7, 0x3493, 0x3ef9, 0x34a0, 0x3efb,
    0x34ac, 0x3efd, 0x34b8, 0x3f00, 0x34c5, 0x3f02, 0x34d1, 0x3f04,
    0x34dd, 0x3f06, 0x34ea, 0x3f08, 0x34f6, 0x3f0a, 0x3503, 0x3f0d,
    0x350f, 0x3f0f, 0x351b, 0x3f11, 0x3528, 0x3f13, 0x3534, 0x3f15,
    0x3540, 0x3f17, 0x354d, 0x3f19, 0x3559, 0x3f1c, 0x3566, 0x3f1e,
    0x3572, 0x3f20, 0x357e, 0x3f22, 0x358b, 0x3f24, 0x3597, 0x3f26,
    0x35a4, 0x3f28, 0x35b0, 0x3f2a, 0x35bc, 0x3f2c, 0x35c9, 0x3f2e,
    0x35d5, 0x3f30, 0x35e2, 0x3f32, 0x35ee, 0x3f34, 0x35fa, 0x3f36,
    0x3607, 0x3f38, 0x3613, 0x3f3a, 0x3620, 0x3f3c, 0x362c, 0x3f3e,
    0x3639, 0x3f40, 0x3645, 0x3f42, 0x3651, 0x3f43, 0x365e, 0x3f45,
    0x366a, 0x3f47, 0x3677, 0x3f49, 0x3683, 0x3f4b, 0x3690, 0x3f4d,
    0x369c, 0x3f4f, 0x36a8, 0x3f51, 0x36b5, 0x3f52, 0x36c1, 0x3f54,
    0x36ce, 0x3f56, 0x36da, 0x3f58, 0x36e7, 0x3f5a, 0x36f3, 0x3f5b,
    0x36ff, 0x3f5d, 0x370c, 0x3f5f, 0x3718, 0x3f61, 0x3725, 0x3f62,
    0x3731, 0x3f64, 0x373e, 0x3f66, 0x374a, 0x3f68, 0x3757, 0x3f69,
    0x3763, 0x3f6b, 0x376f, 0x3f6d, 0x377c, 0x3f6e, 0x3788, 0x3f70,
    0x3795, 0x3f72, 0x37a1, 0x3f73, 0x37ae, 0x3f75, 0x37ba, 0x3f77,
    0x37c7, 0x3f78, 0x37d3, 0x3f7a, 0x37e0, 0x3f7b, 0x37ec, 0x3f7d,
    0x37f9, 0x3f7f, 0x3805, 0x3f80, 0x3811, 0x3f82, 0x381e, 0x3f83,
    0x382a, 0x3f85, 0x3837, 0x3f86, 0x3843, 0x3f88, 0x3850, 0x3f89,
    0x385c, 0x3f8b, 0x3869, 0x3f8c, 0x3875, 0x3f8e, 0x3882, 0x3f8f,
    0x388e, 0x3f91, 0x389b, 0x3f92, 0x38a7, 0x3f94, 0x38b4, 0x3f95,
    0x38c0, 0x3f97, 0x38cd, 0x3f98, 0x38d9, 0x3f99, 0x38e6, 0x3f9b,
    0x38f2, 0x3f9c, 0x38ff, 0x3f9e, 0x390b, 0x3f9f, 0x3918, 0x3fa0,
    0x3924, 0x3fa2, 0x3931, 0x3fa3, 0x393d, 0x3fa4, 0x394a, 0x3fa6,
    0x3956, 0x3fa7, 0x3963, 0x3fa8, 0x396f, 0x3faa, 0x397c, 0x3fab,
    0x3988, 0x3fac, 0x3995, 0x3fad, 0x39a1, 0x3faf, 0x39ae, 0x3fb0,
    0x39ba, 0x3fb1, 0x39c7, 0x3fb2, 0x39d3, 0x3fb4, 0x39e0, 0x3fb5,
    0x39ec, 0x3fb6, 0x39f9, 0x3fb7, 0x3a05, 0x3fb8, 0x3a12, 0x3fb9,
    0x3a1e, 0x3fbb, 0x3a2b, 0x3fbc, 0x3a37, 0x3fbd, 0x3a44, 0x3fbe,
    0x3a50, 0x3fbf, 0x3a5d, 0x3fc0, 0x3a69, 0x3fc1, 0x3a76, 0x3fc3,
    0x3a82, 0x3fc4, 0x3a8f, 0x3fc5, 0x3a9b, 0x3fc6, 0x3aa8, 0x3fc7,
    0x3ab4, 0x3fc8, 0x3ac1, 0x3fc9, 0x3acd, 0x3fca, 0x3ada, 0x3fcb,
    0x3ae6, 0x3fcc, 0x3af3, 0x3fcd, 0x3b00, 0x3fce, 0x3b0c, 0x3fcf,
    0x3b19, 0x3fd0, 0x3b25, 0x3fd1, 0x3b32, 0x3fd2, 0x3b3e, 0x3fd3,
    0x3b4b, 0x3fd4, 0x3b57, 0x3fd5, 0x3b64, 0x3fd5, 0x3b70, 0x3fd6,
    0x3b7d, 0x3fd7, 0x3b89, 0x3fd8, 0x3b96, 0x3fd9, 0x3ba2, 0x3fda,
    0x3baf, 0x3fdb, 0x3bbc, 0x3fdc, 0x3bc8, 0x3fdc, 0x3bd5, 0x3fdd,
    0x3be1, 0x3fde, 0x3bee, 0x3fdf, 0x3bfa, 0x3fe0, 0x3c07, 0x3fe0,
    0x3c13, 0x3fe1, 0x3c20, 0x3fe2, 0x3c2c, 0x3fe3, 0x3c39, 0x3fe3,
    0x3c45, 0x3fe4, 0x3c52, 0x3fe5, 0x3c5f, 0x3fe6, 0x3c6b, 0x3fe6,
    0x3c78, 0x3fe7, 0x3c84, 0x3fe8, 0x3c91, 0x3fe8, 0x3c9d, 0x3fe9,
    0x3caa, 0x3fea, 0x3cb6, 0x3fea, 0x3cc3, 0x3feb, 0x3cd0, 0x3fec,
    0x3cdc, 0x3fec, 0x3ce9, 0x3fed, 0x3cf5, 0x3fed, 0x3d02, 0x3fee,
    0x3d0e, 0x3fef, 0x3d1b, 0x3fef, 0x3d27, 0x3ff0, 0x3d34, 0x3ff0,
    0x3d40, 0x3ff1, 0x3d4d, 0x3ff1, 0x3d5a, 0x3ff2, 0x3d66, 0x3ff2,
    0x3d73, 0x3ff3, 0x3d7f, 0x3ff3, 0x3d8c, 0x3ff4, 0x3d98, 0x3ff4,
    0x3da5, 0x3ff5, 0x3db2, 0x3ff5, 0x3dbe, 0x3ff6, 0x3dcb, 0x3ff6,
    0x3dd7, 0x3ff7, 0x3de4, 0x3ff7, 0x3df0, 0x3ff7, 0x3dfd, 0x3ff8,
    0x3e09, 0x3ff8, 0x3e16, 0x3ff9, 0x3e23, 0x3ff9, 0x3e2f, 0x3ff9,
    0x3e3c, 0x3ffa, 0x3e48, 0x3ffa, 0x3e55, 0x3ffa, 0x3e61, 0x3ffb,
    0x3e6e, 0x3ffb, 0x3e7a, 0x3ffb, 0x3e87, 0x3ffc, 0x3e94, 0x3ffc,
    0x3ea0, 0x3ffc, 0x3ead, 0x3ffc, 0x3eb9, 0x3ffd, 0x3ec6, 0x3ffd,
    0x3ed2, 0x3ffd, 0x3edf, 0x3ffd, 0x3eec, 0x3ffe, 0x3ef8, 0x3ffe,
    0x3f05, 0x3ffe, 0x3f11, 0x3ffe, 0x3f1e, 0x3ffe, 0x3f2a, 0x3fff,
    0x3f37, 0x3fff, 0x3f44, 0x3fff, 0x3f50, 0x3fff, 0x3f5d, 0x3fff,
    0x3f69, 0x3fff, 0x3f76, 0x3fff, 0x3f82, 0x4000, 0x3f8f, 0x4000,
    0x3f9b, 0x4000, 0x3fa8, 0x4000, 0x3fb5, 0x4000, 0x3fc1, 0x4000,
    0x3fce, 0x4000, 0x3fda, 0x4000, 0x3fe7, 0x4000, 0x3ff3, 0x4000,
};

/**    
* \par   
* Generation of real_CoefB array:    
* \par    
* n = 4096    
* <pre>for (i = 0; i < n; i++)    
*  {    
*    pBTable[2 * i] = 0.5 * (1.0 + sin (2 * PI / (double) (2 * n) * (double) i));    
*    pBTable[2 * i + 1] = 0.5 * (1.0 * cos (2 * PI / (double) (2 * n) * (double) i));    
*  } </pre>   
* \par    
* Convert to fixed point Q15 format    
*       round(pBTable[i] * pow(2, 15))    
*    
*/
static const q15_t ALIGN4 realCoefBQ15[8192] = {
    0x4000, 0x4000, 0x400d, 0x4000, 0x4019, 0x4000, 0x4026, 0x4000,
    0x4032, 0x4000, 0x403f, 0x4000, 0x404b, 0x4000, 0x4058, 0x4000,
    0x4065, 0x4000, 0x4071, 0x4000, 0x407e, 0x4000, 0x408a, 0x3fff,
    0x4097, 0x3fff, 0x40a3, 0x3fff, 0x40b0, 0x3fff, 0x40bc, 0x3fff,
    0x40c9, 0x3fff, 0x40d6, 0x3fff, 0x40e2, 0x3ffe, 0x40ef, 0x3ffe,
    0x40fb, 0x3ffe, 0x4108, 0x3ffe, 0x4114, 0x3ffe, 0x4121, 0x3ffd,
    0x412e, 0x3ffd, 0x413a, 0x3ffd, 0x4147, 0x3ffd, 0x4153, 0x3ffc,
    0x4160, 0x3ffc, 0x416c, 0x3ffc, 0x4179, 0x3ffc, 0x4186, 0x3ffb,
    0x4192, 0x3ffb, 0x419f, 0x3ffb, 0x41ab, 0x3ffa, 0x41b8, 0x3ffa,
    0x41c4, 0x3ffa, 0x41d1, 0x3ff9, 0x41dd, 0x3ff9, 0x41ea, 0x3ff9,
    0x41f7, 0x3ff8, 0x4203, 0x3ff8, 0x4210, 0x3ff7, 0x421c, 0x3ff7,
    0x4229, 0x3ff7, 0x4235, 0x3ff6, 0x4242, 0x3ff6, 0x424e, 0x3ff5,
    0x425b, 0x3ff5, 0x4268, 0x3ff4, 0x4274, 0x3ff4, 0x4281, 0x3ff3,
    0x428d, 0x3ff3, 0x429a, 0x3ff2, 0x42a6, 0x3ff2, 0x42b3, 0x3ff1,
    0x42c0, 0x3ff1, 0x42cc, 0x3ff0, 0x42d9, 0x3ff0, 0x42e5, 0x3fef,
    0x42f2, 0x3fef, 0x42fe, 0x3fee, 0x430b, 0x3fed, 0x4317, 0x3fed,
    0x4324, 0x3fec, 0x4330, 0x3fec, 0x433d, 0x3feb, 0x434a, 0x3fea,
    0x4356, 0x3fea, 0x4363, 0x3fe9, 0x436f, 0x3fe8, 0x437c, 0x3fe8,
    0x4388, 0x3fe7, 0x4395, 0x3fe6, 0x43a1, 0x3fe6, 0x43ae, 0x3fe5,
    0x43bb, 0x3fe4, 0x43c7, 0x3fe3, 0x43d4, 0x3fe3, 0x43e0, 0x3fe2,
    0x43ed, 0x3fe1, 0x43f9, 0x3fe0, 0x4406, 0x3fe0, 0x4412, 0x3fdf,
    0x441f, 0x3fde, 0x442b, 0x3fdd, 0x4438, 0x3fdc, 0x4444, 0x3fdc,
    0x4451, 0x3fdb, 0x445e, 0x3fda, 0x446a, 0x3fd9, 0x4477, 0x3fd8,
    0x4483, 0x3fd7, 0x4490, 0x3fd6, 0x449c, 0x3fd5, 0x44a9, 0x3fd5,
    0x44b5, 0x3fd4, 0x44c2, 0x3fd3, 0x44ce, 0x3fd2, 0x44db, 0x3fd1,
    0x44e7, 0x3fd0, 0x44f4, 0x3fcf, 0x4500, 0x3fce, 0x450d, 0x3fcd,
    0x451a, 0x3fcc, 0x4526, 0x3fcb, 0x4533, 0x3fca, 0x453f, 0x3fc9,
    0x454c, 0x3fc8, 0x4558, 0x3fc7, 0x4565, 0x3fc6, 0x4571, 0x3fc5,
    0x457e, 0x3fc4, 0x458a, 0x3fc3, 0x4597, 0x3fc1, 0x45a3, 0x3fc0,
    0x45b0, 0x3fbf, 0x45bc, 0x3fbe, 0x45c9, 0x3fbd, 0x45d5, 0x3fbc,
    0x45e2, 0x3fbb, 0x45ee, 0x3fb9, 0x45fb, 0x3fb8, 0x4607, 0x3fb7,
    0x4614, 0x3fb6, 0x4620, 0x3fb5, 0x462d, 0x3fb4, 0x4639, 0x3fb2,
    0x4646, 0x3fb1, 0x4652, 0x3fb0, 0x465f, 0x3faf, 0x466b, 0x3fad,
    0x4678, 0x3fac, 0x4684, 0x3fab, 0x4691, 0x3faa, 0x469d, 0x3fa8,
    0x46aa, 0x3fa7, 0x46b6, 0x3fa6, 0x46c3, 0x3fa4, 0x46cf, 0x3fa3,
    0x46dc, 0x3fa2, 0x46e8, 0x3fa0, 0x46f5, 0x3f9f, 0x4701, 0x3f9e,
    0x470e, 0x3f9c, 0x471a, 0x3f9b, 0x4727, 0x3f99, 0x4733, 0x3f98,
    0x4740, 0x3f97, 0x474c, 0x3f95, 0x4759, 0x3f94, 0x4765, 0x3f92,
    0x4772, 0x3f91, 0x477e, 0x3f8f, 0x478b, 0x3f8e, 0x4797, 0x3f8c,
    0x47a4, 0x3f8b, 0x47b0, 0x3f89, 0x47bd, 0x3f88, 0x47c9, 0x3f86,
    0x47d6, 0x3f85, 0x47e2, 0x3f83, 0x47ef, 0x3f82, 0x47fb, 0x3f80,
    0x4807, 0x3f7f, 0x4814, 0x3f7d, 0x4820, 0x3f7b, 0x482d, 0x3f7a,
    0x4839, 0x3f78, 0x4846, 0x3f77, 0x4852, 0x3f75, 0x485f, 0x3f73,
    0x486b, 0x3f72, 0x4878, 0x3f70, 0x4884, 0x3f6e, 0x4891, 0x3f6d,
    0x489d, 0x3f6b, 0x48a9, 0x3f69, 0x48b6, 0x3f68, 0x48c2, 0x3f66,
    0x48cf, 0x3f64, 0x48db, 0x3f62, 0x48e8, 0x3f61, 0x48f4, 0x3f5f,
    0x4901, 0x3f5d, 0x490d, 0x3f5b, 0x4919, 0x3f5a, 0x4926, 0x3f58,
    0x4932, 0x3f56, 0x493f, 0x3f54, 0x494b, 0x3f52, 0x4958, 0x3f51,
    0x4964, 0x3f4f, 0x4970, 0x3f4d, 0x497d, 0x3f4b, 0x4989, 0x3f49,
    0x4996, 0x3f47, 0x49a2, 0x3f45, 0x49af, 0x3f43, 0x49bb, 0x3f42,
    0x49c7, 0x3f40, 0x49d4, 0x3f3e, 0x49e0, 0x3f3c, 0x49ed, 0x3f3a,
    0x49f9, 0x3f38, 0x4a06, 0x3f36, 0x4a12, 0x3f34, 0x4a1e, 0x3f32,
    0x4a2b, 0x3f30, 0x4a37, 0x3f2e, 0x4a44, 0x3f2c, 0x4a50, 0x3f2a,
    0x4a5c, 0x3f28, 0x4a69, 0x3f26, 0x4a75, 0x3f24, 0x4a82, 0x3f22,
    0x4a8e, 0x3f20, 0x4a9a, 0x3f1e, 0x4aa7, 0x3f1c, 0x4ab3, 0x3f19,
    0x4ac0, 0x3f17, 0x4acc, 0x3f15, 0x4ad8, 0x3f13, 0x4ae5, 0x3f11,
    0x4af1, 0x3f0f, 0x4afd, 0x3f0d, 0x4b0a, 0x3f0a, 0x4b16, 0x3f08,
    0x4b23, 0x3f06, 0x4b2f, 0x3f04, 0x4b3b, 0x3f02, 0x4b48, 0x3f00,
    0x4b54, 0x3efd, 0x4b60, 0x3efb, 0x4b6d, 0x3ef9, 0x4b79, 0x3ef7,
    0x4b85, 0x3ef4, 0x4b92, 0x3ef2, 0x4b9e, 0x3ef0, 0x4bab, 0x3eed,
    0x4bb7, 0x3eeb, 0x4bc3, 0x3ee9, 0x4bd0, 0x3ee7, 0x4bdc, 0x3ee4,
    0x4be8, 0x3ee2, 0x4bf5, 0x3ee0, 0x4c01, 0x3edd, 0x4c0d, 0x3edb,
    0x4c1a, 0x3ed8, 0x4c26, 0x3ed6, 0x4c32, 0x3ed4, 0x4c3f, 0x3ed1,
    0x4c4b, 0x3ecf, 0x4c57, 0x3ecc, 0x4c64, 0x3eca, 0x4c70, 0x3ec8,
    0x4c7c, 0x3ec5, 0x4c89, 0x3ec3, 0x4c95, 0x3ec0, 0x4ca1, 0x3ebe,
    0x4cae, 0x3ebb, 0x4cba, 0x3eb9, 0x4cc6, 0x3eb6, 0x4cd3, 0x3eb4,
    0x4cdf, 0x3eb1, 0x4ceb, 0x3eaf, 0x4cf8, 0x3eac, 0x4d04, 0x3eaa,
    0x4d10, 0x3ea7, 0x4d1c, 0x3ea5, 0x4d29, 0x3ea2, 0x4d35, 0x3e9f,
    0x4d41, 0x3e9d, 0x4d4e, 0x3e9a, 0x4d5a, 0x3e98, 0x4d66, 0x3e95,
    0x4d72, 0x3e92, 0x4d7f, 0x3e90, 0x4d8b, 0x3e8d, 0x4d97, 0x3e8a,
    0x4da4, 0x3e88, 0x4db0, 0x3e85, 0x4dbc, 0x3e82, 0x4dc8, 0x3e80,
    0x4dd5, 0x3e7d, 0x4de1, 0x3e7a, 0x4ded, 0x3e77, 0x4df9, 0x3e75,
    0x4e06, 0x3e72, 0x4e12, 0x3e6f, 0x4e1e, 0x3e6c, 0x4e2b, 0x3e6a,
    0x4e37, 0x3e67, 0x4e43, 0x3e64, 0x4e4f, 0x3e61, 0x4e5c, 0x3e5e,
    0x4e68, 0x3e5c, 0x4e74, 0x3e59, 0x4e80, 0x3e56, 0x4e8c, 0x3e53,
    0x4e99, 0x3e50, 0x4ea5, 0x3e4d, 0x4eb1, 0x3e4a, 0x4ebd, 0x3e48,
    0x4eca, 0x3e45, 0x4ed6, 0x3e42, 0x4ee2, 0x3e3f, 0x4eee, 0x3e3c,
    0x4efb, 0x3e39, 0x4f07, 0x3e36, 0x4f13, 0x3e33, 0x4f1f, 0x3e30,
    0x4f2b, 0x3e2d, 0x4f38, 0x3e2a, 0x4f44, 0x3e27, 0x4f50, 0x3e24,
    0x4f5c, 0x3e21, 0x4f68, 0x3e1e, 0x4f75, 0x3e1b, 0x4f81, 0x3e18,
    0x4f8d, 0x3e15, 0x4f99, 0x3e12, 0x4fa5, 0x3e0f, 0x4fb2, 0x3e0c,
    0x4fbe, 0x3e09, 0x4fca, 0x3e06, 0x4fd6, 0x3e03, 0x4fe2, 0x3dff,
    0x4fee, 0x3dfc, 0x4ffb, 0x3df9, 0x5007, 0x3df6, 0x5013, 0x3df3,
    0x501f, 0x3df0, 0x502b, 0x3ded, 0x5037, 0x3de9, 0x5044, 0x3de6,
    0x5050, 0x3de3, 0x505c, 0x3de0, 0x5068, 0x3ddd, 0x5074, 0x3dd9,
    0x5080, 0x3dd6, 0x508c, 0x3dd3, 0x5099, 0x3dd0, 0x50a5, 0x3dcc,
    0x50b1, 0x3dc9, 0x50bd, 0x3dc6, 0x50c9, 0x3dc2, 0x50d5, 0x3dbf,
    0x50e1, 0x3dbc, 0x50ed, 0x3db9, 0x50fa, 0x3db5, 0x5106, 0x3db2,
    0x5112, 0x3daf, 0x511e, 0x3dab, 0x512a, 0x3da8, 0x5136, 0x3da4,
    0x5142, 0x3da1, 0x514e, 0x3d9e, 0x515a, 0x3d9a, 0x5167, 0x3d97,
    0x5173, 0x3d93, 0x517f, 0x3d90, 0x518b, 0x3d8d, 0x5197, 0x3d89,
    0x51a3, 0x3d86, 0x51af, 0x3d82, 0x51bb, 0x3d7f, 0x51c7, 0x3d7b,
    0x51d3, 0x3d78, 0x51df, 0x3d74, 0x51eb, 0x3d71, 0x51f7, 0x3d6d,
    0x5204, 0x3d6a, 0x5210, 0x3d66, 0x521c, 0x3d63, 0x5228, 0x3d5f,
    0x5234, 0x3d5b, 0x5240, 0x3d58, 0x524c, 0x3d54, 0x5258, 0x3d51,
    0x5264, 0x3d4d, 0x5270, 0x3d49, 0x527c, 0x3d46, 0x5288, 0x3d42,
    0x5294, 0x3d3f, 0x52a0, 0x3d3b, 0x52ac, 0x3d37, 0x52b8, 0x3d34,
    0x52c4, 0x3d30, 0x52d0, 0x3d2c, 0x52dc, 0x3d28, 0x52e8, 0x3d25,
    0x52f4, 0x3d21, 0x5300, 0x3d1d, 0x530c, 0x3d1a, 0x5318, 0x3d16,
    0x5324, 0x3d12, 0x5330, 0x3d0e, 0x533c, 0x3d0b, 0x5348, 0x3d07,
    0x5354, 0x3d03, 0x5360, 0x3cff, 0x536c, 0x3cfb, 0x5378, 0x3cf8,
    0x5384, 0x3cf4, 0x5390, 0x3cf0, 0x539c, 0x3cec, 0x53a8, 0x3ce8,
    0x53b4, 0x3ce4, 0x53c0, 0x3ce0, 0x53cc, 0x3cdd, 0x53d8, 0x3cd9,
    0x53e4, 0x3cd5, 0x53f0, 0x3cd1, 0x53fb, 0x3ccd, 0x5407, 0x3cc9,
    0x5413, 0x3cc5, 0x541f, 0x3cc1, 0x542b, 0x3cbd, 0x5437, 0x3cb9,
    0x5443, 0x3cb5, 0x544f, 0x3cb1, 0x545b, 0x3cad, 0x5467, 0x3ca9,
    0x5473, 0x3ca5, 0x547f, 0x3ca1, 0x548b, 0x3c9d, 0x5496, 0x3c99,
    0x54a2, 0x3c95, 0x54ae, 0x3c91, 0x54ba, 0x3c8d, 0x54c6, 0x3c89,
    0x54d2, 0x3c85, 0x54de, 0x3c81, 0x54ea, 0x3c7d, 0x54f6, 0x3c79,
    0x5501, 0x3c74, 0x550d, 0x3c70, 0x5519, 0x3c6c, 0x5525, 0x3c68,
    0x5531, 0x3c64, 0x553d, 0x3c60, 0x5549, 0x3c5b, 0x5554, 0x3c57,
    0x5560, 0x3c53, 0x556c, 0x3c4f, 0x5578, 0x3c4b, 0x5584, 0x3c46,
    0x5590, 0x3c42, 0x559b, 0x3c3e, 0x55a7, 0x3c3a, 0x55b3, 0x3c36,
    0x55bf, 0x3c31, 0x55cb, 0x3c2d, 0x55d7, 0x3c29, 0x55e2, 0x3c24,
    0x55ee, 0x3c20, 0x55fa, 0x3c1c, 0x5606, 0x3c17, 0x5612, 0x3c13,
    0x561d, 0x3c0f, 0x5629, 0x3c0a, 0x5635, 0x3c06, 0x5641, 0x3c02,
    0x564c, 0x3bfd, 0x5658, 0x3bf9, 0x5664, 0x3bf5, 0x5670, 0x3bf0,
    0x567c, 0x3bec, 0x5687, 0x3be7, 0x5693, 0x3be3, 0x569f, 0x3bde,
    0x56ab, 0x3bda, 0x56b6, 0x3bd6, 0x56c2, 0x3bd1, 0x56ce, 0x3bcd,
    0x56da, 0x3bc8, 0x56e5, 0x3bc4, 0x56f1, 0x3bbf, 0x56fd, 0x3bbb,
    0x5709, 0x3bb6, 0x5714, 0x3bb2, 0x5720, 0x3bad, 0x572c, 0x3ba9,
    0x5737, 0x3ba4, 0x5743, 0x3b9f, 0x574f, 0x3b9b, 0x575b, 0x3b96,
    0x5766, 0x3b92, 0x5772, 0x3b8d, 0x577e, 0x3b88, 0x5789, 0x3b84,
    0x5795, 0x3b7f, 0x57a1, 0x3b7b, 0x57ac, 0x3b76, 0x57b8, 0x3b71,
    0x57c4, 0x3b6d, 0x57cf, 0x3b68, 0x57db, 0x3b63, 0x57e7, 0x3b5f,
    0x57f2, 0x3b5a, 0x57fe, 0x3b55, 0x580a, 0x3b50, 0x5815, 0x3b4c,
    0x5821, 0x3b47, 0x582d, 0x3b42, 0x5838, 0x3b3e, 0x5844, 0x3b39,
    0x584f, 0x3b34, 0x585b, 0x3b2f, 0x5867, 0x3b2a, 0x5872, 0x3b26,
    0x587e, 0x3b21, 0x5889, 0x3b1c, 0x5895, 0x3b17, 0x58a1, 0x3b12,
    0x58ac, 0x3b0e, 0x58b8, 0x3b09, 0x58c3, 0x3b04, 0x58cf, 0x3aff,
    0x58db, 0x3afa, 0x58e6, 0x3af5, 0x58f2, 0x3af0, 0x58fd, 0x3aeb,
    0x5909, 0x3ae6, 0x5914, 0x3ae2, 0x5920, 0x3add, 0x592c, 0x3ad8,
    0x5937, 0x3ad3, 0x5943, 0x3ace, 0x594e, 0x3ac9, 0x595a, 0x3ac4,
    0x5965, 0x3abf, 0x5971, 0x3aba, 0x597c, 0x3ab5, 0x5988, 0x3ab0,
    0x5993, 0x3aab, 0x599f, 0x3aa6, 0x59aa, 0x3aa1, 0x59b6, 0x3a9c,
    0x59c1, 0x3a97, 0x59cd, 0x3a92, 0x59d8, 0x3a8d, 0x59e4, 0x3a88,
    0x59ef, 0x3a82, 0x59fb, 0x3a7d, 0x5a06, 0x3a78, 0x5a12, 0x3a73,
    0x5a1d, 0x3a6e, 0x5a29, 0x3a69, 0x5a34, 0x3a64, 0x5a40, 0x3a5f,
    0x5a4b, 0x3a59, 0x5a57, 0x3a54, 0x5a62, 0x3a4f, 0x5a6e, 0x3a4a,
    0x5a79, 0x3a45, 0x5a84, 0x3a3f, 0x5a90, 0x3a3a, 0x5a9b, 0x3a35,
    0x5aa7, 0x3a30, 0x5ab2, 0x3a2b, 0x5abe, 0x3a25, 0x5ac9, 0x3a20,
    0x5ad4, 0x3a1b, 0x5ae0, 0x3a16, 0x5aeb, 0x3a10, 0x5af7, 0x3a0b,
    0x5b02, 0x3a06, 0x5b0d, 0x3a00, 0x5b19, 0x39fb, 0x5b24, 0x39f6,
    0x5b30, 0x39f0, 0x5b3b, 0x39eb, 0x5b46, 0x39e6, 0x5b52, 0x39e0,
    0x5b5d, 0x39db, 0x5b68, 0x39d6, 0x5b74, 0x39d0, 0x5b7f, 0x39cb,
    0x5b8a, 0x39c5, 0x5b96, 0x39c0, 0x5ba1, 0x39bb, 0x5bac, 0x39b5,
    0x5bb8, 0x39b0, 0x5bc3, 0x39aa, 0x5bce, 0x39a5, 0x5bda, 0x399f,
    0x5be5, 0x399a, 0x5bf0, 0x3994, 0x5bfc, 0x398f, 0x5c07, 0x3989,
    0x5c12, 0x3984, 0x5c1e, 0x397e, 0x5c29, 0x3979, 0x5c34, 0x3973,
    0x5c3f, 0x396e, 0x5c4b, 0x3968, 0x5c56, 0x3963, 0x5c61, 0x395d,
    0x5c6c, 0x3958, 0x5c78, 0x3952, 0x5c83, 0x394c, 0x5c8e, 0x3947,
    0x5c99, 0x3941, 0x5ca5, 0x393b, 0x5cb0, 0x3936, 0x5cbb, 0x3930,
    0x5cc6, 0x392b, 0x5cd2, 0x3925, 0x5cdd, 0x391f, 0x5ce8, 0x391a,
    0x5cf3, 0x3914, 0x5cff, 0x390e, 0x5d0a, 0x3909, 0x5d15, 0x3903,
    0x5d20, 0x38fd, 0x5d2b, 0x38f7, 0x5d36, 0x38f2, 0x5d42, 0x38ec,
    0x5d4d, 0x38e6, 0x5d58, 0x38e0, 0x5d63, 0x38db, 0x5d6e, 0x38d5,
    0x5d79, 0x38cf, 0x5d85, 0x38c9, 0x5d90, 0x38c3, 0x5d9b, 0x38be,
    0x5da6, 0x38b8, 0x5db1, 0x38b2, 0x5dbc, 0x38ac, 0x5dc7, 0x38a6,
    0x5dd3, 0x38a1, 0x5dde, 0x389b, 0x5de9, 0x3895, 0x5df4, 0x388f,
    0x5dff, 0x3889, 0x5e0a, 0x3883, 0x5e15, 0x387d, 0x5e20, 0x3877,
    0x5e2b, 0x3871, 0x5e36, 0x386b, 0x5e42, 0x3866, 0x5e4d, 0x3860,
    0x5e58, 0x385a, 0x5e63, 0x3854, 0x5e6e, 0x384e, 0x5e79, 0x3848,
    0x5e84, 0x3842, 0x5e8f, 0x383c, 0x5e9a, 0x3836, 0x5ea5, 0x3830,
    0x5eb0, 0x382a, 0x5ebb, 0x3824, 0x5ec6, 0x381e, 0x5ed1, 0x3818,
    0x5edc, 0x3812, 0x5ee7, 0x380b, 0x5ef2, 0x3805, 0x5efd, 0x37ff,
    0x5f08, 0x37f9, 0x5f13, 0x37f3, 0x5f1e, 0x37ed, 0x5f29, 0x37e7,
    0x5f34, 0x37e1, 0x5f3f, 0x37db, 0x5f4a, 0x37d5, 0x5f55, 0x37ce,
    0x5f60, 0x37c8, 0x5f6b, 0x37c2, 0x5f76, 0x37bc, 0x5f81, 0x37b6,
    0x5f8c, 0x37b0, 0x5f97, 0x37a9, 0x5fa2, 0x37a3, 0x5fac, 0x379d,
    0x5fb7, 0x3797, 0x5fc2, 0x3790, 0x5fcd, 0x378a, 0x5fd8, 0x3784,
    0x5fe3, 0x377e, 0x5fee, 0x3777, 0x5ff9, 0x3771, 0x6004, 0x376b,
    0x600f, 0x3765, 0x6019, 0x375e, 0x6024, 0x3758, 0x602f, 0x3752,
    0x603a, 0x374b, 0x6045, 0x3745, 0x6050, 0x373f, 0x605b, 0x3738,
    0x6065, 0x3732, 0x6070, 0x372c, 0x607b, 0x3725, 0x6086, 0x371f,
    0x6091, 0x3718, 0x609b, 0x3712, 0x60a6, 0x370c, 0x60b1, 0x3705,
    0x60bc, 0x36ff, 0x60c7, 0x36f8, 0x60d1, 0x36f2, 0x60dc, 0x36eb,
    0x60e7, 0x36e5, 0x60f2, 0x36df, 0x60fd, 0x36d8, 0x6107, 0x36d2,
    0x6112, 0x36cb, 0x611d, 0x36c5, 0x6128, 0x36be, 0x6132, 0x36b8,
    0x613d, 0x36b1, 0x6148, 0x36ab, 0x6153, 0x36a4, 0x615d, 0x369d,
    0x6168, 0x3697, 0x6173, 0x3690, 0x617d, 0x368a, 0x6188, 0x3683,
    0x6193, 0x367d, 0x619e, 0x3676, 0x61a8, 0x366f, 0x61b3, 0x3669,
    0x61be, 0x3662, 0x61c8, 0x365c, 0x61d3, 0x3655, 0x61de, 0x364e,
    0x61e8, 0x3648, 0x61f3, 0x3641, 0x61fe, 0x363a, 0x6208, 0x3634,
    0x6213, 0x362d, 0x621e, 0x3626, 0x6228, 0x3620, 0x6233, 0x3619,
    0x623d, 0x3612, 0x6248, 0x360b, 0x6253, 0x3605, 0x625d, 0x35fe,
    0x6268, 0x35f7, 0x6272, 0x35f0, 0x627d, 0x35ea, 0x6288, 0x35e3,
    0x6292, 0x35dc, 0x629d, 0x35d5, 0x62a7, 0x35ce, 0x62b2, 0x35c8,
    0x62bc, 0x35c1, 0x62c7, 0x35ba, 0x62d2, 0x35b3, 0x62dc, 0x35ac,
    0x62e7, 0x35a5, 0x62f1, 0x359f, 0x62fc, 0x3598, 0x6306, 0x3591,
    0x6311, 0x358a, 0x631b, 0x3583, 0x6326, 0x357c, 0x6330, 0x3575,
    0x633b, 0x356e, 0x6345, 0x3567, 0x6350, 0x3561, 0x635a, 0x355a,
    0x6365, 0x3553, 0x636f, 0x354c, 0x637a, 0x3545, 0x6384, 0x353e,
    0x638e, 0x3537, 0x6399, 0x3530, 0x63a3, 0x3529, 0x63ae, 0x3522,
    0x63b8, 0x351b, 0x63c3, 0x3514, 0x63cd, 0x350d, 0x63d7, 0x3506,
    0x63e2, 0x34ff, 0x63ec, 0x34f8, 0x63f7, 0x34f1, 0x6401, 0x34ea,
    0x640b, 0x34e2, 0x6416, 0x34db, 0x6420, 0x34d4, 0x642b, 0x34cd,
    0x6435, 0x34c6, 0x643f, 0x34bf, 0x644a, 0x34b8, 0x6454, 0x34b1,
    0x645e, 0x34aa, 0x6469, 0x34a2, 0x6473, 0x349b, 0x647d, 0x3494,
    0x6488, 0x348d, 0x6492, 0x3486, 0x649c, 0x347f, 0x64a7, 0x3477,
    0x64b1, 0x3470, 0x64bb, 0x3469, 0x64c5, 0x3462, 0x64d0, 0x345b,
    0x64da, 0x3453, 0x64e4, 0x344c, 0x64ef, 0x3445, 0x64f9, 0x343e,
    0x6503, 0x3436, 0x650d, 0x342f, 0x6518, 0x3428, 0x6522, 0x3420,
    0x652c, 0x3419, 0x6536, 0x3412, 0x6541, 0x340b, 0x654b, 0x3403,
    0x6555, 0x33fc, 0x655f, 0x33f5, 0x6569, 0x33ed, 0x6574, 0x33e6,
    0x657e, 0x33df, 0x6588, 0x33d7, 0x6592, 0x33d0, 0x659c, 0x33c8,
    0x65a6, 0x33c1, 0x65b1, 0x33ba, 0x65bb, 0x33b2, 0x65c5, 0x33ab,
    0x65cf, 0x33a3, 0x65d9, 0x339c, 0x65e3, 0x3395, 0x65ed, 0x338d,
    0x65f8, 0x3386, 0x6602, 0x337e, 0x660c, 0x3377, 0x6616, 0x336f,
    0x6620, 0x3368, 0x662a, 0x3360, 0x6634, 0x3359, 0x663e, 0x3351,
    0x6648, 0x334a, 0x6652, 0x3342, 0x665c, 0x333b, 0x6666, 0x3333,
    0x6671, 0x332c, 0x667b, 0x3324, 0x6685, 0x331d, 0x668f, 0x3315,
    0x6699, 0x330d, 0x66a3, 0x3306, 0x66ad, 0x32fe, 0x66b7, 0x32f7,
    0x66c1, 0x32ef, 0x66cb, 0x32e7, 0x66d5, 0x32e0, 0x66df, 0x32d8,
    0x66e9, 0x32d0, 0x66f3, 0x32c9, 0x66fd, 0x32c1, 0x6707, 0x32ba,
    0x6711, 0x32b2, 0x671a, 0x32aa, 0x6724, 0x32a3, 0x672e, 0x329b,
    0x6738, 0x3293, 0x6742, 0x328b, 0x674c, 0x3284, 0x6756, 0x327c,
    0x6760, 0x3274, 0x676a, 0x326d, 0x6774, 0x3265, 0x677e, 0x325d,
    0x6788, 0x3255, 0x6791, 0x324e, 0x679b, 0x3246, 0x67a5, 0x323e,
    0x67af, 0x3236, 0x67b9, 0x322e, 0x67c3, 0x3227, 0x67cd, 0x321f,
    0x67d6, 0x3217, 0x67e0, 0x320f, 0x67ea, 0x3207, 0x67f4, 0x31ff,
    0x67fe, 0x31f8, 0x6808, 0x31f0, 0x6811, 0x31e8, 0x681b, 0x31e0,
    0x6825, 0x31d8, 0x682f, 0x31d0, 0x6838, 0x31c8, 0x6842, 0x31c0,
    0x684c, 0x31b9, 0x6856, 0x31b1, 0x6860, 0x31a9, 0x6869, 0x31a1,
    0x6873, 0x3199, 0x687d, 0x3191, 0x6886, 0x3189, 0x6890, 0x3181,
    0x689a, 0x3179, 0x68a4, 0x3171, 0x68ad, 0x3169, 0x68b7, 0x3161,
    0x68c1, 0x3159, 0x68ca, 0x3151, 0x68d4, 0x3149, 0x68de, 0x3141,
    0x68e7, 0x3139, 0x68f1, 0x3131, 0x68fb, 0x3129, 0x6904, 0x3121,
    0x690e, 0x3119, 0x6918, 0x3111, 0x6921, 0x3109, 0x692b, 0x3101,
    0x6935, 0x30f9, 0x693e, 0x30f0, 0x6948, 0x30e8, 0x6951, 0x30e0,
    0x695b, 0x30d8, 0x6965, 0x30d0, 0x696e, 0x30c8, 0x6978, 0x30c0,
    0x6981, 0x30b8, 0x698b, 0x30af, 0x6994, 0x30a7, 0x699e, 0x309f,
    0x69a7, 0x3097, 0x69b1, 0x308f, 0x69bb, 0x3087, 0x69c4, 0x307e,
    0x69ce, 0x3076, 0x69d7, 0x306e, 0x69e1, 0x3066, 0x69ea, 0x305d,
    0x69f4, 0x3055, 0x69fd, 0x304d, 0x6a07, 0x3045, 0x6a10, 0x303c,
    0x6a1a, 0x3034, 0x6a23, 0x302c, 0x6a2c, 0x3024, 0x6a36, 0x301b,
    0x6a3f, 0x3013, 0x6a49, 0x300b, 0x6a52, 0x3002, 0x6a5c, 0x2ffa,
    0x6a65, 0x2ff2, 0x6a6e, 0x2fea, 0x6a78, 0x2fe1, 0x6a81, 0x2fd9,
    0x6a8b, 0x2fd0, 0x6a94, 0x2fc8, 0x6a9d, 0x2fc0, 0x6aa7, 0x2fb7,
    0x6ab0, 0x2faf, 0x6ab9, 0x2fa7, 0x6ac3, 0x2f9e, 0x6acc, 0x2f96,
    0x6ad6, 0x2f8d, 0x6adf, 0x2f85, 0x6ae8, 0x2f7d, 0x6af2, 0x2f74,
    0x6afb, 0x2f6c, 0x6b04, 0x2f63, 0x6b0d, 0x2f5b, 0x6b17, 0x2f52,
    0x6b20, 0x2f4a, 0x6b29, 0x2f41, 0x6b33, 0x2f39, 0x6b3c, 0x2f30,
    0x6b45, 0x2f28, 0x6b4e, 0x2f20, 0x6b58, 0x2f17, 0x6b61, 0x2f0e,
    0x6b6a, 0x2f06, 0x6b73, 0x2efd, 0x6b7d, 0x2ef5, 0x6b86, 0x2eec,
    0x6b8f, 0x2ee4, 0x6b98, 0x2edb, 0x6ba1, 0x2ed3, 0x6bab, 0x2eca,
    0x6bb4, 0x2ec2, 0x6bbd, 0x2eb9, 0x6bc6, 0x2eb0, 0x6bcf, 0x2ea8,
    0x6bd8, 0x2e9f, 0x6be2, 0x2e97, 0x6beb, 0x2e8e, 0x6bf4, 0x2e85,
    0x6bfd, 0x2e7d, 0x6c06, 0x2e74, 0x6c0f, 0x2e6b, 0x6c18, 0x2e63,
    0x6c21, 0x2e5a, 0x6c2b, 0x2e51, 0x6c34, 0x2e49, 0x6c3d, 0x2e40,
    0x6c46, 0x2e37, 0x6c4f, 0x2e2f, 0x6c58, 0x2e26, 0x6c61, 0x2e1d,
    0x6c6a, 0x2e15, 0x6c73, 0x2e0c, 0x6c7c, 0x2e03, 0x6c85, 0x2dfa,
    0x6c8e, 0x2df2, 0x6c97, 0x2de9, 0x6ca0, 0x2de0, 0x6ca9, 0x2dd7,
    0x6cb2, 0x2dcf, 0x6cbb, 0x2dc6, 0x6cc4, 0x2dbd, 0x6ccd, 0x2db4,
    0x6cd6, 0x2dab, 0x6cdf, 0x2da3, 0x6ce8, 0x2d9a, 0x6cf1, 0x2d91,
    0x6cfa, 0x2d88, 0x6d03, 0x2d7f, 0x6d0c, 0x2d76, 0x6d15, 0x2d6e,
    0x6d1e, 0x2d65, 0x6d27, 0x2d5c, 0x6d2f, 0x2d53, 0x6d38, 0x2d4a,
    0x6d41, 0x2d41, 0x6d4a, 0x2d38, 0x6d53, 0x2d2f, 0x6d5c, 0x2d27,
    0x6d65, 0x2d1e, 0x6d6e, 0x2d15, 0x6d76, 0x2d0c, 0x6d7f, 0x2d03,
    0x6d88, 0x2cfa, 0x6d91, 0x2cf1, 0x6d9a, 0x2ce8, 0x6da3, 0x2cdf,
    0x6dab, 0x2cd6, 0x6db4, 0x2ccd, 0x6dbd, 0x2cc4, 0x6dc6, 0x2cbb,
    0x6dcf, 0x2cb2, 0x6dd7, 0x2ca9, 0x6de0, 0x2ca0, 0x6de9, 0x2c97,
    0x6df2, 0x2c8e, 0x6dfa, 0x2c85, 0x6e03, 0x2c7c, 0x6e0c, 0x2c73,
    0x6e15, 0x2c6a, 0x6e1d, 0x2c61, 0x6e26, 0x2c58, 0x6e2f, 0x2c4f,
    0x6e37, 0x2c46, 0x6e40, 0x2c3d, 0x6e49, 0x2c34, 0x6e51, 0x2c2b,
    0x6e5a, 0x2c21, 0x6e63, 0x2c18, 0x6e6b, 0x2c0f, 0x6e74, 0x2c06,
    0x6e7d, 0x2bfd, 0x6e85, 0x2bf4, 0x6e8e, 0x2beb, 0x6e97, 0x2be2,
    0x6e9f, 0x2bd8, 0x6ea8, 0x2bcf, 0x6eb0, 0x2bc6, 0x6eb9, 0x2bbd,
    0x6ec2, 0x2bb4, 0x6eca, 0x2bab, 0x6ed3, 0x2ba1, 0x6edb, 0x2b98,
    0x6ee4, 0x2b8f, 0x6eec, 0x2b86, 0x6ef5, 0x2b7d, 0x6efd, 0x2b73,
    0x6f06, 0x2b6a, 0x6f0e, 0x2b61, 0x6f17, 0x2b58, 0x6f20, 0x2b4e,
    0x6f28, 0x2b45, 0x6f30, 0x2b3c, 0x6f39, 0x2b33, 0x6f41, 0x2b29,
    0x6f4a, 0x2b20, 0x6f52, 0x2b17, 0x6f5b, 0x2b0d, 0x6f63, 0x2b04,
    0x6f6c, 0x2afb, 0x6f74, 0x2af2, 0x6f7d, 0x2ae8, 0x6f85, 0x2adf,
    0x6f8d, 0x2ad6, 0x6f96, 0x2acc, 0x6f9e, 0x2ac3, 0x6fa7, 0x2ab9,
    0x6faf, 0x2ab0, 0x6fb7, 0x2aa7, 0x6fc0, 0x2a9d, 0x6fc8, 0x2a94,
    0x6fd0, 0x2a8b, 0x6fd9, 0x2a81, 0x6fe1, 0x2a78, 0x6fea, 0x2a6e,
    0x6ff2, 0x2a65, 0x6ffa, 0x2a5c, 0x7002, 0x2a52, 0x700b, 0x2a49,
    0x7013, 0x2a3f, 0x701b, 0x2a36, 0x7024, 0x2a2c, 0x702c, 0x2a23,
    0x7034, 0x2a1a, 0x703c, 0x2a10, 0x7045, 0x2a07, 0x704d, 0x29fd,
    0x7055, 0x29f4, 0x705d, 0x29ea, 0x7066, 0x29e1, 0x706e, 0x29d7,
    0x7076, 0x29ce, 0x707e, 0x29c4, 0x7087, 0x29bb, 0x708f, 0x29b1,
    0x7097, 0x29a7, 0x709f, 0x299e, 0x70a7, 0x2994, 0x70af, 0x298b,
    0x70b8, 0x2981, 0x70c0, 0x2978, 0x70c8, 0x296e, 0x70d0, 0x2965,
    0x70d8, 0x295b, 0x70e0, 0x2951, 0x70e8, 0x2948, 0x70f0, 0x293e,
    0x70f9, 0x2935, 0x7101, 0x292b, 0x7109, 0x2921, 0x7111, 0x2918,
    0x7119, 0x290e, 0x7121, 0x2904, 0x7129, 0x28fb, 0x7131, 0x28f1,
    0x7139, 0x28e7, 0x7141, 0x28de, 0x7149, 0x28d4, 0x7151, 0x28ca,
    0x7159, 0x28c1, 0x7161, 0x28b7, 0x7169, 0x28ad, 0x7171, 0x28a4,
    0x7179, 0x289a, 0x7181, 0x2890, 0x7189, 0x2886, 0x7191, 0x287d,
    0x7199, 0x2873, 0x71a1, 0x2869, 0x71a9, 0x2860, 0x71b1, 0x2856,
    0x71b9, 0x284c, 0x71c0, 0x2842, 0x71c8, 0x2838, 0x71d0, 0x282f,
    0x71d8, 0x2825, 0x71e0, 0x281b, 0x71e8, 0x2811, 0x71f0, 0x2808,
    0x71f8, 0x27fe, 0x71ff, 0x27f4, 0x7207, 0x27ea, 0x720f, 0x27e0,
    0x7217, 0x27d6, 0x721f, 0x27cd, 0x7227, 0x27c3, 0x722e, 0x27b9,
    0x7236, 0x27af, 0x723e, 0x27a5, 0x7246, 0x279b, 0x724e, 0x2791,
    0x7255, 0x2788, 0x725d, 0x277e, 0x7265, 0x2774, 0x726d, 0x276a,
    0x7274, 0x2760, 0x727c, 0x2756, 0x7284, 0x274c, 0x728b, 0x2742,
    0x7293, 0x2738, 0x729b, 0x272e, 0x72a3, 0x2724, 0x72aa, 0x271a,
    0x72b2, 0x2711, 0x72ba, 0x2707, 0x72c1, 0x26fd, 0x72c9, 0x26f3,
    0x72d0, 0x26e9, 0x72d8, 0x26df, 0x72e0, 0x26d5, 0x72e7, 0x26cb,
    0x72ef, 0x26c1, 0x72f7, 0x26b7, 0x72fe, 0x26ad, 0x7306, 0x26a3,
    0x730d, 0x2699, 0x7315, 0x268f, 0x731d, 0x2685, 0x7324, 0x267b,
    0x732c, 0x2671, 0x7333, 0x2666, 0x733b, 0x265c, 0x7342, 0x2652,
    0x734a, 0x2648, 0x7351, 0x263e, 0x7359, 0x2634, 0x7360, 0x262a,
    0x7368, 0x2620, 0x736f, 0x2616, 0x7377, 0x260c, 0x737e, 0x2602,
    0x7386, 0x25f8, 0x738d, 0x25ed, 0x7395, 0x25e3, 0x739c, 0x25d9,
    0x73a3, 0x25cf, 0x73ab, 0x25c5, 0x73b2, 0x25bb, 0x73ba, 0x25b1,
    0x73c1, 0x25a6, 0x73c8, 0x259c, 0x73d0, 0x2592, 0x73d7, 0x2588,
    0x73df, 0x257e, 0x73e6, 0x2574, 0x73ed, 0x2569, 0x73f5, 0x255f,
    0x73fc, 0x2555, 0x7403, 0x254b, 0x740b, 0x2541, 0x7412, 0x2536,
    0x7419, 0x252c, 0x7420, 0x2522, 0x7428, 0x2518, 0x742f, 0x250d,
    0x7436, 0x2503, 0x743e, 0x24f9, 0x7445, 0x24ef, 0x744c, 0x24e4,
    0x7453, 0x24da, 0x745b, 0x24d0, 0x7462, 0x24c5, 0x7469, 0x24bb,
    0x7470, 0x24b1, 0x7477, 0x24a7, 0x747f, 0x249c, 0x7486, 0x2492,
    0x748d, 0x2488, 0x7494, 0x247d, 0x749b, 0x2473, 0x74a2, 0x2469,
    0x74aa, 0x245e, 0x74b1, 0x2454, 0x74b8, 0x244a, 0x74bf, 0x243f,
    0x74c6, 0x2435, 0x74cd, 0x242b, 0x74d4, 0x2420, 0x74db, 0x2416,
    0x74e2, 0x240b, 0x74ea, 0x2401, 0x74f1, 0x23f7, 0x74f8, 0x23ec,
    0x74ff, 0x23e2, 0x7506, 0x23d7, 0x750d, 0x23cd, 0x7514, 0x23c3,
    0x751b, 0x23b8, 0x7522, 0x23ae, 0x7529, 0x23a3, 0x7530, 0x2399,
    0x7537, 0x238e, 0x753e, 0x2384, 0x7545, 0x237a, 0x754c, 0x236f,
    0x7553, 0x2365, 0x755a, 0x235a, 0x7561, 0x2350, 0x7567, 0x2345,
    0x756e, 0x233b, 0x7575, 0x2330, 0x757c, 0x2326, 0x7583, 0x231b,
    0x758a, 0x2311, 0x7591, 0x2306, 0x7598, 0x22fc, 0x759f, 0x22f1,
    0x75a5, 0x22e7, 0x75ac, 0x22dc, 0x75b3, 0x22d2, 0x75ba, 0x22c7,
    0x75c1, 0x22bc, 0x75c8, 0x22b2, 0x75ce, 0x22a7, 0x75d5, 0x229d,
    0x75dc, 0x2292, 0x75e3, 0x2288, 0x75ea, 0x227d, 0x75f0, 0x2272,
    0x75f7, 0x2268, 0x75fe, 0x225d, 0x7605, 0x2253, 0x760b, 0x2248,
    0x7612, 0x223d, 0x7619, 0x2233, 0x7620, 0x2228, 0x7626, 0x221e,
    0x762d, 0x2213, 0x7634, 0x2208, 0x763a, 0x21fe, 0x7641, 0x21f3,
    0x7648, 0x21e8, 0x764e, 0x21de, 0x7655, 0x21d3, 0x765c, 0x21c8,
    0x7662, 0x21be, 0x7669, 0x21b3, 0x766f, 0x21a8, 0x7676, 0x219e,
    0x767d, 0x2193, 0x7683, 0x2188, 0x768a, 0x217d, 0x7690, 0x2173,
    0x7697, 0x2168, 0x769d, 0x215d, 0x76a4, 0x2153, 0x76ab, 0x2148,
    0x76b1, 0x213d, 0x76b8, 0x2132, 0x76be, 0x2128, 0x76c5, 0x211d,
    0x76cb, 0x2112, 0x76d2, 0x2107, 0x76d8, 0x20fd, 0x76df, 0x20f2,
    0x76e5, 0x20e7, 0x76eb, 0x20dc, 0x76f2, 0x20d1, 0x76f8, 0x20c7,
    0x76ff, 0x20bc, 0x7705, 0x20b1, 0x770c, 0x20a6, 0x7712, 0x209b,
    0x7718, 0x2091, 0x771f, 0x2086, 0x7725, 0x207b, 0x772c, 0x2070,
    0x7732, 0x2065, 0x7738, 0x205b, 0x773f, 0x2050, 0x7745, 0x2045,
    0x774b, 0x203a, 0x7752, 0x202f, 0x7758, 0x2024, 0x775e, 0x2019,
    0x7765, 0x200f, 0x776b, 0x2004, 0x7771, 0x1ff9, 0x7777, 0x1fee,
    0x777e, 0x1fe3, 0x7784, 0x1fd8, 0x778a, 0x1fcd, 0x7790, 0x1fc2,
    0x7797, 0x1fb7, 0x779d, 0x1fac, 0x77a3, 0x1fa2, 0x77a9, 0x1f97,
    0x77b0, 0x1f8c, 0x77b6, 0x1f81, 0x77bc, 0x1f76, 0x77c2, 0x1f6b,
    0x77c8, 0x1f60, 0x77ce, 0x1f55, 0x77d5, 0x1f4a, 0x77db, 0x1f3f,
    0x77e1, 0x1f34, 0x77e7, 0x1f29, 0x77ed, 0x1f1e, 0x77f3, 0x1f13,
    0x77f9, 0x1f08, 0x77ff, 0x1efd, 0x7805, 0x1ef2, 0x780b, 0x1ee7,
    0x7812, 0x1edc, 0x7818, 0x1ed1, 0x781e, 0x1ec6, 0x7824, 0x1ebb,
    0x782a, 0x1eb0, 0x7830, 0x1ea5, 0x7836, 0x1e9a, 0x783c, 0x1e8f,
    0x7842, 0x1e84, 0x7848, 0x1e79, 0x784e, 0x1e6e, 0x7854, 0x1e63,
    0x785a, 0x1e58, 0x7860, 0x1e4d, 0x7866, 0x1e42, 0x786b, 0x1e36,
    0x7871, 0x1e2b, 0x7877, 0x1e20, 0x787d, 0x1e15, 0x7883, 0x1e0a,
    0x7889, 0x1dff, 0x788f, 0x1df4, 0x7895, 0x1de9, 0x789b, 0x1dde,
    0x78a1, 0x1dd3, 0x78a6, 0x1dc7, 0x78ac, 0x1dbc, 0x78b2, 0x1db1,
    0x78b8, 0x1da6, 0x78be, 0x1d9b, 0x78c3, 0x1d90, 0x78c9, 0x1d85,
    0x78cf, 0x1d79, 0x78d5, 0x1d6e, 0x78db, 0x1d63, 0x78e0, 0x1d58,
    0x78e6, 0x1d4d, 0x78ec, 0x1d42, 0x78f2, 0x1d36, 0x78f7, 0x1d2b,
    0x78fd, 0x1d20, 0x7903, 0x1d15, 0x7909, 0x1d0a, 0x790e, 0x1cff,
    0x7914, 0x1cf3, 0x791a, 0x1ce8, 0x791f, 0x1cdd, 0x7925, 0x1cd2,
    0x792b, 0x1cc6, 0x7930, 0x1cbb, 0x7936, 0x1cb0, 0x793b, 0x1ca5,
    0x7941, 0x1c99, 0x7947, 0x1c8e, 0x794c, 0x1c83, 0x7952, 0x1c78,
    0x7958, 0x1c6c, 0x795d, 0x1c61, 0x7963, 0x1c56, 0x7968, 0x1c4b,
    0x796e, 0x1c3f, 0x7973, 0x1c34, 0x7979, 0x1c29, 0x797e, 0x1c1e,
    0x7984, 0x1c12, 0x7989, 0x1c07, 0x798f, 0x1bfc, 0x7994, 0x1bf0,
    0x799a, 0x1be5, 0x799f, 0x1bda, 0x79a5, 0x1bce, 0x79aa, 0x1bc3,
    0x79b0, 0x1bb8, 0x79b5, 0x1bac, 0x79bb, 0x1ba1, 0x79c0, 0x1b96,
    0x79c5, 0x1b8a, 0x79cb, 0x1b7f, 0x79d0, 0x1b74, 0x79d6, 0x1b68,
    0x79db, 0x1b5d, 0x79e0, 0x1b52, 0x79e6, 0x1b46, 0x79eb, 0x1b3b,
    0x79f0, 0x1b30, 0x79f6, 0x1b24, 0x79fb, 0x1b19, 0x7a00, 0x1b0d,
    0x7a06, 0x1b02, 0x7a0b, 0x1af7, 0x7a10, 0x1aeb, 0x7a16, 0x1ae0,
    0x7a1b, 0x1ad4, 0x7a20, 0x1ac9, 0x7a25, 0x1abe, 0x7a2b, 0x1ab2,
    0x7a30, 0x1aa7, 0x7a35, 0x1a9b, 0x7a3a, 0x1a90, 0x7a3f, 0x1a84,
    0x7a45, 0x1a79, 0x7a4a, 0x1a6e, 0x7a4f, 0x1a62, 0x7a54, 0x1a57,
    0x7a59, 0x1a4b, 0x7a5f, 0x1a40, 0x7a64, 0x1a34, 0x7a69, 0x1a29,
    0x7a6e, 0x1a1d, 0x7a73, 0x1a12, 0x7a78, 0x1a06, 0x7a7d, 0x19fb,
    0x7a82, 0x19ef, 0x7a88, 0x19e4, 0x7a8d, 0x19d8, 0x7a92, 0x19cd,
    0x7a97, 0x19c1, 0x7a9c, 0x19b6, 0x7aa1, 0x19aa, 0x7aa6, 0x199f,
    0x7aab, 0x1993, 0x7ab0, 0x1988, 0x7ab5, 0x197c, 0x7aba, 0x1971,
    0x7abf, 0x1965, 0x7ac4, 0x195a, 0x7ac9, 0x194e, 0x7ace, 0x1943,
    0x7ad3, 0x1937, 0x7ad8, 0x192c, 0x7add, 0x1920, 0x7ae2, 0x1914,
    0x7ae6, 0x1909, 0x7aeb, 0x18fd, 0x7af0, 0x18f2, 0x7af5, 0x18e6,
    0x7afa, 0x18db, 0x7aff, 0x18cf, 0x7b04, 0x18c3, 0x7b09, 0x18b8,
    0x7b0e, 0x18ac, 0x7b12, 0x18a1, 0x7b17, 0x1895, 0x7b1c, 0x1889,
    0x7b21, 0x187e, 0x7b26, 0x1872, 0x7b2a, 0x1867, 0x7b2f, 0x185b,
    0x7b34, 0x184f, 0x7b39, 0x1844, 0x7b3e, 0x1838, 0x7b42, 0x182d,
    0x7b47, 0x1821, 0x7b4c, 0x1815, 0x7b50, 0x180a, 0x7b55, 0x17fe,
    0x7b5a, 0x17f2, 0x7b5f, 0x17e7, 0x7b63, 0x17db, 0x7b68, 0x17cf,
    0x7b6d, 0x17c4, 0x7b71, 0x17b8, 0x7b76, 0x17ac, 0x7b7b, 0x17a1,
    0x7b7f, 0x1795, 0x7b84, 0x1789, 0x7b88, 0x177e, 0x7b8d, 0x1772,
    0x7b92, 0x1766, 0x7b96, 0x175b, 0x7b9b, 0x174f, 0x7b9f, 0x1743,
    0x7ba4, 0x1737, 0x7ba9, 0x172c, 0x7bad, 0x1720, 0x7bb2, 0x1714,
    0x7bb6, 0x1709, 0x7bbb, 0x16fd, 0x7bbf, 0x16f1, 0x7bc4, 0x16e5,
    0x7bc8, 0x16da, 0x7bcd, 0x16ce, 0x7bd1, 0x16c2, 0x7bd6, 0x16b6,
    0x7bda, 0x16ab, 0x7bde, 0x169f, 0x7be3, 0x1693, 0x7be7, 0x1687,
    0x7bec, 0x167c, 0x7bf0, 0x1670, 0x7bf5, 0x1664, 0x7bf9, 0x1658,
    0x7bfd, 0x164c, 0x7c02, 0x1641, 0x7c06, 0x1635, 0x7c0a, 0x1629,
    0x7c0f, 0x161d, 0x7c13, 0x1612, 0x7c17, 0x1606, 0x7c1c, 0x15fa,
    0x7c20, 0x15ee, 0x7c24, 0x15e2, 0x7c29, 0x15d7, 0x7c2d, 0x15cb,
    0x7c31, 0x15bf, 0x7c36, 0x15b3, 0x7c3a, 0x15a7, 0x7c3e, 0x159b,
    0x7c42, 0x1590, 0x7c46, 0x1584, 0x7c4b, 0x1578, 0x7c4f, 0x156c,
    0x7c53, 0x1560, 0x7c57, 0x1554, 0x7c5b, 0x1549, 0x7c60, 0x153d,
    0x7c64, 0x1531, 0x7c68, 0x1525, 0x7c6c, 0x1519, 0x7c70, 0x150d,
    0x7c74, 0x1501, 0x7c79, 0x14f6, 0x7c7d, 0x14ea, 0x7c81, 0x14de,
    0x7c85, 0x14d2, 0x7c89, 0x14c6, 0x7c8d, 0x14ba, 0x7c91, 0x14ae,
    0x7c95, 0x14a2, 0x7c99, 0x1496, 0x7c9d, 0x148b, 0x7ca1, 0x147f,
    0x7ca5, 0x1473, 0x7ca9, 0x1467, 0x7cad, 0x145b, 0x7cb1, 0x144f,
    0x7cb5, 0x1443, 0x7cb9, 0x1437, 0x7cbd, 0x142b, 0x7cc1, 0x141f,
    0x7cc5, 0x1413, 0x7cc9, 0x1407, 0x7ccd, 0x13fb, 0x7cd1, 0x13f0,
    0x7cd5, 0x13e4, 0x7cd9, 0x13d8, 0x7cdd, 0x13cc, 0x7ce0, 0x13c0,
    0x7ce4, 0x13b4, 0x7ce8, 0x13a8, 0x7cec, 0x139c, 0x7cf0, 0x1390,
    0x7cf4, 0x1384, 0x7cf8, 0x1378, 0x7cfb, 0x136c, 0x7cff, 0x1360,
    0x7d03, 0x1354, 0x7d07, 0x1348, 0x7d0b, 0x133c, 0x7d0e, 0x1330,
    0x7d12, 0x1324, 0x7d16, 0x1318, 0x7d1a, 0x130c, 0x7d1d, 0x1300,
    0x7d21, 0x12f4, 0x7d25, 0x12e8, 0x7d28, 0x12dc, 0x7d2c, 0x12d0,
    0x7d30, 0x12c4, 0x7d34, 0x12b8, 0x7d37, 0x12ac, 0x7d3b, 0x12a0,
    0x7d3f, 0x1294, 0x7d42, 0x1288, 0x7d46, 0x127c, 0x7d49, 0x1270,
    0x7d4d, 0x1264, 0x7d51, 0x1258, 0x7d54, 0x124c, 0x7d58, 0x1240,
    0x7d5b, 0x1234, 0x7d5f, 0x1228, 0x7d63, 0x121c, 0x7d66, 0x1210,
    0x7d6a, 0x1204, 0x7d6d, 0x11f7, 0x7d71, 0x11eb, 0x7d74, 0x11df,
    0x7d78, 0x11d3, 0x7d7b, 0x11c7, 0x7d7f, 0x11bb, 0x7d82, 0x11af,
    0x7d86, 0x11a3, 0x7d89, 0x1197, 0x7d8d, 0x118b, 0x7d90, 0x117f,
    0x7d93, 0x1173, 0x7d97, 0x1167, 0x7d9a, 0x115a, 0x7d9e, 0x114e,
    0x7da1, 0x1142, 0x7da4, 0x1136, 0x7da8, 0x112a, 0x7dab, 0x111e,
    0x7daf, 0x1112, 0x7db2, 0x1106, 0x7db5, 0x10fa, 0x7db9, 0x10ed,
    0x7dbc, 0x10e1, 0x7dbf, 0x10d5, 0x7dc2, 0x10c9, 0x7dc6, 0x10bd,
    0x7dc9, 0x10b1, 0x7dcc, 0x10a5, 0x7dd0, 0x1099, 0x7dd3, 0x108c,
    0x7dd6, 0x1080, 0x7dd9, 0x1074, 0x7ddd, 0x1068, 0x7de0, 0x105c,
    0x7de3, 0x1050, 0x7de6, 0x1044, 0x7de9, 0x1037, 0x7ded, 0x102b,
    0x7df0, 0x101f, 0x7df3, 0x1013, 0x7df6, 0x1007, 0x7df9, 0xffb,
    0x7dfc, 0xfee, 0x7dff, 0xfe2, 0x7e03, 0xfd6, 0x7e06, 0xfca,
    0x7e09, 0xfbe, 0x7e0c, 0xfb2, 0x7e0f, 0xfa5, 0x7e12, 0xf99,
    0x7e15, 0xf8d, 0x7e18, 0xf81, 0x7e1b, 0xf75, 0x7e1e, 0xf68,
    0x7e21, 0xf5c, 0x7e24, 0xf50, 0x7e27, 0xf44, 0x7e2a, 0xf38,
    0x7e2d, 0xf2b, 0x7e30, 0xf1f, 0x7e33, 0xf13, 0x7e36, 0xf07,
    0x7e39, 0xefb, 0x7e3c, 0xeee, 0x7e3f, 0xee2, 0x7e42, 0xed6,
    0x7e45, 0xeca, 0x7e48, 0xebd, 0x7e4a, 0xeb1, 0x7e4d, 0xea5,
    0x7e50, 0xe99, 0x7e53, 0xe8c, 0x7e56, 0xe80, 0x7e59, 0xe74,
    0x7e5c, 0xe68, 0x7e5e, 0xe5c, 0x7e61, 0xe4f, 0x7e64, 0xe43,
    0x7e67, 0xe37, 0x7e6a, 0xe2b, 0x7e6c, 0xe1e, 0x7e6f, 0xe12,
    0x7e72, 0xe06, 0x7e75, 0xdf9, 0x7e77, 0xded, 0x7e7a, 0xde1,
    0x7e7d, 0xdd5, 0x7e80, 0xdc8, 0x7e82, 0xdbc, 0x7e85, 0xdb0,
    0x7e88, 0xda4, 0x7e8a, 0xd97, 0x7e8d, 0xd8b, 0x7e90, 0xd7f,
    0x7e92, 0xd72, 0x7e95, 0xd66, 0x7e98, 0xd5a, 0x7e9a, 0xd4e,
    0x7e9d, 0xd41, 0x7e9f, 0xd35, 0x7ea2, 0xd29, 0x7ea5, 0xd1c,
    0x7ea7, 0xd10, 0x7eaa, 0xd04, 0x7eac, 0xcf8, 0x7eaf, 0xceb,
    0x7eb1, 0xcdf, 0x7eb4, 0xcd3, 0x7eb6, 0xcc6, 0x7eb9, 0xcba,
    0x7ebb, 0xcae, 0x7ebe, 0xca1, 0x7ec0, 0xc95, 0x7ec3, 0xc89,
    0x7ec5, 0xc7c, 0x7ec8, 0xc70, 0x7eca, 0xc64, 0x7ecc, 0xc57,
    0x7ecf, 0xc4b, 0x7ed1, 0xc3f, 0x7ed4, 0xc32, 0x7ed6, 0xc26,
    0x7ed8, 0xc1a, 0x7edb, 0xc0d, 0x7edd, 0xc01, 0x7ee0, 0xbf5,
    0x7ee2, 0xbe8, 0x7ee4, 0xbdc, 0x7ee7, 0xbd0, 0x7ee9, 0xbc3,
    0x7eeb, 0xbb7, 0x7eed, 0xbab, 0x7ef0, 0xb9e, 0x7ef2, 0xb92,
    0x7ef4, 0xb85, 0x7ef7, 0xb79, 0x7ef9, 0xb6d, 0x7efb, 0xb60,
    0x7efd, 0xb54, 0x7f00, 0xb48, 0x7f02, 0xb3b, 0x7f04, 0xb2f,
    0x7f06, 0xb23, 0x7f08, 0xb16, 0x7f0a, 0xb0a, 0x7f0d, 0xafd,
    0x7f0f, 0xaf1, 0x7f11, 0xae5, 0x7f13, 0xad8, 0x7f15, 0xacc,
    0x7f17, 0xac0, 0x7f19, 0xab3, 0x7f1c, 0xaa7, 0x7f1e, 0xa9a,
    0x7f20, 0xa8e, 0x7f22, 0xa82, 0x7f24, 0xa75, 0x7f26, 0xa69,
    0x7f28, 0xa5c, 0x7f2a, 0xa50, 0x7f2c, 0xa44, 0x7f2e, 0xa37,
    0x7f30, 0xa2b, 0x7f32, 0xa1e, 0x7f34, 0xa12, 0x7f36, 0xa06,
    0x7f38, 0x9f9, 0x7f3a, 0x9ed, 0x7f3c, 0x9e0, 0x7f3e, 0x9d4,
    0x7f40, 0x9c7, 0x7f42, 0x9bb, 0x7f43, 0x9af, 0x7f45, 0x9a2,
    0x7f47, 0x996, 0x7f49, 0x989, 0x7f4b, 0x97d, 0x7f4d, 0x970,
    0x7f4f, 0x964, 0x7f51, 0x958, 0x7f52, 0x94b, 0x7f54, 0x93f,
    0x7f56, 0x932, 0x7f58, 0x926, 0x7f5a, 0x919, 0x7f5b, 0x90d,
    0x7f5d, 0x901, 0x7f5f, 0x8f4, 0x7f61, 0x8e8, 0x7f62, 0x8db,
    0x7f64, 0x8cf, 0x7f66, 0x8c2, 0x7f68, 0x8b6, 0x7f69, 0x8a9,
    0x7f6b, 0x89d, 0x7f6d, 0x891, 0x7f6e, 0x884, 0x7f70, 0x878,
    0x7f72, 0x86b, 0x7f73, 0x85f, 0x7f75, 0x852, 0x7f77, 0x846,
    0x7f78, 0x839, 0x7f7a, 0x82d, 0x7f7b, 0x820, 0x7f7d, 0x814,
    0x7f7f, 0x807, 0x7f80, 0x7fb, 0x7f82, 0x7ef, 0x7f83, 0x7e2,
    0x7f85, 0x7d6, 0x7f86, 0x7c9, 0x7f88, 0x7bd, 0x7f89, 0x7b0,
    0x7f8b, 0x7a4, 0x7f8c, 0x797, 0x7f8e, 0x78b, 0x7f8f, 0x77e,
    0x7f91, 0x772, 0x7f92, 0x765, 0x7f94, 0x759, 0x7f95, 0x74c,
    0x7f97, 0x740, 0x7f98, 0x733, 0x7f99, 0x727, 0x7f9b, 0x71a,
    0x7f9c, 0x70e, 0x7f9e, 0x701, 0x7f9f, 0x6f5, 0x7fa0, 0x6e8,
    0x7fa2, 0x6dc, 0x7fa3, 0x6cf, 0x7fa4, 0x6c3, 0x7fa6, 0x6b6,
    0x7fa7, 0x6aa, 0x7fa8, 0x69d, 0x7faa, 0x691, 0x7fab, 0x684,
    0x7fac, 0x678, 0x7fad, 0x66b, 0x7faf, 0x65f, 0x7fb0, 0x652,
    0x7fb1, 0x646, 0x7fb2, 0x639, 0x7fb4, 0x62d, 0x7fb5, 0x620,
    0x7fb6, 0x614, 0x7fb7, 0x607, 0x7fb8, 0x5fb, 0x7fb9, 0x5ee,
    0x7fbb, 0x5e2, 0x7fbc, 0x5d5, 0x7fbd, 0x5c9, 0x7fbe, 0x5bc,
    0x7fbf, 0x5b0, 0x7fc0, 0x5a3, 0x7fc1, 0x597, 0x7fc3, 0x58a,
    0x7fc4, 0x57e, 0x7fc5, 0x571, 0x7fc6, 0x565, 0x7fc7, 0x558,
    0x7fc8, 0x54c, 0x7fc9, 0x53f, 0x7fca, 0x533, 0x7fcb, 0x526,
    0x7fcc, 0x51a, 0x7fcd, 0x50d, 0x7fce, 0x500, 0x7fcf, 0x4f4,
    0x7fd0, 0x4e7, 0x7fd1, 0x4db, 0x7fd2, 0x4ce, 0x7fd3, 0x4c2,
    0x7fd4, 0x4b5, 0x7fd5, 0x4a9, 0x7fd5, 0x49c, 0x7fd6, 0x490,
    0x7fd7, 0x483, 0x7fd8, 0x477, 0x7fd9, 0x46a, 0x7fda, 0x45e,
    0x7fdb, 0x451, 0x7fdc, 0x444, 0x7fdc, 0x438, 0x7fdd, 0x42b,
    0x7fde, 0x41f, 0x7fdf, 0x412, 0x7fe0, 0x406, 0x7fe0, 0x3f9,
    0x7fe1, 0x3ed, 0x7fe2, 0x3e0, 0x7fe3, 0x3d4, 0x7fe3, 0x3c7,
    0x7fe4, 0x3bb, 0x7fe5, 0x3ae, 0x7fe6, 0x3a1, 0x7fe6, 0x395,
    0x7fe7, 0x388, 0x7fe8, 0x37c, 0x7fe8, 0x36f, 0x7fe9, 0x363,
    0x7fea, 0x356, 0x7fea, 0x34a, 0x7feb, 0x33d, 0x7fec, 0x330,
    0x7fec, 0x324, 0x7fed, 0x317, 0x7fed, 0x30b, 0x7fee, 0x2fe,
    0x7fef, 0x2f2, 0x7fef, 0x2e5, 0x7ff0, 0x2d9, 0x7ff0, 0x2cc,
    0x7ff1, 0x2c0, 0x7ff1, 0x2b3, 0x7ff2, 0x2a6, 0x7ff2, 0x29a,
    0x7ff3, 0x28d, 0x7ff3, 0x281, 0x7ff4, 0x274, 0x7ff4, 0x268,
    0x7ff5, 0x25b, 0x7ff5, 0x24e, 0x7ff6, 0x242, 0x7ff6, 0x235,
    0x7ff7, 0x229, 0x7ff7, 0x21c, 0x7ff7, 0x210, 0x7ff8, 0x203,
    0x7ff8, 0x1f7, 0x7ff9, 0x1ea, 0x7ff9, 0x1dd, 0x7ff9, 0x1d1,
    0x7ffa, 0x1c4, 0x7ffa, 0x1b8, 0x7ffa, 0x1ab, 0x7ffb, 0x19f,
    0x7ffb, 0x192, 0x7ffb, 0x186, 0x7ffc, 0x179, 0x7ffc, 0x16c,
    0x7ffc, 0x160, 0x7ffc, 0x153, 0x7ffd, 0x147, 0x7ffd, 0x13a,
    0x7ffd, 0x12e, 0x7ffd, 0x121, 0x7ffe, 0x114, 0x7ffe, 0x108,
    0x7ffe, 0xfb, 0x7ffe, 0xef, 0x7ffe, 0xe2, 0x7fff, 0xd6,
    0x7fff, 0xc9, 0x7fff, 0xbc, 0x7fff, 0xb0, 0x7fff, 0xa3,
    0x7fff, 0x97, 0x7fff, 0x8a, 0x7fff, 0x7e, 0x7fff, 0x71,
    0x7fff, 0x65, 0x7fff, 0x58, 0x7fff, 0x4b, 0x7fff, 0x3f,
    0x7fff, 0x32, 0x7fff, 0x26, 0x7fff, 0x19, 0x7fff, 0xd,
    0x7fff, 0x0, 0x7fff, 0xfff3, 0x7fff, 0xffe7, 0x7fff, 0xffda,
    0x7fff, 0xffce, 0x7fff, 0xffc1, 0x7fff, 0xffb5, 0x7fff, 0xffa8,
    0x7fff, 0xff9b, 0x7fff, 0xff8f, 0x7fff, 0xff82, 0x7fff, 0xff76,
    0x7fff, 0xff69, 0x7fff, 0xff5d, 0x7fff, 0xff50, 0x7fff, 0xff44,
    0x7fff, 0xff37, 0x7fff, 0xff2a, 0x7ffe, 0xff1e, 0x7ffe, 0xff11,
    0x7ffe, 0xff05, 0x7ffe, 0xfef8, 0x7ffe, 0xfeec, 0x7ffd, 0xfedf,
    0x7ffd, 0xfed2, 0x7ffd, 0xfec6, 0x7ffd, 0xfeb9, 0x7ffc, 0xfead,
    0x7ffc, 0xfea0, 0x7ffc, 0xfe94, 0x7ffc, 0xfe87, 0x7ffb, 0xfe7a,
    0x7ffb, 0xfe6e, 0x7ffb, 0xfe61, 0x7ffa, 0xfe55, 0x7ffa, 0xfe48,
    0x7ffa, 0xfe3c, 0x7ff9, 0xfe2f, 0x7ff9, 0xfe23, 0x7ff9, 0xfe16,
    0x7ff8, 0xfe09, 0x7ff8, 0xfdfd, 0x7ff7, 0xfdf0, 0x7ff7, 0xfde4,
    0x7ff7, 0xfdd7, 0x7ff6, 0xfdcb, 0x7ff6, 0xfdbe, 0x7ff5, 0xfdb2,
    0x7ff5, 0xfda5, 0x7ff4, 0xfd98, 0x7ff4, 0xfd8c, 0x7ff3, 0xfd7f,
    0x7ff3, 0xfd73, 0x7ff2, 0xfd66, 0x7ff2, 0xfd5a, 0x7ff1, 0xfd4d,
    0x7ff1, 0xfd40, 0x7ff0, 0xfd34, 0x7ff0, 0xfd27, 0x7fef, 0xfd1b,
    0x7fef, 0xfd0e, 0x7fee, 0xfd02, 0x7fed, 0xfcf5, 0x7fed, 0xfce9,
    0x7fec, 0xfcdc, 0x7fec, 0xfcd0, 0x7feb, 0xfcc3, 0x7fea, 0xfcb6,
    0x7fea, 0xfcaa, 0x7fe9, 0xfc9d, 0x7fe8, 0xfc91, 0x7fe8, 0xfc84,
    0x7fe7, 0xfc78, 0x7fe6, 0xfc6b, 0x7fe6, 0xfc5f, 0x7fe5, 0xfc52,
    0x7fe4, 0xfc45, 0x7fe3, 0xfc39, 0x7fe3, 0xfc2c, 0x7fe2, 0xfc20,
    0x7fe1, 0xfc13, 0x7fe0, 0xfc07, 0x7fe0, 0xfbfa, 0x7fdf, 0xfbee,
    0x7fde, 0xfbe1, 0x7fdd, 0xfbd5, 0x7fdc, 0xfbc8, 0x7fdc, 0xfbbc,
    0x7fdb, 0xfbaf, 0x7fda, 0xfba2, 0x7fd9, 0xfb96, 0x7fd8, 0xfb89,
    0x7fd7, 0xfb7d, 0x7fd6, 0xfb70, 0x7fd5, 0xfb64, 0x7fd5, 0xfb57,
    0x7fd4, 0xfb4b, 0x7fd3, 0xfb3e, 0x7fd2, 0xfb32, 0x7fd1, 0xfb25,
    0x7fd0, 0xfb19, 0x7fcf, 0xfb0c, 0x7fce, 0xfb00, 0x7fcd, 0xfaf3,
    0x7fcc, 0xfae6, 0x7fcb, 0xfada, 0x7fca, 0xfacd, 0x7fc9, 0xfac1,
    0x7fc8, 0xfab4, 0x7fc7, 0xfaa8, 0x7fc6, 0xfa9b, 0x7fc5, 0xfa8f,
    0x7fc4, 0xfa82, 0x7fc3, 0xfa76, 0x7fc1, 0xfa69, 0x7fc0, 0xfa5d,
    0x7fbf, 0xfa50, 0x7fbe, 0xfa44, 0x7fbd, 0xfa37, 0x7fbc, 0xfa2b,
    0x7fbb, 0xfa1e, 0x7fb9, 0xfa12, 0x7fb8, 0xfa05, 0x7fb7, 0xf9f9,
    0x7fb6, 0xf9ec, 0x7fb5, 0xf9e0, 0x7fb4, 0xf9d3, 0x7fb2, 0xf9c7,
    0x7fb1, 0xf9ba, 0x7fb0, 0xf9ae, 0x7faf, 0xf9a1, 0x7fad, 0xf995,
    0x7fac, 0xf988, 0x7fab, 0xf97c, 0x7faa, 0xf96f, 0x7fa8, 0xf963,
    0x7fa7, 0xf956, 0x7fa6, 0xf94a, 0x7fa4, 0xf93d, 0x7fa3, 0xf931,
    0x7fa2, 0xf924, 0x7fa0, 0xf918, 0x7f9f, 0xf90b, 0x7f9e, 0xf8ff,
    0x7f9c, 0xf8f2, 0x7f9b, 0xf8e6, 0x7f99, 0xf8d9, 0x7f98, 0xf8cd,
    0x7f97, 0xf8c0, 0x7f95, 0xf8b4, 0x7f94, 0xf8a7, 0x7f92, 0xf89b,
    0x7f91, 0xf88e, 0x7f8f, 0xf882, 0x7f8e, 0xf875, 0x7f8c, 0xf869,
    0x7f8b, 0xf85c, 0x7f89, 0xf850, 0x7f88, 0xf843, 0x7f86, 0xf837,
    0x7f85, 0xf82a, 0x7f83, 0xf81e, 0x7f82, 0xf811, 0x7f80, 0xf805,
    0x7f7f, 0xf7f9, 0x7f7d, 0xf7ec, 0x7f7b, 0xf7e0, 0x7f7a, 0xf7d3,
    0x7f78, 0xf7c7, 0x7f77, 0xf7ba, 0x7f75, 0xf7ae, 0x7f73, 0xf7a1,
    0x7f72, 0xf795, 0x7f70, 0xf788, 0x7f6e, 0xf77c, 0x7f6d, 0xf76f,
    0x7f6b, 0xf763, 0x7f69, 0xf757, 0x7f68, 0xf74a, 0x7f66, 0xf73e,
    0x7f64, 0xf731, 0x7f62, 0xf725, 0x7f61, 0xf718, 0x7f5f, 0xf70c,
    0x7f5d, 0xf6ff, 0x7f5b, 0xf6f3, 0x7f5a, 0xf6e7, 0x7f58, 0xf6da,
    0x7f56, 0xf6ce, 0x7f54, 0xf6c1, 0x7f52, 0xf6b5, 0x7f51, 0xf6a8,
    0x7f4f, 0xf69c, 0x7f4d, 0xf690, 0x7f4b, 0xf683, 0x7f49, 0xf677,
    0x7f47, 0xf66a, 0x7f45, 0xf65e, 0x7f43, 0xf651, 0x7f42, 0xf645,
    0x7f40, 0xf639, 0x7f3e, 0xf62c, 0x7f3c, 0xf620, 0x7f3a, 0xf613,
    0x7f38, 0xf607, 0x7f36, 0xf5fa, 0x7f34, 0xf5ee, 0x7f32, 0xf5e2,
    0x7f30, 0xf5d5, 0x7f2e, 0xf5c9, 0x7f2c, 0xf5bc, 0x7f2a, 0xf5b0,
    0x7f28, 0xf5a4, 0x7f26, 0xf597, 0x7f24, 0xf58b, 0x7f22, 0xf57e,
    0x7f20, 0xf572, 0x7f1e, 0xf566, 0x7f1c, 0xf559, 0x7f19, 0xf54d,
    0x7f17, 0xf540, 0x7f15, 0xf534, 0x7f13, 0xf528, 0x7f11, 0xf51b,
    0x7f0f, 0xf50f, 0x7f0d, 0xf503, 0x7f0a, 0xf4f6, 0x7f08, 0xf4ea,
    0x7f06, 0xf4dd, 0x7f04, 0xf4d1, 0x7f02, 0xf4c5, 0x7f00, 0xf4b8,
    0x7efd, 0xf4ac, 0x7efb, 0xf4a0, 0x7ef9, 0xf493, 0x7ef7, 0xf487,
    0x7ef4, 0xf47b, 0x7ef2, 0xf46e, 0x7ef0, 0xf462, 0x7eed, 0xf455,
    0x7eeb, 0xf449, 0x7ee9, 0xf43d, 0x7ee7, 0xf430, 0x7ee4, 0xf424,
    0x7ee2, 0xf418, 0x7ee0, 0xf40b, 0x7edd, 0xf3ff, 0x7edb, 0xf3f3,
    0x7ed8, 0xf3e6, 0x7ed6, 0xf3da, 0x7ed4, 0xf3ce, 0x7ed1, 0xf3c1,
    0x7ecf, 0xf3b5, 0x7ecc, 0xf3a9, 0x7eca, 0xf39c, 0x7ec8, 0xf390,
    0x7ec5, 0xf384, 0x7ec3, 0xf377, 0x7ec0, 0xf36b, 0x7ebe, 0xf35f,
    0x7ebb, 0xf352, 0x7eb9, 0xf346, 0x7eb6, 0xf33a, 0x7eb4, 0xf32d,
    0x7eb1, 0xf321, 0x7eaf, 0xf315, 0x7eac, 0xf308, 0x7eaa, 0xf2fc,
    0x7ea7, 0xf2f0, 0x7ea5, 0xf2e4, 0x7ea2, 0xf2d7, 0x7e9f, 0xf2cb,
    0x7e9d, 0xf2bf, 0x7e9a, 0xf2b2, 0x7e98, 0xf2a6, 0x7e95, 0xf29a,
    0x7e92, 0xf28e, 0x7e90, 0xf281, 0x7e8d, 0xf275, 0x7e8a, 0xf269,
    0x7e88, 0xf25c, 0x7e85, 0xf250, 0x7e82, 0xf244, 0x7e80, 0xf238,
    0x7e7d, 0xf22b, 0x7e7a, 0xf21f, 0x7e77, 0xf213, 0x7e75, 0xf207,
    0x7e72, 0xf1fa, 0x7e6f, 0xf1ee, 0x7e6c, 0xf1e2, 0x7e6a, 0xf1d5,
    0x7e67, 0xf1c9, 0x7e64, 0xf1bd, 0x7e61, 0xf1b1, 0x7e5e, 0xf1a4,
    0x7e5c, 0xf198, 0x7e59, 0xf18c, 0x7e56, 0xf180, 0x7e53, 0xf174,
    0x7e50, 0xf167, 0x7e4d, 0xf15b, 0x7e4a, 0xf14f, 0x7e48, 0xf143,
    0x7e45, 0xf136, 0x7e42, 0xf12a, 0x7e3f, 0xf11e, 0x7e3c, 0xf112,
    0x7e39, 0xf105, 0x7e36, 0xf0f9, 0x7e33, 0xf0ed, 0x7e30, 0xf0e1,
    0x7e2d, 0xf0d5, 0x7e2a, 0xf0c8, 0x7e27, 0xf0bc, 0x7e24, 0xf0b0,
    0x7e21, 0xf0a4, 0x7e1e, 0xf098, 0x7e1b, 0xf08b, 0x7e18, 0xf07f,
    0x7e15, 0xf073, 0x7e12, 0xf067, 0x7e0f, 0xf05b, 0x7e0c, 0xf04e,
    0x7e09, 0xf042, 0x7e06, 0xf036, 0x7e03, 0xf02a, 0x7dff, 0xf01e,
    0x7dfc, 0xf012, 0x7df9, 0xf005, 0x7df6, 0xeff9, 0x7df3, 0xefed,
    0x7df0, 0xefe1, 0x7ded, 0xefd5, 0x7de9, 0xefc9, 0x7de6, 0xefbc,
    0x7de3, 0xefb0, 0x7de0, 0xefa4, 0x7ddd, 0xef98, 0x7dd9, 0xef8c,
    0x7dd6, 0xef80, 0x7dd3, 0xef74, 0x7dd0, 0xef67, 0x7dcc, 0xef5b,
    0x7dc9, 0xef4f, 0x7dc6, 0xef43, 0x7dc2, 0xef37, 0x7dbf, 0xef2b,
    0x7dbc, 0xef1f, 0x7db9, 0xef13, 0x7db5, 0xef06, 0x7db2, 0xeefa,
    0x7daf, 0xeeee, 0x7dab, 0xeee2, 0x7da8, 0xeed6, 0x7da4, 0xeeca,
    0x7da1, 0xeebe, 0x7d9e, 0xeeb2, 0x7d9a, 0xeea6, 0x7d97, 0xee99,
    0x7d93, 0xee8d, 0x7d90, 0xee81, 0x7d8d, 0xee75, 0x7d89, 0xee69,
    0x7d86, 0xee5d, 0x7d82, 0xee51, 0x7d7f, 0xee45, 0x7d7b, 0xee39,
    0x7d78, 0xee2d, 0x7d74, 0xee21, 0x7d71, 0xee15, 0x7d6d, 0xee09,
    0x7d6a, 0xedfc, 0x7d66, 0xedf0, 0x7d63, 0xede4, 0x7d5f, 0xedd8,
    0x7d5b, 0xedcc, 0x7d58, 0xedc0, 0x7d54, 0xedb4, 0x7d51, 0xeda8,
    0x7d4d, 0xed9c, 0x7d49, 0xed90, 0x7d46, 0xed84, 0x7d42, 0xed78,
    0x7d3f, 0xed6c, 0x7d3b, 0xed60, 0x7d37, 0xed54, 0x7d34, 0xed48,
    0x7d30, 0xed3c, 0x7d2c, 0xed30, 0x7d28, 0xed24, 0x7d25, 0xed18,
    0x7d21, 0xed0c, 0x7d1d, 0xed00, 0x7d1a, 0xecf4, 0x7d16, 0xece8,
    0x7d12, 0xecdc, 0x7d0e, 0xecd0, 0x7d0b, 0xecc4, 0x7d07, 0xecb8,
    0x7d03, 0xecac, 0x7cff, 0xeca0, 0x7cfb, 0xec94, 0x7cf8, 0xec88,
    0x7cf4, 0xec7c, 0x7cf0, 0xec70, 0x7cec, 0xec64, 0x7ce8, 0xec58,
    0x7ce4, 0xec4c, 0x7ce0, 0xec40, 0x7cdd, 0xec34, 0x7cd9, 0xec28,
    0x7cd5, 0xec1c, 0x7cd1, 0xec10, 0x7ccd, 0xec05, 0x7cc9, 0xebf9,
    0x7cc5, 0xebed, 0x7cc1, 0xebe1, 0x7cbd, 0xebd5, 0x7cb9, 0xebc9,
    0x7cb5, 0xebbd, 0x7cb1, 0xebb1, 0x7cad, 0xeba5, 0x7ca9, 0xeb99,
    0x7ca5, 0xeb8d, 0x7ca1, 0xeb81, 0x7c9d, 0xeb75, 0x7c99, 0xeb6a,
    0x7c95, 0xeb5e, 0x7c91, 0xeb52, 0x7c8d, 0xeb46, 0x7c89, 0xeb3a,
    0x7c85, 0xeb2e, 0x7c81, 0xeb22, 0x7c7d, 0xeb16, 0x7c79, 0xeb0a,
    0x7c74, 0xeaff, 0x7c70, 0xeaf3, 0x7c6c, 0xeae7, 0x7c68, 0xeadb,
    0x7c64, 0xeacf, 0x7c60, 0xeac3, 0x7c5b, 0xeab7, 0x7c57, 0xeaac,
    0x7c53, 0xeaa0, 0x7c4f, 0xea94, 0x7c4b, 0xea88, 0x7c46, 0xea7c,
    0x7c42, 0xea70, 0x7c3e, 0xea65, 0x7c3a, 0xea59, 0x7c36, 0xea4d,
    0x7c31, 0xea41, 0x7c2d, 0xea35, 0x7c29, 0xea29, 0x7c24, 0xea1e,
    0x7c20, 0xea12, 0x7c1c, 0xea06, 0x7c17, 0xe9fa, 0x7c13, 0xe9ee,
    0x7c0f, 0xe9e3, 0x7c0a, 0xe9d7, 0x7c06, 0xe9cb, 0x7c02, 0xe9bf,
    0x7bfd, 0xe9b4, 0x7bf9, 0xe9a8, 0x7bf5, 0xe99c, 0x7bf0, 0xe990,
    0x7bec, 0xe984, 0x7be7, 0xe979, 0x7be3, 0xe96d, 0x7bde, 0xe961,
    0x7bda, 0xe955, 0x7bd6, 0xe94a, 0x7bd1, 0xe93e, 0x7bcd, 0xe932,
    0x7bc8, 0xe926, 0x7bc4, 0xe91b, 0x7bbf, 0xe90f, 0x7bbb, 0xe903,
    0x7bb6, 0xe8f7, 0x7bb2, 0xe8ec, 0x7bad, 0xe8e0, 0x7ba9, 0xe8d4,
    0x7ba4, 0xe8c9, 0x7b9f, 0xe8bd, 0x7b9b, 0xe8b1, 0x7b96, 0xe8a5,
    0x7b92, 0xe89a, 0x7b8d, 0xe88e, 0x7b88, 0xe882, 0x7b84, 0xe877,
    0x7b7f, 0xe86b, 0x7b7b, 0xe85f, 0x7b76, 0xe854, 0x7b71, 0xe848,
    0x7b6d, 0xe83c, 0x7b68, 0xe831, 0x7b63, 0xe825, 0x7b5f, 0xe819,
    0x7b5a, 0xe80e, 0x7b55, 0xe802, 0x7b50, 0xe7f6, 0x7b4c, 0xe7eb,
    0x7b47, 0xe7df, 0x7b42, 0xe7d3, 0x7b3e, 0xe7c8, 0x7b39, 0xe7bc,
    0x7b34, 0xe7b1, 0x7b2f, 0xe7a5, 0x7b2a, 0xe799, 0x7b26, 0xe78e,
    0x7b21, 0xe782, 0x7b1c, 0xe777, 0x7b17, 0xe76b, 0x7b12, 0xe75f,
    0x7b0e, 0xe754, 0x7b09, 0xe748, 0x7b04, 0xe73d, 0x7aff, 0xe731,
    0x7afa, 0xe725, 0x7af5, 0xe71a, 0x7af0, 0xe70e, 0x7aeb, 0xe703,
    0x7ae6, 0xe6f7, 0x7ae2, 0xe6ec, 0x7add, 0xe6e0, 0x7ad8, 0xe6d4,
    0x7ad3, 0xe6c9, 0x7ace, 0xe6bd, 0x7ac9, 0xe6b2, 0x7ac4, 0xe6a6,
    0x7abf, 0xe69b, 0x7aba, 0xe68f, 0x7ab5, 0xe684, 0x7ab0, 0xe678,
    0x7aab, 0xe66d, 0x7aa6, 0xe661, 0x7aa1, 0xe656, 0x7a9c, 0xe64a,
    0x7a97, 0xe63f, 0x7a92, 0xe633, 0x7a8d, 0xe628, 0x7a88, 0xe61c,
    0x7a82, 0xe611, 0x7a7d, 0xe605, 0x7a78, 0xe5fa, 0x7a73, 0xe5ee,
    0x7a6e, 0xe5e3, 0x7a69, 0xe5d7, 0x7a64, 0xe5cc, 0x7a5f, 0xe5c0,
    0x7a59, 0xe5b5, 0x7a54, 0xe5a9, 0x7a4f, 0xe59e, 0x7a4a, 0xe592,
    0x7a45, 0xe587, 0x7a3f, 0xe57c, 0x7a3a, 0xe570, 0x7a35, 0xe565,
    0x7a30, 0xe559, 0x7a2b, 0xe54e, 0x7a25, 0xe542, 0x7a20, 0xe537,
    0x7a1b, 0xe52c, 0x7a16, 0xe520, 0x7a10, 0xe515, 0x7a0b, 0xe509,
    0x7a06, 0xe4fe, 0x7a00, 0xe4f3, 0x79fb, 0xe4e7, 0x79f6, 0xe4dc,
    0x79f0, 0xe4d0, 0x79eb, 0xe4c5, 0x79e6, 0xe4ba, 0x79e0, 0xe4ae,
    0x79db, 0xe4a3, 0x79d6, 0xe498, 0x79d0, 0xe48c, 0x79cb, 0xe481,
    0x79c5, 0xe476, 0x79c0, 0xe46a, 0x79bb, 0xe45f, 0x79b5, 0xe454,
    0x79b0, 0xe448, 0x79aa, 0xe43d, 0x79a5, 0xe432, 0x799f, 0xe426,
    0x799a, 0xe41b, 0x7994, 0xe410, 0x798f, 0xe404, 0x7989, 0xe3f9,
    0x7984, 0xe3ee, 0x797e, 0xe3e2, 0x7979, 0xe3d7, 0x7973, 0xe3cc,
    0x796e, 0xe3c1, 0x7968, 0xe3b5, 0x7963, 0xe3aa, 0x795d, 0xe39f,
    0x7958, 0xe394, 0x7952, 0xe388, 0x794c, 0xe37d, 0x7947, 0xe372,
    0x7941, 0xe367, 0x793b, 0xe35b, 0x7936, 0xe350, 0x7930, 0xe345,
    0x792b, 0xe33a, 0x7925, 0xe32e, 0x791f, 0xe323, 0x791a, 0xe318,
    0x7914, 0xe30d, 0x790e, 0xe301, 0x7909, 0xe2f6, 0x7903, 0xe2eb,
    0x78fd, 0xe2e0, 0x78f7, 0xe2d5, 0x78f2, 0xe2ca, 0x78ec, 0xe2be,
    0x78e6, 0xe2b3, 0x78e0, 0xe2a8, 0x78db, 0xe29d, 0x78d5, 0xe292,
    0x78cf, 0xe287, 0x78c9, 0xe27b, 0x78c3, 0xe270, 0x78be, 0xe265,
    0x78b8, 0xe25a, 0x78b2, 0xe24f, 0x78ac, 0xe244, 0x78a6, 0xe239,
    0x78a1, 0xe22d, 0x789b, 0xe222, 0x7895, 0xe217, 0x788f, 0xe20c,
    0x7889, 0xe201, 0x7883, 0xe1f6, 0x787d, 0xe1eb, 0x7877, 0xe1e0,
    0x7871, 0xe1d5, 0x786b, 0xe1ca, 0x7866, 0xe1be, 0x7860, 0xe1b3,
    0x785a, 0xe1a8, 0x7854, 0xe19d, 0x784e, 0xe192, 0x7848, 0xe187,
    0x7842, 0xe17c, 0x783c, 0xe171, 0x7836, 0xe166, 0x7830, 0xe15b,
    0x782a, 0xe150, 0x7824, 0xe145, 0x781e, 0xe13a, 0x7818, 0xe12f,
    0x7812, 0xe124, 0x780b, 0xe119, 0x7805, 0xe10e, 0x77ff, 0xe103,
    0x77f9, 0xe0f8, 0x77f3, 0xe0ed, 0x77ed, 0xe0e2, 0x77e7, 0xe0d7,
    0x77e1, 0xe0cc, 0x77db, 0xe0c1, 0x77d5, 0xe0b6, 0x77ce, 0xe0ab,
    0x77c8, 0xe0a0, 0x77c2, 0xe095, 0x77bc, 0xe08a, 0x77b6, 0xe07f,
    0x77b0, 0xe074, 0x77a9, 0xe069, 0x77a3, 0xe05e, 0x779d, 0xe054,
    0x7797, 0xe049, 0x7790, 0xe03e, 0x778a, 0xe033, 0x7784, 0xe028,
    0x777e, 0xe01d, 0x7777, 0xe012, 0x7771, 0xe007, 0x776b, 0xdffc,
    0x7765, 0xdff1, 0x775e, 0xdfe7, 0x7758, 0xdfdc, 0x7752, 0xdfd1,
    0x774b, 0xdfc6, 0x7745, 0xdfbb, 0x773f, 0xdfb0, 0x7738, 0xdfa5,
    0x7732, 0xdf9b, 0x772c, 0xdf90, 0x7725, 0xdf85, 0x771f, 0xdf7a,
    0x7718, 0xdf6f, 0x7712, 0xdf65, 0x770c, 0xdf5a, 0x7705, 0xdf4f,
    0x76ff, 0xdf44, 0x76f8, 0xdf39, 0x76f2, 0xdf2f, 0x76eb, 0xdf24,
    0x76e5, 0xdf19, 0x76df, 0xdf0e, 0x76d8, 0xdf03, 0x76d2, 0xdef9,
    0x76cb, 0xdeee, 0x76c5, 0xdee3, 0x76be, 0xded8, 0x76b8, 0xdece,
    0x76b1, 0xdec3, 0x76ab, 0xdeb8, 0x76a4, 0xdead, 0x769d, 0xdea3,
    0x7697, 0xde98, 0x7690, 0xde8d, 0x768a, 0xde83, 0x7683, 0xde78,
    0x767d, 0xde6d, 0x7676, 0xde62, 0x766f, 0xde58, 0x7669, 0xde4d,
    0x7662, 0xde42, 0x765c, 0xde38, 0x7655, 0xde2d, 0x764e, 0xde22,
    0x7648, 0xde18, 0x7641, 0xde0d, 0x763a, 0xde02, 0x7634, 0xddf8,
    0x762d, 0xdded, 0x7626, 0xdde2, 0x7620, 0xddd8, 0x7619, 0xddcd,
    0x7612, 0xddc3, 0x760b, 0xddb8, 0x7605, 0xddad, 0x75fe, 0xdda3,
    0x75f7, 0xdd98, 0x75f0, 0xdd8e, 0x75ea, 0xdd83, 0x75e3, 0xdd78,
    0x75dc, 0xdd6e, 0x75d5, 0xdd63, 0x75ce, 0xdd59, 0x75c8, 0xdd4e,
    0x75c1, 0xdd44, 0x75ba, 0xdd39, 0x75b3, 0xdd2e, 0x75ac, 0xdd24,
    0x75a5, 0xdd19, 0x759f, 0xdd0f, 0x7598, 0xdd04, 0x7591, 0xdcfa,
    0x758a, 0xdcef, 0x7583, 0xdce5, 0x757c, 0xdcda, 0x7575, 0xdcd0,
    0x756e, 0xdcc5, 0x7567, 0xdcbb, 0x7561, 0xdcb0, 0x755a, 0xdca6,
    0x7553, 0xdc9b, 0x754c, 0xdc91, 0x7545, 0xdc86, 0x753e, 0xdc7c,
    0x7537, 0xdc72, 0x7530, 0xdc67, 0x7529, 0xdc5d, 0x7522, 0xdc52,
    0x751b, 0xdc48, 0x7514, 0xdc3d, 0x750d, 0xdc33, 0x7506, 0xdc29,
    0x74ff, 0xdc1e, 0x74f8, 0xdc14, 0x74f1, 0xdc09, 0x74ea, 0xdbff,
    0x74e2, 0xdbf5, 0x74db, 0xdbea, 0x74d4, 0xdbe0, 0x74cd, 0xdbd5,
    0x74c6, 0xdbcb, 0x74bf, 0xdbc1, 0x74b8, 0xdbb6, 0x74b1, 0xdbac,
    0x74aa, 0xdba2, 0x74a2, 0xdb97, 0x749b, 0xdb8d, 0x7494, 0xdb83,
    0x748d, 0xdb78, 0x7486, 0xdb6e, 0x747f, 0xdb64, 0x7477, 0xdb59,
    0x7470, 0xdb4f, 0x7469, 0xdb45, 0x7462, 0xdb3b, 0x745b, 0xdb30,
    0x7453, 0xdb26, 0x744c, 0xdb1c, 0x7445, 0xdb11, 0x743e, 0xdb07,
    0x7436, 0xdafd, 0x742f, 0xdaf3, 0x7428, 0xdae8, 0x7420, 0xdade,
    0x7419, 0xdad4, 0x7412, 0xdaca, 0x740b, 0xdabf, 0x7403, 0xdab5,
    0x73fc, 0xdaab, 0x73f5, 0xdaa1, 0x73ed, 0xda97, 0x73e6, 0xda8c,
    0x73df, 0xda82, 0x73d7, 0xda78, 0x73d0, 0xda6e, 0x73c8, 0xda64,
    0x73c1, 0xda5a, 0x73ba, 0xda4f, 0x73b2, 0xda45, 0x73ab, 0xda3b,
    0x73a3, 0xda31, 0x739c, 0xda27, 0x7395, 0xda1d, 0x738d, 0xda13,
    0x7386, 0xda08, 0x737e, 0xd9fe, 0x7377, 0xd9f4, 0x736f, 0xd9ea,
    0x7368, 0xd9e0, 0x7360, 0xd9d6, 0x7359, 0xd9cc, 0x7351, 0xd9c2,
    0x734a, 0xd9b8, 0x7342, 0xd9ae, 0x733b, 0xd9a4, 0x7333, 0xd99a,
    0x732c, 0xd98f, 0x7324, 0xd985, 0x731d, 0xd97b, 0x7315, 0xd971,
    0x730d, 0xd967, 0x7306, 0xd95d, 0x72fe, 0xd953, 0x72f7, 0xd949,
    0x72ef, 0xd93f, 0x72e7, 0xd935, 0x72e0, 0xd92b, 0x72d8, 0xd921,
    0x72d0, 0xd917, 0x72c9, 0xd90d, 0x72c1, 0xd903, 0x72ba, 0xd8f9,
    0x72b2, 0xd8ef, 0x72aa, 0xd8e6, 0x72a3, 0xd8dc, 0x729b, 0xd8d2,
    0x7293, 0xd8c8, 0x728b, 0xd8be, 0x7284, 0xd8b4, 0x727c, 0xd8aa,
    0x7274, 0xd8a0, 0x726d, 0xd896, 0x7265, 0xd88c, 0x725d, 0xd882,
    0x7255, 0xd878, 0x724e, 0xd86f, 0x7246, 0xd865, 0x723e, 0xd85b,
    0x7236, 0xd851, 0x722e, 0xd847, 0x7227, 0xd83d, 0x721f, 0xd833,
    0x7217, 0xd82a, 0x720f, 0xd820, 0x7207, 0xd816, 0x71ff, 0xd80c,
    0x71f8, 0xd802, 0x71f0, 0xd7f8, 0x71e8, 0xd7ef, 0x71e0, 0xd7e5,
    0x71d8, 0xd7db, 0x71d0, 0xd7d1, 0x71c8, 0xd7c8, 0x71c0, 0xd7be,
    0x71b9, 0xd7b4, 0x71b1, 0xd7aa, 0x71a9, 0xd7a0, 0x71a1, 0xd797,
    0x7199, 0xd78d, 0x7191, 0xd783, 0x7189, 0xd77a, 0x7181, 0xd770,
    0x7179, 0xd766, 0x7171, 0xd75c, 0x7169, 0xd753, 0x7161, 0xd749,
    0x7159, 0xd73f, 0x7151, 0xd736, 0x7149, 0xd72c, 0x7141, 0xd722,
    0x7139, 0xd719, 0x7131, 0xd70f, 0x7129, 0xd705, 0x7121, 0xd6fc,
    0x7119, 0xd6f2, 0x7111, 0xd6e8, 0x7109, 0xd6df, 0x7101, 0xd6d5,
    0x70f9, 0xd6cb, 0x70f0, 0xd6c2, 0x70e8, 0xd6b8, 0x70e0, 0xd6af,
    0x70d8, 0xd6a5, 0x70d0, 0xd69b, 0x70c8, 0xd692, 0x70c0, 0xd688,
    0x70b8, 0xd67f, 0x70af, 0xd675, 0x70a7, 0xd66c, 0x709f, 0xd662,
    0x7097, 0xd659, 0x708f, 0xd64f, 0x7087, 0xd645, 0x707e, 0xd63c,
    0x7076, 0xd632, 0x706e, 0xd629, 0x7066, 0xd61f, 0x705d, 0xd616,
    0x7055, 0xd60c, 0x704d, 0xd603, 0x7045, 0xd5f9, 0x703c, 0xd5f0,
    0x7034, 0xd5e6, 0x702c, 0xd5dd, 0x7024, 0xd5d4, 0x701b, 0xd5ca,
    0x7013, 0xd5c1, 0x700b, 0xd5b7, 0x7002, 0xd5ae, 0x6ffa, 0xd5a4,
    0x6ff2, 0xd59b, 0x6fea, 0xd592, 0x6fe1, 0xd588, 0x6fd9, 0xd57f,
    0x6fd0, 0xd575, 0x6fc8, 0xd56c, 0x6fc0, 0xd563, 0x6fb7, 0xd559,
    0x6faf, 0xd550, 0x6fa7, 0xd547, 0x6f9e, 0xd53d, 0x6f96, 0xd534,
    0x6f8d, 0xd52a, 0x6f85, 0xd521, 0x6f7d, 0xd518, 0x6f74, 0xd50e,
    0x6f6c, 0xd505, 0x6f63, 0xd4fc, 0x6f5b, 0xd4f3, 0x6f52, 0xd4e9,
    0x6f4a, 0xd4e0, 0x6f41, 0xd4d7, 0x6f39, 0xd4cd, 0x6f30, 0xd4c4,
    0x6f28, 0xd4bb, 0x6f20, 0xd4b2, 0x6f17, 0xd4a8, 0x6f0e, 0xd49f,
    0x6f06, 0xd496, 0x6efd, 0xd48d, 0x6ef5, 0xd483, 0x6eec, 0xd47a,
    0x6ee4, 0xd471, 0x6edb, 0xd468, 0x6ed3, 0xd45f, 0x6eca, 0xd455,
    0x6ec2, 0xd44c, 0x6eb9, 0xd443, 0x6eb0, 0xd43a, 0x6ea8, 0xd431,
    0x6e9f, 0xd428, 0x6e97, 0xd41e, 0x6e8e, 0xd415, 0x6e85, 0xd40c,
    0x6e7d, 0xd403, 0x6e74, 0xd3fa, 0x6e6b, 0xd3f1, 0x6e63, 0xd3e8,
    0x6e5a, 0xd3df, 0x6e51, 0xd3d5, 0x6e49, 0xd3cc, 0x6e40, 0xd3c3,
    0x6e37, 0xd3ba, 0x6e2f, 0xd3b1, 0x6e26, 0xd3a8, 0x6e1d, 0xd39f,
    0x6e15, 0xd396, 0x6e0c, 0xd38d, 0x6e03, 0xd384, 0x6dfa, 0xd37b,
    0x6df2, 0xd372, 0x6de9, 0xd369, 0x6de0, 0xd360, 0x6dd7, 0xd357,
    0x6dcf, 0xd34e, 0x6dc6, 0xd345, 0x6dbd, 0xd33c, 0x6db4, 0xd333,
    0x6dab, 0xd32a, 0x6da3, 0xd321, 0x6d9a, 0xd318, 0x6d91, 0xd30f,
    0x6d88, 0xd306, 0x6d7f, 0xd2fd, 0x6d76, 0xd2f4, 0x6d6e, 0xd2eb,
    0x6d65, 0xd2e2, 0x6d5c, 0xd2d9, 0x6d53, 0xd2d1, 0x6d4a, 0xd2c8,
    0x6d41, 0xd2bf, 0x6d38, 0xd2b6, 0x6d2f, 0xd2ad, 0x6d27, 0xd2a4,
    0x6d1e, 0xd29b, 0x6d15, 0xd292, 0x6d0c, 0xd28a, 0x6d03, 0xd281,
    0x6cfa, 0xd278, 0x6cf1, 0xd26f, 0x6ce8, 0xd266, 0x6cdf, 0xd25d,
    0x6cd6, 0xd255, 0x6ccd, 0xd24c, 0x6cc4, 0xd243, 0x6cbb, 0xd23a,
    0x6cb2, 0xd231, 0x6ca9, 0xd229, 0x6ca0, 0xd220, 0x6c97, 0xd217,
    0x6c8e, 0xd20e, 0x6c85, 0xd206, 0x6c7c, 0xd1fd, 0x6c73, 0xd1f4,
    0x6c6a, 0xd1eb, 0x6c61, 0xd1e3, 0x6c58, 0xd1da, 0x6c4f, 0xd1d1,
    0x6c46, 0xd1c9, 0x6c3d, 0xd1c0, 0x6c34, 0xd1b7, 0x6c2b, 0xd1af,
    0x6c21, 0xd1a6, 0x6c18, 0xd19d, 0x6c0f, 0xd195, 0x6c06, 0xd18c,
    0x6bfd, 0xd183, 0x6bf4, 0xd17b, 0x6beb, 0xd172, 0x6be2, 0xd169,
    0x6bd8, 0xd161, 0x6bcf, 0xd158, 0x6bc6, 0xd150, 0x6bbd, 0xd147,
    0x6bb4, 0xd13e, 0x6bab, 0xd136, 0x6ba1, 0xd12d, 0x6b98, 0xd125,
    0x6b8f, 0xd11c, 0x6b86, 0xd114, 0x6b7d, 0xd10b, 0x6b73, 0xd103,
    0x6b6a, 0xd0fa, 0x6b61, 0xd0f2, 0x6b58, 0xd0e9, 0x6b4e, 0xd0e0,
    0x6b45, 0xd0d8, 0x6b3c, 0xd0d0, 0x6b33, 0xd0c7, 0x6b29, 0xd0bf,
    0x6b20, 0xd0b6, 0x6b17, 0xd0ae, 0x6b0d, 0xd0a5, 0x6b04, 0xd09d,
    0x6afb, 0xd094, 0x6af2, 0xd08c, 0x6ae8, 0xd083, 0x6adf, 0xd07b,
    0x6ad6, 0xd073, 0x6acc, 0xd06a, 0x6ac3, 0xd062, 0x6ab9, 0xd059,
    0x6ab0, 0xd051, 0x6aa7, 0xd049, 0x6a9d, 0xd040, 0x6a94, 0xd038,
    0x6a8b, 0xd030, 0x6a81, 0xd027, 0x6a78, 0xd01f, 0x6a6e, 0xd016,
    0x6a65, 0xd00e, 0x6a5c, 0xd006, 0x6a52, 0xcffe, 0x6a49, 0xcff5,
    0x6a3f, 0xcfed, 0x6a36, 0xcfe5, 0x6a2c, 0xcfdc, 0x6a23, 0xcfd4,
    0x6a1a, 0xcfcc, 0x6a10, 0xcfc4, 0x6a07, 0xcfbb, 0x69fd, 0xcfb3,
    0x69f4, 0xcfab, 0x69ea, 0xcfa3, 0x69e1, 0xcf9a, 0x69d7, 0xcf92,
    0x69ce, 0xcf8a, 0x69c4, 0xcf82, 0x69bb, 0xcf79, 0x69b1, 0xcf71,
    0x69a7, 0xcf69, 0x699e, 0xcf61, 0x6994, 0xcf59, 0x698b, 0xcf51,
    0x6981, 0xcf48, 0x6978, 0xcf40, 0x696e, 0xcf38, 0x6965, 0xcf30,
    0x695b, 0xcf28, 0x6951, 0xcf20, 0x6948, 0xcf18, 0x693e, 0xcf10,
    0x6935, 0xcf07, 0x692b, 0xceff, 0x6921, 0xcef7, 0x6918, 0xceef,
    0x690e, 0xcee7, 0x6904, 0xcedf, 0x68fb, 0xced7, 0x68f1, 0xcecf,
    0x68e7, 0xcec7, 0x68de, 0xcebf, 0x68d4, 0xceb7, 0x68ca, 0xceaf,
    0x68c1, 0xcea7, 0x68b7, 0xce9f, 0x68ad, 0xce97, 0x68a4, 0xce8f,
    0x689a, 0xce87, 0x6890, 0xce7f, 0x6886, 0xce77, 0x687d, 0xce6f,
    0x6873, 0xce67, 0x6869, 0xce5f, 0x6860, 0xce57, 0x6856, 0xce4f,
    0x684c, 0xce47, 0x6842, 0xce40, 0x6838, 0xce38, 0x682f, 0xce30,
    0x6825, 0xce28, 0x681b, 0xce20, 0x6811, 0xce18, 0x6808, 0xce10,
    0x67fe, 0xce08, 0x67f4, 0xce01, 0x67ea, 0xcdf9, 0x67e0, 0xcdf1,
    0x67d6, 0xcde9, 0x67cd, 0xcde1, 0x67c3, 0xcdd9, 0x67b9, 0xcdd2,
    0x67af, 0xcdca, 0x67a5, 0xcdc2, 0x679b, 0xcdba, 0x6791, 0xcdb2,
    0x6788, 0xcdab, 0x677e, 0xcda3, 0x6774, 0xcd9b, 0x676a, 0xcd93,
    0x6760, 0xcd8c, 0x6756, 0xcd84, 0x674c, 0xcd7c, 0x6742, 0xcd75,
    0x6738, 0xcd6d, 0x672e, 0xcd65, 0x6724, 0xcd5d, 0x671a, 0xcd56,
    0x6711, 0xcd4e, 0x6707, 0xcd46, 0x66fd, 0xcd3f, 0x66f3, 0xcd37,
    0x66e9, 0xcd30, 0x66df, 0xcd28, 0x66d5, 0xcd20, 0x66cb, 0xcd19,
    0x66c1, 0xcd11, 0x66b7, 0xcd09, 0x66ad, 0xcd02, 0x66a3, 0xccfa,
    0x6699, 0xccf3, 0x668f, 0xcceb, 0x6685, 0xcce3, 0x667b, 0xccdc,
    0x6671, 0xccd4, 0x6666, 0xcccd, 0x665c, 0xccc5, 0x6652, 0xccbe,
    0x6648, 0xccb6, 0x663e, 0xccaf, 0x6634, 0xcca7, 0x662a, 0xcca0,
    0x6620, 0xcc98, 0x6616, 0xcc91, 0x660c, 0xcc89, 0x6602, 0xcc82,
    0x65f8, 0xcc7a, 0x65ed, 0xcc73, 0x65e3, 0xcc6b, 0x65d9, 0xcc64,
    0x65cf, 0xcc5d, 0x65c5, 0xcc55, 0x65bb, 0xcc4e, 0x65b1, 0xcc46,
    0x65a6, 0xcc3f, 0x659c, 0xcc38, 0x6592, 0xcc30, 0x6588, 0xcc29,
    0x657e, 0xcc21, 0x6574, 0xcc1a, 0x6569, 0xcc13, 0x655f, 0xcc0b,
    0x6555, 0xcc04, 0x654b, 0xcbfd, 0x6541, 0xcbf5, 0x6536, 0xcbee,
    0x652c, 0xcbe7, 0x6522, 0xcbe0, 0x6518, 0xcbd8, 0x650d, 0xcbd1,
    0x6503, 0xcbca, 0x64f9, 0xcbc2, 0x64ef, 0xcbbb, 0x64e4, 0xcbb4,
    0x64da, 0xcbad, 0x64d0, 0xcba5, 0x64c5, 0xcb9e, 0x64bb, 0xcb97,
    0x64b1, 0xcb90, 0x64a7, 0xcb89, 0x649c, 0xcb81, 0x6492, 0xcb7a,
    0x6488, 0xcb73, 0x647d, 0xcb6c, 0x6473, 0xcb65, 0x6469, 0xcb5e,
    0x645e, 0xcb56, 0x6454, 0xcb4f, 0x644a, 0xcb48, 0x643f, 0xcb41,
    0x6435, 0xcb3a, 0x642b, 0xcb33, 0x6420, 0xcb2c, 0x6416, 0xcb25,
    0x640b, 0xcb1e, 0x6401, 0xcb16, 0x63f7, 0xcb0f, 0x63ec, 0xcb08,
    0x63e2, 0xcb01, 0x63d7, 0xcafa, 0x63cd, 0xcaf3, 0x63c3, 0xcaec,
    0x63b8, 0xcae5, 0x63ae, 0xcade, 0x63a3, 0xcad7, 0x6399, 0xcad0,
    0x638e, 0xcac9, 0x6384, 0xcac2, 0x637a, 0xcabb, 0x636f, 0xcab4,
    0x6365, 0xcaad, 0x635a, 0xcaa6, 0x6350, 0xca9f, 0x6345, 0xca99,
    0x633b, 0xca92, 0x6330, 0xca8b, 0x6326, 0xca84, 0x631b, 0xca7d,
    0x6311, 0xca76, 0x6306, 0xca6f, 0x62fc, 0xca68, 0x62f1, 0xca61,
    0x62e7, 0xca5b, 0x62dc, 0xca54, 0x62d2, 0xca4d, 0x62c7, 0xca46,
    0x62bc, 0xca3f, 0x62b2, 0xca38, 0x62a7, 0xca32, 0x629d, 0xca2b,
    0x6292, 0xca24, 0x6288, 0xca1d, 0x627d, 0xca16, 0x6272, 0xca10,
    0x6268, 0xca09, 0x625d, 0xca02, 0x6253, 0xc9fb, 0x6248, 0xc9f5,
    0x623d, 0xc9ee, 0x6233, 0xc9e7, 0x6228, 0xc9e0, 0x621e, 0xc9da,
    0x6213, 0xc9d3, 0x6208, 0xc9cc, 0x61fe, 0xc9c6, 0x61f3, 0xc9bf,
    0x61e8, 0xc9b8, 0x61de, 0xc9b2, 0x61d3, 0xc9ab, 0x61c8, 0xc9a4,
    0x61be, 0xc99e, 0x61b3, 0xc997, 0x61a8, 0xc991, 0x619e, 0xc98a,
    0x6193, 0xc983, 0x6188, 0xc97d, 0x617d, 0xc976, 0x6173, 0xc970,
    0x6168, 0xc969, 0x615d, 0xc963, 0x6153, 0xc95c, 0x6148, 0xc955,
    0x613d, 0xc94f, 0x6132, 0xc948, 0x6128, 0xc942, 0x611d, 0xc93b,
    0x6112, 0xc935, 0x6107, 0xc92e, 0x60fd, 0xc928, 0x60f2, 0xc921,
    0x60e7, 0xc91b, 0x60dc, 0xc915, 0x60d1, 0xc90e, 0x60c7, 0xc908,
    0x60bc, 0xc901, 0x60b1, 0xc8fb, 0x60a6, 0xc8f4, 0x609b, 0xc8ee,
    0x6091, 0xc8e8, 0x6086, 0xc8e1, 0x607b, 0xc8db, 0x6070, 0xc8d4,
    0x6065, 0xc8ce, 0x605b, 0xc8c8, 0x6050, 0xc8c1, 0x6045, 0xc8bb,
    0x603a, 0xc8b5, 0x602f, 0xc8ae, 0x6024, 0xc8a8, 0x6019, 0xc8a2,
    0x600f, 0xc89b, 0x6004, 0xc895, 0x5ff9, 0xc88f, 0x5fee, 0xc889,
    0x5fe3, 0xc882, 0x5fd8, 0xc87c, 0x5fcd, 0xc876, 0x5fc2, 0xc870,
    0x5fb7, 0xc869, 0x5fac, 0xc863, 0x5fa2, 0xc85d, 0x5f97, 0xc857,
    0x5f8c, 0xc850, 0x5f81, 0xc84a, 0x5f76, 0xc844, 0x5f6b, 0xc83e,
    0x5f60, 0xc838, 0x5f55, 0xc832, 0x5f4a, 0xc82b, 0x5f3f, 0xc825,
    0x5f34, 0xc81f, 0x5f29, 0xc819, 0x5f1e, 0xc813, 0x5f13, 0xc80d,
    0x5f08, 0xc807, 0x5efd, 0xc801, 0x5ef2, 0xc7fb, 0x5ee7, 0xc7f5,
    0x5edc, 0xc7ee, 0x5ed1, 0xc7e8, 0x5ec6, 0xc7e2, 0x5ebb, 0xc7dc,
    0x5eb0, 0xc7d6, 0x5ea5, 0xc7d0, 0x5e9a, 0xc7ca, 0x5e8f, 0xc7c4,
    0x5e84, 0xc7be, 0x5e79, 0xc7b8, 0x5e6e, 0xc7b2, 0x5e63, 0xc7ac,
    0x5e58, 0xc7a6, 0x5e4d, 0xc7a0, 0x5e42, 0xc79a, 0x5e36, 0xc795,
    0x5e2b, 0xc78f, 0x5e20, 0xc789, 0x5e15, 0xc783, 0x5e0a, 0xc77d,
    0x5dff, 0xc777, 0x5df4, 0xc771, 0x5de9, 0xc76b, 0x5dde, 0xc765,
    0x5dd3, 0xc75f, 0x5dc7, 0xc75a, 0x5dbc, 0xc754, 0x5db1, 0xc74e,
    0x5da6, 0xc748, 0x5d9b, 0xc742, 0x5d90, 0xc73d, 0x5d85, 0xc737,
    0x5d79, 0xc731, 0x5d6e, 0xc72b, 0x5d63, 0xc725, 0x5d58, 0xc720,
    0x5d4d, 0xc71a, 0x5d42, 0xc714, 0x5d36, 0xc70e, 0x5d2b, 0xc709,
    0x5d20, 0xc703, 0x5d15, 0xc6fd, 0x5d0a, 0xc6f7, 0x5cff, 0xc6f2,
    0x5cf3, 0xc6ec, 0x5ce8, 0xc6e6, 0x5cdd, 0xc6e1, 0x5cd2, 0xc6db,
    0x5cc6, 0xc6d5, 0x5cbb, 0xc6d0, 0x5cb0, 0xc6ca, 0x5ca5, 0xc6c5,
    0x5c99, 0xc6bf, 0x5c8e, 0xc6b9, 0x5c83, 0xc6b4, 0x5c78, 0xc6ae,
    0x5c6c, 0xc6a8, 0x5c61, 0xc6a3, 0x5c56, 0xc69d, 0x5c4b, 0xc698,
    0x5c3f, 0xc692, 0x5c34, 0xc68d, 0x5c29, 0xc687, 0x5c1e, 0xc682,
    0x5c12, 0xc67c, 0x5c07, 0xc677, 0x5bfc, 0xc671, 0x5bf0, 0xc66c,
    0x5be5, 0xc666, 0x5bda, 0xc661, 0x5bce, 0xc65b, 0x5bc3, 0xc656,
    0x5bb8, 0xc650, 0x5bac, 0xc64b, 0x5ba1, 0xc645, 0x5b96, 0xc640,
    0x5b8a, 0xc63b, 0x5b7f, 0xc635, 0x5b74, 0xc630, 0x5b68, 0xc62a,
    0x5b5d, 0xc625, 0x5b52, 0xc620, 0x5b46, 0xc61a, 0x5b3b, 0xc615,
    0x5b30, 0xc610, 0x5b24, 0xc60a, 0x5b19, 0xc605, 0x5b0d, 0xc600,
    0x5b02, 0xc5fa, 0x5af7, 0xc5f5, 0x5aeb, 0xc5f0, 0x5ae0, 0xc5ea,
    0x5ad4, 0xc5e5, 0x5ac9, 0xc5e0, 0x5abe, 0xc5db, 0x5ab2, 0xc5d5,
    0x5aa7, 0xc5d0, 0x5a9b, 0xc5cb, 0x5a90, 0xc5c6, 0x5a84, 0xc5c1,
    0x5a79, 0xc5bb, 0x5a6e, 0xc5b6, 0x5a62, 0xc5b1, 0x5a57, 0xc5ac,
    0x5a4b, 0xc5a7, 0x5a40, 0xc5a1, 0x5a34, 0xc59c, 0x5a29, 0xc597,
    0x5a1d, 0xc592, 0x5a12, 0xc58d, 0x5a06, 0xc588, 0x59fb, 0xc583,
    0x59ef, 0xc57e, 0x59e4, 0xc578, 0x59d8, 0xc573, 0x59cd, 0xc56e,
    0x59c1, 0xc569, 0x59b6, 0xc564, 0x59aa, 0xc55f, 0x599f, 0xc55a,
    0x5993, 0xc555, 0x5988, 0xc550, 0x597c, 0xc54b, 0x5971, 0xc546,
    0x5965, 0xc541, 0x595a, 0xc53c, 0x594e, 0xc537, 0x5943, 0xc532,
    0x5937, 0xc52d, 0x592c, 0xc528, 0x5920, 0xc523, 0x5914, 0xc51e,
    0x5909, 0xc51a, 0x58fd, 0xc515, 0x58f2, 0xc510, 0x58e6, 0xc50b,
    0x58db, 0xc506, 0x58cf, 0xc501, 0x58c3, 0xc4fc, 0x58b8, 0xc4f7,
    0x58ac, 0xc4f2, 0x58a1, 0xc4ee, 0x5895, 0xc4e9, 0x5889, 0xc4e4,
    0x587e, 0xc4df, 0x5872, 0xc4da, 0x5867, 0xc4d6, 0x585b, 0xc4d1,
    0x584f, 0xc4cc, 0x5844, 0xc4c7, 0x5838, 0xc4c2, 0x582d, 0xc4be,
    0x5821, 0xc4b9, 0x5815, 0xc4b4, 0x580a, 0xc4b0, 0x57fe, 0xc4ab,
    0x57f2, 0xc4a6, 0x57e7, 0xc4a1, 0x57db, 0xc49d, 0x57cf, 0xc498,
    0x57c4, 0xc493, 0x57b8, 0xc48f, 0x57ac, 0xc48a, 0x57a1, 0xc485,
    0x5795, 0xc481, 0x5789, 0xc47c, 0x577e, 0xc478, 0x5772, 0xc473,
    0x5766, 0xc46e, 0x575b, 0xc46a, 0x574f, 0xc465, 0x5743, 0xc461,
    0x5737, 0xc45c, 0x572c, 0xc457, 0x5720, 0xc453, 0x5714, 0xc44e,
    0x5709, 0xc44a, 0x56fd, 0xc445, 0x56f1, 0xc441, 0x56e5, 0xc43c,
    0x56da, 0xc438, 0x56ce, 0xc433, 0x56c2, 0xc42f, 0x56b6, 0xc42a,
    0x56ab, 0xc426, 0x569f, 0xc422, 0x5693, 0xc41d, 0x5687, 0xc419,
    0x567c, 0xc414, 0x5670, 0xc410, 0x5664, 0xc40b, 0x5658, 0xc407,
    0x564c, 0xc403, 0x5641, 0xc3fe, 0x5635, 0xc3fa, 0x5629, 0xc3f6,
    0x561d, 0xc3f1, 0x5612, 0xc3ed, 0x5606, 0xc3e9, 0x55fa, 0xc3e4,
    0x55ee, 0xc3e0, 0x55e2, 0xc3dc, 0x55d7, 0xc3d7, 0x55cb, 0xc3d3,
    0x55bf, 0xc3cf, 0x55b3, 0xc3ca, 0x55a7, 0xc3c6, 0x559b, 0xc3c2,
    0x5590, 0xc3be, 0x5584, 0xc3ba, 0x5578, 0xc3b5, 0x556c, 0xc3b1,
    0x5560, 0xc3ad, 0x5554, 0xc3a9, 0x5549, 0xc3a5, 0x553d, 0xc3a0,
    0x5531, 0xc39c, 0x5525, 0xc398, 0x5519, 0xc394, 0x550d, 0xc390,
    0x5501, 0xc38c, 0x54f6, 0xc387, 0x54ea, 0xc383, 0x54de, 0xc37f,
    0x54d2, 0xc37b, 0x54c6, 0xc377, 0x54ba, 0xc373, 0x54ae, 0xc36f,
    0x54a2, 0xc36b, 0x5496, 0xc367, 0x548b, 0xc363, 0x547f, 0xc35f,
    0x5473, 0xc35b, 0x5467, 0xc357, 0x545b, 0xc353, 0x544f, 0xc34f,
    0x5443, 0xc34b, 0x5437, 0xc347, 0x542b, 0xc343, 0x541f, 0xc33f,
    0x5413, 0xc33b, 0x5407, 0xc337, 0x53fb, 0xc333, 0x53f0, 0xc32f,
    0x53e4, 0xc32b, 0x53d8, 0xc327, 0x53cc, 0xc323, 0x53c0, 0xc320,
    0x53b4, 0xc31c, 0x53a8, 0xc318, 0x539c, 0xc314, 0x5390, 0xc310,
    0x5384, 0xc30c, 0x5378, 0xc308, 0x536c, 0xc305, 0x5360, 0xc301,
    0x5354, 0xc2fd, 0x5348, 0xc2f9, 0x533c, 0xc2f5, 0x5330, 0xc2f2,
    0x5324, 0xc2ee, 0x5318, 0xc2ea, 0x530c, 0xc2e6, 0x5300, 0xc2e3,
    0x52f4, 0xc2df, 0x52e8, 0xc2db, 0x52dc, 0xc2d8, 0x52d0, 0xc2d4,
    0x52c4, 0xc2d0, 0x52b8, 0xc2cc, 0x52ac, 0xc2c9, 0x52a0, 0xc2c5,
    0x5294, 0xc2c1, 0x5288, 0xc2be, 0x527c, 0xc2ba, 0x5270, 0xc2b7,
    0x5264, 0xc2b3, 0x5258, 0xc2af, 0x524c, 0xc2ac, 0x5240, 0xc2a8,
    0x5234, 0xc2a5, 0x5228, 0xc2a1, 0x521c, 0xc29d, 0x5210, 0xc29a,
    0x5204, 0xc296, 0x51f7, 0xc293, 0x51eb, 0xc28f, 0x51df, 0xc28c,
    0x51d3, 0xc288, 0x51c7, 0xc285, 0x51bb, 0xc281, 0x51af, 0xc27e,
    0x51a3, 0xc27a, 0x5197, 0xc277, 0x518b, 0xc273, 0x517f, 0xc270,
    0x5173, 0xc26d, 0x5167, 0xc269, 0x515a, 0xc266, 0x514e, 0xc262,
    0x5142, 0xc25f, 0x5136, 0xc25c, 0x512a, 0xc258, 0x511e, 0xc255,
    0x5112, 0xc251, 0x5106, 0xc24e, 0x50fa, 0xc24b, 0x50ed, 0xc247,
    0x50e1, 0xc244, 0x50d5, 0xc241, 0x50c9, 0xc23e, 0x50bd, 0xc23a,
    0x50b1, 0xc237, 0x50a5, 0xc234, 0x5099, 0xc230, 0x508c, 0xc22d,
    0x5080, 0xc22a, 0x5074, 0xc227, 0x5068, 0xc223, 0x505c, 0xc220,
    0x5050, 0xc21d, 0x5044, 0xc21a, 0x5037, 0xc217, 0x502b, 0xc213,
    0x501f, 0xc210, 0x5013, 0xc20d, 0x5007, 0xc20a, 0x4ffb, 0xc207,
    0x4fee, 0xc204, 0x4fe2, 0xc201, 0x4fd6, 0xc1fd, 0x4fca, 0xc1fa,
    0x4fbe, 0xc1f7, 0x4fb2, 0xc1f4, 0x4fa5, 0xc1f1, 0x4f99, 0xc1ee,
    0x4f8d, 0xc1eb, 0x4f81, 0xc1e8, 0x4f75, 0xc1e5, 0x4f68, 0xc1e2,
    0x4f5c, 0xc1df, 0x4f50, 0xc1dc, 0x4f44, 0xc1d9, 0x4f38, 0xc1d6,
    0x4f2b, 0xc1d3, 0x4f1f, 0xc1d0, 0x4f13, 0xc1cd, 0x4f07, 0xc1ca,
    0x4efb, 0xc1c7, 0x4eee, 0xc1c4, 0x4ee2, 0xc1c1, 0x4ed6, 0xc1be,
    0x4eca, 0xc1bb, 0x4ebd, 0xc1b8, 0x4eb1, 0xc1b6, 0x4ea5, 0xc1b3,
    0x4e99, 0xc1b0, 0x4e8c, 0xc1ad, 0x4e80, 0xc1aa, 0x4e74, 0xc1a7,
    0x4e68, 0xc1a4, 0x4e5c, 0xc1a2, 0x4e4f, 0xc19f, 0x4e43, 0xc19c,
    0x4e37, 0xc199, 0x4e2b, 0xc196, 0x4e1e, 0xc194, 0x4e12, 0xc191,
    0x4e06, 0xc18e, 0x4df9, 0xc18b, 0x4ded, 0xc189, 0x4de1, 0xc186,
    0x4dd5, 0xc183, 0x4dc8, 0xc180, 0x4dbc, 0xc17e, 0x4db0, 0xc17b,
    0x4da4, 0xc178, 0x4d97, 0xc176, 0x4d8b, 0xc173, 0x4d7f, 0xc170,
    0x4d72, 0xc16e, 0x4d66, 0xc16b, 0x4d5a, 0xc168, 0x4d4e, 0xc166,
    0x4d41, 0xc163, 0x4d35, 0xc161, 0x4d29, 0xc15e, 0x4d1c, 0xc15b,
    0x4d10, 0xc159, 0x4d04, 0xc156, 0x4cf8, 0xc154, 0x4ceb, 0xc151,
    0x4cdf, 0xc14f, 0x4cd3, 0xc14c, 0x4cc6, 0xc14a, 0x4cba, 0xc147,
    0x4cae, 0xc145, 0x4ca1, 0xc142, 0x4c95, 0xc140, 0x4c89, 0xc13d,
    0x4c7c, 0xc13b, 0x4c70, 0xc138, 0x4c64, 0xc136, 0x4c57, 0xc134,
    0x4c4b, 0xc131, 0x4c3f, 0xc12f, 0x4c32, 0xc12c, 0x4c26, 0xc12a,
    0x4c1a, 0xc128, 0x4c0d, 0xc125, 0x4c01, 0xc123, 0x4bf5, 0xc120,
    0x4be8, 0xc11e, 0x4bdc, 0xc11c, 0x4bd0, 0xc119, 0x4bc3, 0xc117,
    0x4bb7, 0xc115, 0x4bab, 0xc113, 0x4b9e, 0xc110, 0x4b92, 0xc10e,
    0x4b85, 0xc10c, 0x4b79, 0xc109, 0x4b6d, 0xc107, 0x4b60, 0xc105,
    0x4b54, 0xc103, 0x4b48, 0xc100, 0x4b3b, 0xc0fe, 0x4b2f, 0xc0fc,
    0x4b23, 0xc0fa, 0x4b16, 0xc0f8, 0x4b0a, 0xc0f6, 0x4afd, 0xc0f3,
    0x4af1, 0xc0f1, 0x4ae5, 0xc0ef, 0x4ad8, 0xc0ed, 0x4acc, 0xc0eb,
    0x4ac0, 0xc0e9, 0x4ab3, 0xc0e7, 0x4aa7, 0xc0e4, 0x4a9a, 0xc0e2,
    0x4a8e, 0xc0e0, 0x4a82, 0xc0de, 0x4a75, 0xc0dc, 0x4a69, 0xc0da,
    0x4a5c, 0xc0d8, 0x4a50, 0xc0d6, 0x4a44, 0xc0d4, 0x4a37, 0xc0d2,
    0x4a2b, 0xc0d0, 0x4a1e, 0xc0ce, 0x4a12, 0xc0cc, 0x4a06, 0xc0ca,
    0x49f9, 0xc0c8, 0x49ed, 0xc0c6, 0x49e0, 0xc0c4, 0x49d4, 0xc0c2,
    0x49c7, 0xc0c0, 0x49bb, 0xc0be, 0x49af, 0xc0bd, 0x49a2, 0xc0bb,
    0x4996, 0xc0b9, 0x4989, 0xc0b7, 0x497d, 0xc0b5, 0x4970, 0xc0b3,
    0x4964, 0xc0b1, 0x4958, 0xc0af, 0x494b, 0xc0ae, 0x493f, 0xc0ac,
    0x4932, 0xc0aa, 0x4926, 0xc0a8, 0x4919, 0xc0a6, 0x490d, 0xc0a5,
    0x4901, 0xc0a3, 0x48f4, 0xc0a1, 0x48e8, 0xc09f, 0x48db, 0xc09e,
    0x48cf, 0xc09c, 0x48c2, 0xc09a, 0x48b6, 0xc098, 0x48a9, 0xc097,
    0x489d, 0xc095, 0x4891, 0xc093, 0x4884, 0xc092, 0x4878, 0xc090,
    0x486b, 0xc08e, 0x485f, 0xc08d, 0x4852, 0xc08b, 0x4846, 0xc089,
    0x4839, 0xc088, 0x482d, 0xc086, 0x4820, 0xc085, 0x4814, 0xc083,
    0x4807, 0xc081, 0x47fb, 0xc080, 0x47ef, 0xc07e, 0x47e2, 0xc07d,
    0x47d6, 0xc07b, 0x47c9, 0xc07a, 0x47bd, 0xc078, 0x47b0, 0xc077,
    0x47a4, 0xc075, 0x4797, 0xc074, 0x478b, 0xc072, 0x477e, 0xc071,
    0x4772, 0xc06f, 0x4765, 0xc06e, 0x4759, 0xc06c, 0x474c, 0xc06b,
    0x4740, 0xc069, 0x4733, 0xc068, 0x4727, 0xc067, 0x471a, 0xc065,
    0x470e, 0xc064, 0x4701, 0xc062, 0x46f5, 0xc061, 0x46e8, 0xc060,
    0x46dc, 0xc05e, 0x46cf, 0xc05d, 0x46c3, 0xc05c, 0x46b6, 0xc05a,
    0x46aa, 0xc059, 0x469d, 0xc058, 0x4691, 0xc056, 0x4684, 0xc055,
    0x4678, 0xc054, 0x466b, 0xc053, 0x465f, 0xc051, 0x4652, 0xc050,
    0x4646, 0xc04f, 0x4639, 0xc04e, 0x462d, 0xc04c, 0x4620, 0xc04b,
    0x4614, 0xc04a, 0x4607, 0xc049, 0x45fb, 0xc048, 0x45ee, 0xc047,
    0x45e2, 0xc045, 0x45d5, 0xc044, 0x45c9, 0xc043, 0x45bc, 0xc042,
    0x45b0, 0xc041, 0x45a3, 0xc040, 0x4597, 0xc03f, 0x458a, 0xc03d,
    0x457e, 0xc03c, 0x4571, 0xc03b, 0x4565, 0xc03a, 0x4558, 0xc039,
    0x454c, 0xc038, 0x453f, 0xc037, 0x4533, 0xc036, 0x4526, 0xc035,
    0x451a, 0xc034, 0x450d, 0xc033, 0x4500, 0xc032, 0x44f4, 0xc031,
    0x44e7, 0xc030, 0x44db, 0xc02f, 0x44ce, 0xc02e, 0x44c2, 0xc02d,
    0x44b5, 0xc02c, 0x44a9, 0xc02b, 0x449c, 0xc02b, 0x4490, 0xc02a,
    0x4483, 0xc029, 0x4477, 0xc028, 0x446a, 0xc027, 0x445e, 0xc026,
    0x4451, 0xc025, 0x4444, 0xc024, 0x4438, 0xc024, 0x442b, 0xc023,
    0x441f, 0xc022, 0x4412, 0xc021, 0x4406, 0xc020, 0x43f9, 0xc020,
    0x43ed, 0xc01f, 0x43e0, 0xc01e, 0x43d4, 0xc01d, 0x43c7, 0xc01d,
    0x43bb, 0xc01c, 0x43ae, 0xc01b, 0x43a1, 0xc01a, 0x4395, 0xc01a,
    0x4388, 0xc019, 0x437c, 0xc018, 0x436f, 0xc018, 0x4363, 0xc017,
    0x4356, 0xc016, 0x434a, 0xc016, 0x433d, 0xc015, 0x4330, 0xc014,
    0x4324, 0xc014, 0x4317, 0xc013, 0x430b, 0xc013, 0x42fe, 0xc012,
    0x42f2, 0xc011, 0x42e5, 0xc011, 0x42d9, 0xc010, 0x42cc, 0xc010,
    0x42c0, 0xc00f, 0x42b3, 0xc00f, 0x42a6, 0xc00e, 0x429a, 0xc00e,
    0x428d, 0xc00d, 0x4281, 0xc00d, 0x4274, 0xc00c, 0x4268, 0xc00c,
    0x425b, 0xc00b, 0x424e, 0xc00b, 0x4242, 0xc00a, 0x4235, 0xc00a,
    0x4229, 0xc009, 0x421c, 0xc009, 0x4210, 0xc009, 0x4203, 0xc008,
    0x41f7, 0xc008, 0x41ea, 0xc007, 0x41dd, 0xc007, 0x41d1, 0xc007,
    0x41c4, 0xc006, 0x41b8, 0xc006, 0x41ab, 0xc006, 0x419f, 0xc005,
    0x4192, 0xc005, 0x4186, 0xc005, 0x4179, 0xc004, 0x416c, 0xc004,
    0x4160, 0xc004, 0x4153, 0xc004, 0x4147, 0xc003, 0x413a, 0xc003,
    0x412e, 0xc003, 0x4121, 0xc003, 0x4114, 0xc002, 0x4108, 0xc002,
    0x40fb, 0xc002, 0x40ef, 0xc002, 0x40e2, 0xc002, 0x40d6, 0xc001,
    0x40c9, 0xc001, 0x40bc, 0xc001, 0x40b0, 0xc001, 0x40a3, 0xc001,
    0x4097, 0xc001, 0x408a, 0xc001, 0x407e, 0xc000, 0x4071, 0xc000,
    0x4065, 0xc000, 0x4058, 0xc000, 0x404b, 0xc000, 0x403f, 0xc000,
    0x4032, 0xc000, 0x4026, 0xc000, 0x4019, 0xc000, 0x400d, 0xc000,
};

/**    
* @brief  Initialization function for the Q15 RFFT/RIFFT.   
* @param[in, out] *S             points to an instance of the Q15 RFFT/RIFFT structure.   
* @param[in]      fftLenReal     length of the FFT.   
* @param[in]      ifftFlagR      flag that selects forward (ifftFlagR=0) or inverse (ifftFlagR=1) transform.   
* @param[in]      bitReverseFlag flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output.   
* @return		The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLenReal</code> is not a supported value.   
*    
* \par Description:   
* \par   
* The parameter <code>fftLenReal</code>	Specifies length of RFFT/RIFFT Process. Supported FFT Lengths are 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192.    
* \par    
* The parameter <code>ifftFlagR</code> controls whether a forward or inverse transform is computed.    
* Set(=1) ifftFlagR to calculate RIFFT, otherwise RFFT is calculated.    
* \par    
* The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order.    
* Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order.   
* \par    
* This function also initializes Twiddle factor table.     
*/
arm_status arm_rfft_init_q15(
    arm_rfft_instance_q15 * S,
    uint32_t fftLenReal,
    uint32_t ifftFlagR,
    uint32_t bitReverseFlag)
{
    /*  Initialise the default arm status */
    arm_status status = ARM_MATH_SUCCESS;

    /*  Initialize the Real FFT length */
    S->fftLenReal = (uint16_t) fftLenReal;

    /*  Initialize the Twiddle coefficientA pointer */
    S->pTwiddleAReal = (q15_t *) realCoefAQ15;

    /*  Initialize the Twiddle coefficientB pointer */
    S->pTwiddleBReal = (q15_t *) realCoefBQ15;

    /*  Initialize the Flag for selection of RFFT or RIFFT */
    S->ifftFlagR = (uint8_t) ifftFlagR;

    /*  Initialize the Flag for calculation Bit reversal or not */
    S->bitReverseFlagR = (uint8_t) bitReverseFlag;

    /*  Initialization of coef modifier depending on the FFT length */
    switch (S->fftLenReal)
    {
    case 8192u:
        S->twidCoefRModifier = 1u;
        S->pCfft = &arm_cfft_sR_q15_len4096;
        break;
    case 4096u:
        S->twidCoefRModifier = 2u;
        S->pCfft = &arm_cfft_sR_q15_len2048;
        break;
    case 2048u:
        S->twidCoefRModifier = 4u;
        S->pCfft = &arm_cfft_sR_q15_len1024;
        break;
    case 1024u:
        S->twidCoefRModifier = 8u;
        S->pCfft = &arm_cfft_sR_q15_len512;
        break;
    case 512u:
        S->twidCoefRModifier = 16u;
        S->pCfft = &arm_cfft_sR_q15_len256;
        break;
    case 256u:
        S->twidCoefRModifier = 32u;
        S->pCfft = &arm_cfft_sR_q15_len128;
        break;
    case 128u:
        S->twidCoefRModifier = 64u;
        S->pCfft = &arm_cfft_sR_q15_len64;
        break;
    case 64u:
        S->twidCoefRModifier = 128u;
        S->pCfft = &arm_cfft_sR_q15_len32;
        break;
    case 32u:
        S->twidCoefRModifier = 256u;
        S->pCfft = &arm_cfft_sR_q15_len16;
        break;
    default:
        /*  Reporting argument error if rfftSize is not valid value */
        status = ARM_MATH_ARGUMENT_ERROR;
        break;
    }

    /* return the status of RFFT Init function */
    return (status);
}

/**    
* @} end of RealFFT group    
*/
