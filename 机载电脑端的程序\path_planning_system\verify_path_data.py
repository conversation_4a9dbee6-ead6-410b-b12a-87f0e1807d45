#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证路径数据的准确性
"""

import json
from collections import Counter

def analyze_path_data():
    """分析路径数据的详细情况"""
    print("🔍 深度分析路径数据")
    print("=" * 70)
    
    # 读取预计算数据
    with open('precomputed_paths_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    path_data = data['path_data']
    
    print(f"总路径数: {len(path_data)}")
    print()
    
    # 分析前几条路径的详细情况
    for i in range(min(3, len(path_data))):
        path_info = path_data[i]
        no_fly_zones = path_info['no_fly_zones']
        path_sequence = path_info['path_sequence']
        
        print(f"📍 路径 {i+1}: 禁飞区 {no_fly_zones}")
        print(f"   路径长度: {len(path_sequence)} 个点")
        print(f"   起点: {path_sequence[0]}")
        print(f"   终点: {path_sequence[-1]}")
        
        # 检查重复点
        point_counts = Counter(path_sequence)
        duplicates = {point: count for point, count in point_counts.items() if count > 1}
        
        if duplicates:
            print(f"   ⚠️  重复访问的点: {duplicates}")
            print(f"   实际唯一点数: {len(point_counts)}")
        else:
            print(f"   ✅ 无重复点，唯一点数: {len(point_counts)}")
        
        # 检查是否覆盖了所有非禁飞区点
        all_points = set(range(11, 98))  # A1B1到A9B7的所有点
        # 移除无效点（如A1B8, A1B9等）
        valid_points = set()
        for pos_code in all_points:
            col = pos_code // 10
            row = pos_code % 10
            if 1 <= col <= 9 and 1 <= row <= 7:
                valid_points.add(pos_code)
        
        # 移除禁飞区
        accessible_points = valid_points - set(no_fly_zones)
        visited_points = set(path_sequence)
        
        print(f"   总可访问点数: {len(accessible_points)}")
        print(f"   实际访问点数: {len(visited_points)}")
        print(f"   覆盖率: {len(visited_points)/len(accessible_points)*100:.1f}%")
        
        # 检查未访问的点
        unvisited = accessible_points - visited_points
        if unvisited:
            print(f"   ❌ 未访问的点: {sorted(unvisited)}")
        else:
            print(f"   ✅ 完全覆盖所有可访问点")
        
        # 检查访问了不应该访问的点
        invalid_visits = visited_points - accessible_points
        if invalid_visits:
            print(f"   ❌ 访问了禁飞区或无效点: {sorted(invalid_visits)}")
        
        print(f"   前10个路径点: {path_sequence[:10]}")
        print(f"   后10个路径点: {path_sequence[-10:]}")
        print()
    
    # 统计所有路径的情况
    print("📊 全体路径统计")
    print("=" * 50)
    
    path_lengths = []
    unique_point_counts = []
    coverage_rates = []
    has_duplicates_count = 0
    
    for path_info in path_data:
        path_sequence = path_info['path_sequence']
        no_fly_zones = path_info['no_fly_zones']
        
        path_lengths.append(len(path_sequence))
        
        # 计算唯一点数
        unique_points = len(set(path_sequence))
        unique_point_counts.append(unique_points)
        
        # 检查重复
        if len(path_sequence) != unique_points:
            has_duplicates_count += 1
        
        # 计算覆盖率
        all_valid_points = set()
        for pos_code in range(11, 98):
            col = pos_code // 10
            row = pos_code % 10
            if 1 <= col <= 9 and 1 <= row <= 7:
                all_valid_points.add(pos_code)
        
        accessible_points = all_valid_points - set(no_fly_zones)
        visited_points = set(path_sequence)
        coverage_rate = len(visited_points) / len(accessible_points) * 100
        coverage_rates.append(coverage_rate)
    
    print(f"路径长度范围: {min(path_lengths)} - {max(path_lengths)}")
    print(f"唯一点数范围: {min(unique_point_counts)} - {max(unique_point_counts)}")
    print(f"有重复点的路径数: {has_duplicates_count}/{len(path_data)}")
    print(f"覆盖率范围: {min(coverage_rates):.1f}% - {max(coverage_rates):.1f}%")
    
    # 检查是否所有路径都是60个唯一点
    if all(count == 60 for count in unique_point_counts):
        print("✅ 所有路径都恰好访问60个唯一点")
    else:
        print("❌ 路径的唯一点数不一致")
        unique_counts = Counter(unique_point_counts)
        print(f"   唯一点数分布: {dict(unique_counts)}")

def verify_dijkstra_behavior():
    """验证Dijkstra算法的行为"""
    print("\n🔍 验证Dijkstra算法行为")
    print("=" * 70)
    
    from algorithms.dijkstra import DijkstraPlanner
    from core.grid_map import GridMap
    
    # 创建测试场景
    grid_map = GridMap()
    no_fly_zones = [33, 34, 35]  # A3B3, A3B4, A3B5
    grid_map.set_no_fly_zones(no_fly_zones)
    
    planner = DijkstraPlanner()
    start_pos = (0, 8)  # A9B1
    
    result = planner.plan_path(grid_map, start_pos)
    
    if result.is_valid():
        path_sequence = result.path_sequence
        print(f"测试路径长度: {len(path_sequence)}")
        print(f"唯一点数: {len(set(path_sequence))}")
        
        # 检查是否是真正的路径（相邻点之间的移动）
        print("\n检查路径连续性（前20步）:")
        for i in range(min(19, len(path_sequence)-1)):
            current = path_sequence[i]
            next_point = path_sequence[i+1]
            
            # 转换为网格坐标
            curr_row, curr_col = grid_map.position_code_to_grid(current)
            next_row, next_col = grid_map.position_code_to_grid(next_point)
            
            # 计算移动距离
            row_diff = abs(next_row - curr_row)
            col_diff = abs(next_col - curr_col)
            
            # 检查是否是相邻移动
            is_adjacent = (row_diff <= 1 and col_diff <= 1) and (row_diff + col_diff > 0)
            
            print(f"  {current}->({curr_row},{curr_col}) → {next_point}->({next_row},{next_col}) "
                  f"距离:({row_diff},{col_diff}) {'✅' if is_adjacent else '❌跳跃'}")
    
    print("\n💡 结论:")
    print("如果看到很多'❌跳跃'，说明这不是真正的连续路径，")
    print("而是访问点的顺序列表（可能包含跳跃移动）。")

if __name__ == "__main__":
    analyze_path_data()
    verify_dijkstra_behavior()
