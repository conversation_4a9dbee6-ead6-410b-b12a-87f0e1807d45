T8CF4 000:307.018   SEGGER J-Link V7.90 Log File
T8CF4 000:307.141   DLL Compiled: Aug  2 2023 15:18:43
T8CF4 000:307.152   Logging started @ 2025-07-08 17:18
T8CF4 000:307.161 - 307.166ms
T8CF4 000:307.176 JLINK_SetWarnOutHandler(...)
T8CF4 000:307.187 - 0.015ms
T8CF4 000:307.199 JLINK_OpenEx(...)
T8CF4 000:310.220   SERVER_Parent Server (S/N 174504307) is using protocol version 50203 which is too old (>= 54110 required). Closing connection to server.
T8CF4 000:310.783   Firmware: J-Link Pro V4 compiled Sep 22 2022 15:00:37
T8CF4 000:310.959   Firmware: J-Link Pro V4 compiled Sep 22 2022 15:00:37
T8CF4 000:311.079   Decompressing FW timestamp took 97 us
T8CF4 000:311.976   Hardware: V4.00
T8CF4 000:311.992   S/N: 174504307
T8CF4 000:312.006   OEM: SEGGER
T8CF4 000:312.019   Feature(s): <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, GDB
T8CF4 000:312.294   Bootloader: (Could not read)
T8CF4 000:323.430   TELNET listener socket opened on port 19021
T8CF4 000:323.725   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T8CF4 000:323.898   WEBSRV Webserver running on local port 19080
T8CF4 000:324.111   Looking for J-Link GUI Server exe at: D:\keil5\ARM\Segger\JLinkGUIServer.exe
T8CF4 000:324.333   Looking for J-Link GUI Server exe at: C:\Program Files\SEGGER\JLink\JLinkGUIServer.exe
T8CF4 000:324.470   Forking J-Link GUI Server: C:\Program Files\SEGGER\JLink\JLinkGUIServer.exe
T8CF4 000:329.083   J-Link GUI Server info: "J-Link GUI server V7.90 "
T8CF4 000:329.359 - 22.169ms returns "O.K."
T8CF4 000:329.387 JLINK_SetErrorOutHandler(...)
T8CF4 000:329.397 - 0.014ms
T8CF4 000:329.419 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\ANO_LX_FC_PROv2.0\ProjectSTM32F429\JLinkSettings.ini"", ...). 
T8CF4 000:339.549   Ref file found at: D:\keil5\ARM\Segger\JLinkDevices.ref
T8CF4 000:339.877   REF file references invalid XML file: C:\Program Files\SEGGER\JLink\JLinkDevices.xml
T8CF4 000:340.564 - 11.153ms returns 0x00
T8CF4 000:340.594 JLINK_ExecCommand("Device = STM32F429VGTx", ...). 
T8CF4 000:341.045   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
T8CF4 000:341.063     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
T8CF4 000:341.918   Device "STM32F429VG" selected.
T8CF4 000:342.475 - 1.877ms returns 0x00
T8CF4 000:342.499 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T8CF4 000:342.514 - 0.006ms returns 0x01
T8CF4 000:342.526 JLINK_GetHardwareVersion()
T8CF4 000:342.536 - 0.014ms returns 40000
T8CF4 000:342.547 JLINK_GetDLLVersion()
T8CF4 000:342.559 - 0.016ms returns 79000
T8CF4 000:342.570 JLINK_GetOEMString(...)
T8CF4 000:342.583 JLINK_GetFirmwareString(...)
T8CF4 000:342.592 - 0.013ms
T8CF4 000:342.606 JLINK_GetDLLVersion()
T8CF4 000:342.615 - 0.013ms returns 79000
T8CF4 000:342.626 JLINK_GetCompileDateTime()
T8CF4 000:342.635 - 0.013ms
T8CF4 000:342.649 JLINK_GetFirmwareString(...)
T8CF4 000:342.658 - 0.013ms
T8CF4 000:342.669 JLINK_GetHardwareVersion()
T8CF4 000:342.678 - 0.013ms returns 40000
T8CF4 000:342.690 JLINK_GetSN()
T8CF4 000:342.699 - 0.013ms returns 174504307
T8CF4 000:342.711 JLINK_GetOEMString(...)
T8CF4 000:342.725 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T8CF4 000:343.082 - 0.366ms returns 0x00
T8CF4 000:343.101 JLINK_HasError()
T8CF4 000:343.120 JLINK_SetSpeed(10000)
T8CF4 000:343.171 - 0.057ms
T8CF4 000:343.669 JLINK_HasError()
T8CF4 000:343.687 JLINK_SetResetType(JLINKARM_RESET_TYPE_NORMAL)
T8CF4 000:343.697 - 0.013ms returns JLINKARM_RESET_TYPE_NORMAL
T8CF4 000:343.708 JLINK_Reset()
T8CF4 000:343.957   InitTarget() start
T8CF4 000:343.975    J-Link Script File: Executing InitTarget()
T8CF4 000:344.077   SWD selected. Executing JTAG -> SWD switching sequence.
T8CF4 000:344.823   DAP initialized successfully.
T8CF4 000:347.003   WARNING: Unknown DEV_ID found: 0x00000414
T8CF4 000:347.022   Security detection skipped.
T8CF4 000:347.204   InitTarget() end - Took 3.22ms
T8CF4 000:347.410   Found SW-DP with ID 0x1BA01477
T8CF4 000:348.346   DPIDR: 0x1BA01477
T8CF4 000:348.364   CoreSight SoC-400 or earlier
T8CF4 000:348.415   Scanning AP map to find all available APs
T8CF4 000:348.671   AP[1]: Stopped AP scan as end of AP map has been reached
T8CF4 000:348.691   AP[0]: AHB-AP (IDR: 0x14770011)
T8CF4 000:348.704   Iterating through AP map to find AHB-AP to use
T8CF4 000:348.955   AP[0]: Core found
T8CF4 000:348.971   AP[0]: AHB-AP ROM base: 0xE00FF000
T8CF4 000:349.108   CPUID register: 0x411FC231. Implementer code: 0x41 (ARM)
T8CF4 000:349.125   Found Cortex-M3 r1p1, Little endian.
T8CF4 000:349.146   
  ***** Warning: 
T8CF4 000:349.159   Identified core does not match configuration. (Found: Cortex-M3, Configured: Cortex-M4)
T8CF4 000:349.321   -- Max. mem block: 0x0000CDF0
T8CF4 000:349.606   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8CF4 000:349.740   CPU_ReadMem(4 bytes @ 0xE0002000)
T8CF4 000:349.874   FPUnit: 6 code (BP) slots and 2 literal slots
T8CF4 000:349.889   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T8CF4 000:350.023   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8CF4 000:350.181   CPU_ReadMem(4 bytes @ 0xE0001000)
T8CF4 000:350.322   CPU_WriteMem(4 bytes @ 0xE0001000)
T8CF4 000:350.460   CPU_ReadMem(4 bytes @ 0xE000ED88)
T8CF4 000:350.617   CPU_WriteMem(4 bytes @ 0xE000ED88)
T8CF4 000:350.762   CPU_ReadMem(4 bytes @ 0xE000ED88)
T8CF4 000:350.897   CPU_WriteMem(4 bytes @ 0xE000ED88)
T8CF4 000:351.084   CoreSight components:
T8CF4 000:351.108   ROMTbl[0] @ E00FF000
T8CF4 000:351.125   CPU_ReadMem(64 bytes @ 0xE00FF000)
T8CF4 000:351.369   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T8CF4 000:351.659   [0][0]: E000E000 CID B105E00D PID 001BB000 SCS
T8CF4 000:351.677   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T8CF4 000:351.862   [0][1]: E0001000 CID B105E00D PID 001BB002 DWT
T8CF4 000:351.880   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T8CF4 000:352.076   [0][2]: E0002000 CID B105E00D PID 000BB003 FPB
T8CF4 000:352.093   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T8CF4 000:352.276   [0][3]: E0000000 CID B105E00D PID 001BB001 ITM
T8CF4 000:352.290   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T8CF4 000:352.467   [0][4]: E0040000 CID B105900D PID 001BB923 TPIU-Lite
T8CF4 000:352.481   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T8CF4 000:352.669   [0][5]: E0041000 CID B105900D PID 101BB924 ETM-M3
T8CF4 000:352.717   CPU is running
T8CF4 000:352.734   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8CF4 000:352.874   CPU is running
T8CF4 000:352.889   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8CF4 000:353.039   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T8CF4 000:353.245   Reset: Reset device via AIRCR.SYSRESETREQ.
T8CF4 000:353.265   CPU is running
T8CF4 000:353.279   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T8CF4 000:405.252   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8CF4 000:405.411   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8CF4 000:406.846   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8CF4 000:412.342   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8CF4 000:413.462   CPU_WriteMem(4 bytes @ 0xE0002000)
T8CF4 000:413.623   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T8CF4 000:413.759   CPU_ReadMem(4 bytes @ 0xE0001000)
T8CF4 000:413.896 - 70.195ms
T8CF4 000:413.917 JLINK_GetId()
T8CF4 000:414.020 - 0.109ms returns 0x1BA01477
T8CF4 000:420.659 JLINK_GetFirmwareString(...)
T8CF4 000:420.688 - 0.034ms
T8CF4 032:703.631 JLINK_Close()
T8CF4 032:703.873   OnDisconnectTarget() start
T8CF4 032:703.898    J-Link Script File: Executing OnDisconnectTarget()
T8CF4 032:703.919   CPU_WriteMem(4 bytes @ 0xE0042004)
T8CF4 032:704.081   CPU_WriteMem(4 bytes @ 0xE0042008)
T8CF4 032:704.232   OnDisconnectTarget() end - Took 332us
T8CF4 032:704.249   CPU_ReadMem(4 bytes @ 0xE0001000)
T8CF4 032:704.395   CPU_WriteMem(4 bytes @ 0xE0001004)
T8CF4 032:705.367 - 1.745ms
T8CF4 032:705.381   
T8CF4 032:705.390   Closed
