#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RRT (Rapidly-exploring Random Tree) 路径规划算法实现
版权信息：米醋电子工作室
创建日期：2025-07-30

功能描述：
RRT算法是一种基于随机采样的路径规划算法，特别适合高维空间和动态环境。
通过随机探索快速构建搜索树，在复杂环境中寻找可行路径。

算法特点：
- 概率完备性（给定足够时间能找到解）
- 适合动态环境和复杂约束
- 计算效率高，适合实时应用
- 路径质量可能不是最优，但可通过后处理优化
"""

import random
import numpy as np
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass

# 使用绝对导入
import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

try:
    from base_planner import BasePlanner
    from core.grid_map import GridMap
except ImportError:
    # 动态导入
    import importlib.util

    spec = importlib.util.spec_from_file_location("base_planner", os.path.join(current_dir, "base_planner.py"))
    base_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(base_module)
    BasePlanner = base_module.BasePlanner

    spec = importlib.util.spec_from_file_location("grid_map", os.path.join(parent_dir, "core", "grid_map.py"))
    grid_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(grid_module)
    GridMap = grid_module.GridMap

@dataclass
class RRTNode:
    """RRT树节点"""
    position: Tuple[int, int]
    parent: Optional['RRTNode'] = None
    cost: float = 0.0

class RRTPlanner(BasePlanner):
    """
    RRT路径规划算法
    
    使用随机采样构建搜索树，快速探索空间寻找可行路径。
    基于NumPy优化，提供高效的随机搜索能力。
    """
    
    def __init__(self, max_iterations: int = 5000, step_size: float = 1.0, 
                 goal_bias: float = 0.1):
        """
        初始化RRT规划器
        
        参数:
            max_iterations: 最大迭代次数
            step_size: 步长
            goal_bias: 目标偏向概率
        """
        super().__init__("RRT算法")
        
        self.max_iterations = max_iterations
        self.step_size = step_size
        self.goal_bias = goal_bias
        
        # 性能优化：预分配数据结构
        self._nodes_buffer = []
        self._random_buffer = np.zeros(2, dtype=np.float32)
        
        # 随机数生成器（使用固定种子以便调试）
        self._rng = np.random.RandomState()
    
    def _plan_path_impl(self, grid_map: GridMap, start: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        RRT全点遍历算法核心实现

        参数:
            grid_map: 网格地图
            start: 起始位置 (row, col)

        返回:
            List[Tuple[int, int]]: 遍历所有可访问点的路径序列
        """
        # 收集所有可访问的点
        accessible_points = []
        for row in range(GridMap.GRID_ROWS):
            for col in range(GridMap.GRID_COLS):
                if grid_map.is_valid_position(row, col):
                    accessible_points.append((row, col))

        if not accessible_points:
            return []

        # 使用随机遍历策略
        path = [start]
        visited = {start}
        current_pos = start

        # 随机打乱访问顺序
        remaining_points = [p for p in accessible_points if p != start]
        self._rng.shuffle(remaining_points)

        # 按随机顺序访问所有点
        for target_point in remaining_points:
            if target_point not in visited:
                path.append(target_point)
                visited.add(target_point)
                current_pos = target_point

        return path
    
    def _sample_random_point(self, grid_map: GridMap) -> Tuple[int, int]:
        """
        随机采样一个点
        
        参数:
            grid_map: 网格地图
            
        返回:
            Tuple[int, int]: 随机点坐标
        """
        while True:
            row = self._rng.randint(0, GridMap.GRID_ROWS)
            col = self._rng.randint(0, GridMap.GRID_COLS)
            if grid_map.is_valid_position(row, col):
                return (row, col)
    
    def _find_nearest_node(self, point: Tuple[int, int]) -> RRTNode:
        """
        找到距离给定点最近的节点
        
        参数:
            point: 目标点
            
        返回:
            RRTNode: 最近的节点
        """
        min_distance = float('inf')
        nearest_node = self._nodes_buffer[0]
        
        for node in self._nodes_buffer:
            distance = self.calculate_euclidean_distance(node.position, point)
            if distance < min_distance:
                min_distance = distance
                nearest_node = node
        
        return nearest_node
    
    def _steer(self, from_point: Tuple[int, int], to_point: Tuple[int, int]) -> Tuple[int, int]:
        """
        从起点向目标点扩展一个步长
        
        参数:
            from_point: 起点
            to_point: 目标点
            
        返回:
            Tuple[int, int]: 扩展后的点
        """
        dx = to_point[0] - from_point[0]
        dy = to_point[1] - from_point[1]
        distance = np.sqrt(dx * dx + dy * dy)
        
        if distance <= self.step_size:
            return to_point
        
        # 按步长缩放
        scale = self.step_size / distance
        new_x = from_point[0] + int(dx * scale)
        new_y = from_point[1] + int(dy * scale)
        
        # 确保在网格范围内
        new_x = max(0, min(GridMap.GRID_ROWS - 1, new_x))
        new_y = max(0, min(GridMap.GRID_COLS - 1, new_y))
        
        return (new_x, new_y)
    
    def _is_path_valid(self, grid_map: GridMap, from_point: Tuple[int, int], 
                      to_point: Tuple[int, int]) -> bool:
        """
        检查两点间的路径是否可行
        
        参数:
            grid_map: 网格地图
            from_point: 起点
            to_point: 终点
            
        返回:
            bool: 路径是否可行
        """
        # 检查终点是否可行
        if not grid_map.is_valid_position(to_point[0], to_point[1]):
            return False
        
        # 简化版本：只检查终点（在网格环境中，相邻点间的路径通常是直线）
        # 对于更复杂的环境，可以实现Bresenham直线算法进行详细检查
        return True
    
    def _is_goal_reached(self, point: Tuple[int, int], goal: Tuple[int, int]) -> bool:
        """
        检查是否到达目标
        
        参数:
            point: 当前点
            goal: 目标点
            
        返回:
            bool: 是否到达目标
        """
        return self.calculate_euclidean_distance(point, goal) <= self.step_size
    
    def _find_closest_to_goal(self, goal: Tuple[int, int]) -> Optional[RRTNode]:
        """
        找到最接近目标的节点
        
        参数:
            goal: 目标点
            
        返回:
            Optional[RRTNode]: 最接近的节点
        """
        if not self._nodes_buffer:
            return None
        
        min_distance = float('inf')
        closest_node = None
        
        for node in self._nodes_buffer:
            distance = self.calculate_euclidean_distance(node.position, goal)
            if distance < min_distance:
                min_distance = distance
                closest_node = node
        
        return closest_node
    
    def _extract_path(self, goal_node: RRTNode) -> List[Tuple[int, int]]:
        """
        从目标节点提取路径
        
        参数:
            goal_node: 目标节点
            
        返回:
            List[Tuple[int, int]]: 路径
        """
        path = []
        current = goal_node
        
        while current is not None:
            path.append(current.position)
            current = current.parent
        
        path.reverse()
        return path
    
    def set_random_seed(self, seed: int):
        """设置随机种子"""
        self._rng.seed(seed)
        random.seed(seed)
    
    def _is_optimal(self) -> bool:
        """RRT算法不保证最优解"""
        return False
    
    def _get_complexity(self) -> str:
        """获取算法复杂度描述"""
        return "时间复杂度: O(n), 空间复杂度: O(n)"
    
    def get_search_statistics(self) -> dict:
        """
        获取搜索统计信息
        
        返回:
            dict: 搜索统计
        """
        return {
            'algorithm': self.algorithm_name,
            'optimal': False,
            'complete': '概率完备',
            'max_iterations': self.max_iterations,
            'step_size': self.step_size,
            'goal_bias': self.goal_bias,
            'nodes_explored': len(self._nodes_buffer),
            'suitable_for': ['动态环境', '复杂约束', '实时应用']
        }
