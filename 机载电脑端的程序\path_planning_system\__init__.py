#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径规划系统 - 智能无人机路径规划解决方案
版权信息：米醋电子工作室
创建日期：2025-07-30
编码格式：UTF-8

功能描述：
基于PC端的高性能路径规划系统，支持多种算法和性能对比。
替代单片机端的简陋路径规划，提供工业级的路径优化解决方案。

主要特性：
1. 多算法支持：A*、Dijkstra、RRT等先进算法
2. 性能对比：自动选择最优算法
3. 智能优化：基于场景特征的算法推荐
4. 高效通信：与单片机的可靠数据传输
"""

__version__ = "1.0.0"
__author__ = "Alex (工程师) - 米醋电子工作室"
__description__ = "智能路径规划系统 - 替代愚蠢的单片机路径规划"

# 导出主要类和函数
from .algorithms.astar import AStarPlanner
from .algorithms.dijkstra import DijkstraPlanner  
from .algorithms.rrt import RRTPlanner
from .core.grid_map import GridMap
from .core.path_result import PathResult

__all__ = [
    'AStarPlanner',
    'DijkstraPlanner', 
    'RRTPlanner',
    'GridMap',
    'PathResult'
]
