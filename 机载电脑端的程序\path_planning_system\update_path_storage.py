#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新path_storage.c文件，使用安全修正后的数据，并统一为60个点
"""

import json
from datetime import datetime

def load_safe_data():
    """加载安全修正后的数据"""
    with open('safe_optimized_return_paths.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data['path_data']

def truncate_to_60_points(path_sequence):
    """将路径截断为60个点"""
    if len(path_sequence) > 60:
        return path_sequence[:60]
    return path_sequence

def generate_updated_c_code():
    """生成更新后的C代码"""
    
    path_data = load_safe_data()
    
    # 生成C代码
    c_code = f'''/*==========================================================================
 * 版权    ：米醋电子工作室
 * 创建时间：{datetime.now().strftime("%Y-%m-%d")}
 * 作者    ：Alex (Engineer)
 * 功能描述：预计算路径存储模块 - 实现文件 (安全修正版)
 * 编码格式：UTF-8
 * 
 * 说明：
 * 本模块包含92种禁飞区组合的Dijkstra最优路径数据。
 * 数据大小：8188字节 ≈ 7.0KB
 * 查找性能：线性搜索，最坏情况92次比较，<1ms完成
 * 扩展功能：包含巡查路径和返航路径，支持安全返航到起点A9B1
 * 安全特性：已修正对角线移动安全问题，统一为60个巡查点
===========================================================================*/

#include "path_storage.h"
#include "ANO_DT.h"

/*==========================================================================
 * 预计算路径查找表
 * 
 * 数据来源：PC端Dijkstra算法预计算 + 安全修正
 * 生成时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
 * 数据格式：{{禁飞区[3], 巡查路径长度, 巡查路径序列[60], 返航路径长度, 返航路径序列[25]}}
 *
 * 注意：此数组为只读数据，存储在Flash中，不占用RAM
 * 安全特性：已修正所有对角线移动安全问题
===========================================================================*/
static const precomputed_path_t path_lookup_table[PRECOMPUTED_PATH_COUNT] = {{
'''
    
    # 生成每个路径的数据
    for i, path in enumerate(path_data):
        # 截断巡查路径为60个点
        patrol_path = truncate_to_60_points(path['patrol_path_sequence'])
        patrol_length = len(patrol_path)
        
        # 返航路径
        return_path = path['return_path_sequence']
        return_length = len(return_path)
        
        c_code += f'''    // 路径{i+1}: 禁飞区{path['no_fly_zones']}, 巡查长度{patrol_length}, 返航长度{return_length}
    {{
        {{{path['no_fly_zones'][0]}, {path['no_fly_zones'][1]}, {path['no_fly_zones'][2]}}},  // 禁飞区
        {patrol_length},  // 巡查路径长度
        {{  // 巡查路径序列
'''
        
        # 生成巡查路径数据，每行8个数字
        for j in range(0, len(patrol_path), 8):
            line_data = patrol_path[j:j+8]
            formatted_line = ', '.join(f'{x:3d}' for x in line_data)
            c_code += f'             {formatted_line}'
            if j + 8 < len(patrol_path):
                c_code += ',\n'
            else:
                c_code += '\n'
        
        c_code += f'''        }},
        {return_length},  // 返航路径长度
        {{  // 返航路径序列
'''
        
        # 生成返航路径数据，补齐到25个元素
        return_path_padded = return_path + [0] * (25 - len(return_path))
        for j in range(0, 25, 8):
            line_data = return_path_padded[j:j+8]
            formatted_line = ', '.join(f'{x:3d}' for x in line_data)
            c_code += f'             {formatted_line}'
            if j + 8 < 25:
                c_code += ',\n'
            else:
                c_code += '\n'
        
        c_code += '        }\n    }'
        
        if i < len(path_data) - 1:
            c_code += ',\n\n'
        else:
            c_code += '\n\n'
    
    # 添加函数实现部分
    c_code += '''};

/*==========================================================================
 * 函数实现
===========================================================================*/

/**
 * @brief 查找预计算的最优巡查路径
 * @param no_fly_zones 禁飞区数组（3个元素）
 * @param output_path 输出路径数组（调用者分配）
 * @return 路径长度，0表示未找到
 */
u8 find_precomputed_path(const u8 no_fly_zones[3], u8 output_path[MAX_PATH_LENGTH]) {
    // 参数验证
    if (!no_fly_zones || !output_path) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                      "find_precomputed_path: Invalid parameters");
        return 0;
    }
    
    // 线性搜索匹配的禁飞区组合
    for (int i = 0; i < PRECOMPUTED_PATH_COUNT; i++) {
        const precomputed_path_t* entry = &path_lookup_table[i];
        
        // 比较禁飞区（必须完全匹配）
        if (entry->no_fly_zones[0] == no_fly_zones[0] &&
            entry->no_fly_zones[1] == no_fly_zones[1] &&
            entry->no_fly_zones[2] == no_fly_zones[2]) {
            
            // 找到匹配项，复制路径数据
            u8 path_length = entry->path_length;
            
            for (int j = 0; j < path_length; j++) {
                output_path[j] = entry->path_sequence[j];
            }
            
            // 调试输出
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                          "find_precomputed_path: Found optimal patrol path");
            AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)path_length,
                             "Patrol path length:");

            return path_length;
        }
    }
    
    // 未找到匹配的禁飞区组合
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW,
                  "find_precomputed_path: No matching no-fly zone combination found");
    return 0;
}

/**
 * @brief 查找预计算的最优返航路径
 * @param no_fly_zones 禁飞区数组（3个元素）
 * @param output_path 输出路径数组（调用者分配）
 * @return 路径长度，0表示未找到
 */
u8 find_precomputed_return_path(const u8 no_fly_zones[3], u8 output_path[MAX_RETURN_LENGTH]) {
    // 参数验证
    if (!no_fly_zones || !output_path) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                      "find_precomputed_return_path: Invalid parameters");
        return 0;
    }
    
    // 线性搜索匹配的禁飞区组合
    for (int i = 0; i < PRECOMPUTED_PATH_COUNT; i++) {
        const precomputed_path_t* entry = &path_lookup_table[i];
        
        // 比较禁飞区（必须完全匹配）
        if (entry->no_fly_zones[0] == no_fly_zones[0] &&
            entry->no_fly_zones[1] == no_fly_zones[1] &&
            entry->no_fly_zones[2] == no_fly_zones[2]) {
            
            // 找到匹配项，复制返航路径数据
            u8 return_length = entry->return_length;
            
            for (int j = 0; j < return_length; j++) {
                output_path[j] = entry->return_sequence[j];
            }
            
            // 调试输出
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                          "find_precomputed_return_path: Found optimal return path");
            AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)return_length,
                             "Return path length:");

            return return_length;
        }
    }
    
    // 未找到匹配的禁飞区组合
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW,
                  "find_precomputed_return_path: No matching no-fly zone combination found");
    return 0;
}
'''
    
    return c_code

if __name__ == "__main__":
    print("正在生成更新后的C代码...")
    c_code = generate_updated_c_code()
    
    # 保存到文件
    output_file = "../../FcSrc/User/path_storage_updated.c"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(c_code)
    
    print(f"已生成更新后的C代码: {output_file}")
    print("请检查并替换原有的path_storage.c文件")
