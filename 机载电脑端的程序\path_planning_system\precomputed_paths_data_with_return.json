{"metadata": {"generation_time": "2025-07-31 15:00:00", "total_combinations": 92, "validation_report": {"total_combinations": 92, "valid_paths": 92, "invalid_paths": 0, "start_point_errors": 0, "coverage_errors": 0, "success_rate": 100.0}, "return_path_added": true}, "path_data": [{"no_fly_zones": [11, 21, 31], "path_sequence": [91, 81, 71, 61, 51, 41, 42, 32, 22, 12, 13, 23, 33, 43, 53, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.532699999999999, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [21, 31, 41], "path_sequence": [91, 81, 71, 61, 51, 52, 42, 32, 22, 12, 11, 13, 23, 33, 43, 53, 63, 62, 72, 82, 92, 93, 83, 73, 74, 64, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.39339999999999, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [31, 41, 51], "path_sequence": [91, 81, 71, 61, 62, 52, 42, 32, 22, 21, 11, 12, 13, 23, 33, 43, 53, 63, 73, 72, 82, 92, 93, 83, 84, 74, 64, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 75, 85, 95, 94, 96, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.0166999999999824, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [41, 51, 61], "path_sequence": [91, 81, 71, 72, 62, 52, 42, 32, 31, 21, 11, 12, 22, 23, 13, 14, 24, 34, 33, 43, 53, 63, 73, 83, 82, 92, 93, 94, 84, 74, 64, 54, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.020199999999972, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [51, 61, 71], "path_sequence": [91, 81, 82, 72, 62, 52, 42, 41, 31, 21, 11, 12, 22, 32, 33, 23, 13, 14, 24, 34, 44, 43, 53, 63, 73, 83, 93, 92, 94, 84, 74, 64, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "path_length": 60, "computation_time_ms": 2.0840999999999776, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [61, 71, 81], "path_sequence": [91, 92, 82, 72, 62, 52, 51, 41, 31, 21, 11, 12, 22, 32, 42, 43, 33, 23, 13, 14, 24, 34, 44, 54, 53, 63, 73, 83, 93, 94, 84, 74, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "path_length": 60, "computation_time_ms": 2.0537999999999945, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [12, 22, 32], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 13, 23, 33, 43, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 86, 96, 97], "path_length": 60, "computation_time_ms": 2.0096000000000003, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [22, 32, 42], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 13, 23, 33, 43, 53, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 54, 44, 34, 24, 14, 15, 25, 35, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.092499999999997, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [32, 42, 52], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 23, 13, 14, 24, 34, 33, 43, 53, 63, 62, 72, 82, 92, 93, 83, 73, 74, 64, 54, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.0298999999999734, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [42, 52, 62], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 33, 23, 13, 14, 24, 34, 44, 43, 53, 63, 73, 72, 82, 92, 93, 83, 84, 74, 64, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 94, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "path_length": 60, "computation_time_ms": 2.112500000000017, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [52, 62, 72], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 43, 33, 23, 13, 14, 24, 34, 44, 54, 53, 63, 73, 83, 82, 92, 93, 94, 84, 74, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "path_length": 60, "computation_time_ms": 2.0132999999999956, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [62, 72, 82], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 63, 73, 83, 93, 92, 94, 84, 74, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 85, 95, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.024999999999999, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 63, 73, 83, 92, 91], "return_length": 10}, {"no_fly_zones": [72, 82, 92], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 73, 83, 93, 94, 84, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 95, 97, 87, 77, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.0283000000000384, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [13, 23, 33], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 44, 34, 24, 14, 15, 25, 35, 45, 55, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 76, 86, 96, 97, 87], "path_length": 60, "computation_time_ms": 2.0926, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [87, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [23, 33, 43], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 44, 34, 24, 14, 13, 15, 25, 35, 45, 55, 65, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 86, 96, 97], "path_length": 60, "computation_time_ms": 2.031000000000005, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [33, 43, 53], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 54, 44, 34, 24, 23, 13, 14, 15, 25, 35, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.0185999999999815, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [43, 53, 63], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 74, 64, 54, 44, 34, 33, 23, 13, 14, 24, 25, 15, 16, 26, 36, 35, 45, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 56, 46, 47, 37, 27, 17, 57, 67, 77, 87, 97], "path_length": 60, "computation_time_ms": 4.044199999999998, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [53, 63, 73], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 84, 74, 64, 54, 44, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 65, 75, 85, 95, 94, 96, 86, 76, 66, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.2677999999999865, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [63, 73, 83], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 94, 84, 74, 64, 54, 53, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.0749999999999935, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [73, 83, 93], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 94, 84, 74, 64, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "path_length": 60, "computation_time_ms": 2.016099999999965, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 84, 94, 84, 94, 84, 94, 84, 94, 84, 94, 84, 94, 84, 94, 84, 94, 84, 91], "return_length": 22}, {"no_fly_zones": [14, 24, 34], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 15, 25, 35, 45, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "path_length": 60, "computation_time_ms": 2.2159999999999958, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [77, 86, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [24, 34, 44], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 15, 25, 35, 45, 55, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 76, 86, 96, 97, 87], "path_length": 60, "computation_time_ms": 2.1799000000000124, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [87, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [34, 44, 54], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 25, 15, 16, 26, 36, 35, 45, 55, 65, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 47, 37, 27, 17, 57, 67, 77, 87, 86, 96, 97], "path_length": 60, "computation_time_ms": 2.1960999999999786, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [44, 54, 64], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.039399999999969, "total_distance": 63.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [54, 64, 74], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.0352000000000148, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [64, 74, 84], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 94, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "path_length": 60, "computation_time_ms": 2.0212000000000008, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [74, 84, 94], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "path_length": 60, "computation_time_ms": 2.3447999999999802, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 85, 95, 85, 95, 85, 95, 85, 95, 85, 95, 85, 95, 85, 95, 85, 95, 85, 95, 91], "return_length": 22}, {"no_fly_zones": [15, 25, 35], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 36, 26, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "path_length": 60, "computation_time_ms": 2.1407999999999983, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [67, 76, 85, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [25, 35, 45], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 36, 26, 16, 15, 17, 27, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "path_length": 60, "computation_time_ms": 2.1737000000000006, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [77, 86, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [35, 45, 55], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 36, 26, 25, 15, 16, 17, 27, 37, 47, 57, 67, 77, 76, 86, 96, 97, 87], "path_length": 60, "computation_time_ms": 2.2572000000000148, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [87, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [45, 55, 65], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 36, 35, 25, 15, 16, 26, 27, 17, 37, 47, 57, 67, 77, 87, 86, 96, 97], "path_length": 60, "computation_time_ms": 2.080100000000029, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [55, 65, 75], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 86, 76, 66, 56, 46, 45, 35, 25, 15, 16, 26, 36, 37, 27, 17, 47, 57, 67, 77, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.24650000000004, "total_distance": 61.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [65, 75, 85], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 96, 86, 76, 66, 56, 55, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17, 57, 67, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.021700000000015, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [75, 85, 95], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 96, 86, 76, 66, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.2804000000000157, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 86, 96, 86, 96, 86, 96, 86, 96, 86, 96, 86, 96, 86, 96, 86, 96, 86, 96, 86, 91], "return_length": 22}, {"no_fly_zones": [16, 26, 36], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 17, 27, 37, 47, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57], "path_length": 60, "computation_time_ms": 2.2776999999999936, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [57, 66, 75, 84, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [26, 36, 46], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "path_length": 60, "computation_time_ms": 2.0975999999999773, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [67, 76, 85, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [36, 46, 56], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 27, 17, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "path_length": 60, "computation_time_ms": 2.107799999999993, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [77, 86, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [46, 56, 66], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 37, 27, 17, 47, 57, 67, 77, 76, 86, 96, 97, 87], "path_length": 60, "computation_time_ms": 2.453900000000009, "total_distance": 61.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [87, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [56, 66, 76], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17, 57, 67, 77, 87, 86, 96, 97], "path_length": 60, "computation_time_ms": 2.032900000000004, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [66, 76, 86], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.1036000000000388, "total_distance": 63.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [76, 86, 96], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.0839999999999748, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 91], "return_length": 22}, {"no_fly_zones": [17, 27, 37], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47], "path_length": 60, "computation_time_ms": 2.025800000000022, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [47, 56, 65, 74, 83, 92, 91], "return_length": 7}, {"no_fly_zones": [27, 37, 47], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 17], "path_length": 60, "computation_time_ms": 2.1052999999999766, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [37, 47, 57], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 27, 17], "path_length": 60, "computation_time_ms": 2.0575999999999928, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [47, 57, 67], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 37, 27, 17], "path_length": 60, "computation_time_ms": 11.676300000000028, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [57, 67, 77], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.9817999999999234, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [67, 77, 87], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 4.10569999999999, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [77, 87, 97], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 3.1565000000000065, "total_distance": 61.16227766016838, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [11, 12, 13], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 24, 14, 15, 25, 35, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 36, 26, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "path_length": 60, "computation_time_ms": 2.114700000000025, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [67, 76, 85, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [12, 13, 14], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.6584000000000607, "total_distance": 59.41421356237309, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [13, 14, 15], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 26, 16, 17, 27, 37, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47], "path_length": 60, "computation_time_ms": 2.06800000000007, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [47, 56, 65, 74, 83, 92, 91], "return_length": 7}, {"no_fly_zones": [14, 15, 16], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 3.0544000000000127, "total_distance": 59.41421356237309, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [15, 16, 17], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 25, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27], "path_length": 60, "computation_time_ms": 3.7728000000000206, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [27, 36, 45, 54, 63, 72, 81, 91], "return_length": 8}, {"no_fly_zones": [21, 22, 23], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 34, 24, 14, 13, 12, 11, 15, 25, 35, 45, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "path_length": 60, "computation_time_ms": 2.5574999999999903, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [77, 86, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [22, 23, 24], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 13, 14, 15, 25, 35, 34, 33, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 36, 26, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "path_length": 60, "computation_time_ms": 2.3436000000000012, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [67, 76, 85, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [23, 24, 25], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 36, 26, 16, 15, 14, 13, 17, 27, 37, 47, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57], "path_length": 60, "computation_time_ms": 2.2647000000000084, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [57, 66, 75, 84, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [24, 25, 26], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 15, 16, 17, 27, 37, 36, 35, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47], "path_length": 60, "computation_time_ms": 2.22140000000004, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [47, 56, 65, 74, 83, 92, 91], "return_length": 7}, {"no_fly_zones": [25, 26, 27], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 35, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 17, 16, 15], "path_length": 60, "computation_time_ms": 2.2478999999999694, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [15, 24, 33, 42, 51, 61, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [31, 32, 33], "path_sequence": [91, 81, 71, 61, 51, 41, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 44, 34, 24, 23, 22, 21, 11, 12, 13, 14, 15, 25, 35, 45, 55, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 36, 26, 16, 17, 27, 37, 47, 57, 67, 77, 76, 86, 96, 97, 87], "path_length": 60, "computation_time_ms": 2.231500000000053, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [87, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [32, 33, 34], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 23, 13, 14, 24, 25, 15, 16, 26, 36, 35, 45, 44, 43, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 47, 37, 27, 17, 57, 67, 66, 76, 86, 96, 97, 87, 77], "path_length": 60, "computation_time_ms": 2.05769999999994, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [77, 86, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [33, 34, 35], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 36, 26, 25, 24, 23, 13, 14, 15, 16, 17, 27, 37, 47, 57, 56, 66, 76, 86, 96, 97, 87, 77, 67], "path_length": 60, "computation_time_ms": 2.3069000000000006, "total_distance": 59.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [67, 76, 85, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [34, 35, 36], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 25, 15, 16, 26, 27, 17, 37, 47, 46, 45, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57], "path_length": 60, "computation_time_ms": 2.0510999999999724, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [57, 66, 75, 84, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [35, 36, 37], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 45, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 27, 26, 25, 15, 16, 17], "path_length": 60, "computation_time_ms": 2.0799000000000234, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 25, 34, 43, 52, 61, 71, 81, 91], "return_length": 10}, {"no_fly_zones": [41, 42, 43], "path_sequence": [91, 81, 71, 61, 51, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 44, 34, 33, 32, 31, 21, 11, 12, 22, 23, 13, 14, 24, 25, 15, 16, 26, 36, 35, 45, 55, 65, 64, 74, 84, 94, 95, 85, 75, 76, 66, 56, 46, 47, 37, 27, 17, 57, 67, 77, 87, 86, 96, 97], "path_length": 60, "computation_time_ms": 2.1065999999999585, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [42, 43, 44], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 54, 53, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 57, 47, 37, 27, 17, 67, 77, 76, 86, 96, 97, 87], "path_length": 60, "computation_time_ms": 2.987500000000032, "total_distance": 63.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [87, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [43, 44, 45], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 46, 36, 35, 34, 33, 23, 13, 14, 24, 25, 15, 16, 26, 27, 17, 37, 47, 57, 67, 66, 76, 86, 96, 97, 87, 77], "path_length": 60, "computation_time_ms": 2.1660000000000013, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [77, 86, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [44, 45, 46], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 37, 27, 17, 47, 57, 56, 55, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 76, 86, 96, 97, 87, 77, 67], "path_length": 60, "computation_time_ms": 2.292100000000019, "total_distance": 61.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [67, 76, 85, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [45, 46, 47], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 55, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 37, 36, 35, 25, 15, 16, 26, 27, 17], "path_length": 60, "computation_time_ms": 2.1420000000000883, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [51, 52, 53], "path_sequence": [91, 81, 71, 61, 62, 72, 82, 92, 93, 83, 73, 63, 64, 54, 44, 43, 42, 41, 31, 21, 11, 12, 22, 32, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 65, 75, 74, 84, 94, 95, 85, 86, 76, 66, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.217899999999995, "total_distance": 63.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [52, 53, 54], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 64, 63, 62, 72, 82, 92, 93, 83, 73, 74, 84, 94, 95, 85, 75, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 86, 96, 97], "path_length": 60, "computation_time_ms": 2.0580000000000043, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [53, 54, 55], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 64, 74, 84, 94, 95, 85, 75, 65, 66, 56, 46, 45, 44, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 37, 27, 17, 47, 57, 67, 77, 76, 86, 96, 97, 87], "path_length": 60, "computation_time_ms": 2.171499999999993, "total_distance": 61.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [87, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [54, 55, 56], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17, 57, 67, 66, 65, 64, 74, 84, 94, 95, 85, 75, 76, 86, 96, 97, 87, 77], "path_length": 60, "computation_time_ms": 2.262499999999945, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [77, 86, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [55, 56, 57], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 65, 66, 76, 86, 96, 97, 87, 77, 67, 47, 46, 45, 35, 25, 15, 16, 26, 36, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.0644999999999136, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [61, 62, 63], "path_sequence": [91, 81, 71, 72, 82, 92, 93, 83, 73, 74, 64, 54, 53, 52, 51, 41, 31, 21, 11, 12, 22, 32, 42, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 56, 55, 65, 75, 85, 84, 94, 95, 96, 86, 76, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.0961999999999925, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [62, 63, 64], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 74, 73, 72, 82, 92, 93, 83, 84, 94, 95, 85, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.1548000000000123, "total_distance": 65.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [63, 64, 65], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 74, 84, 94, 95, 85, 75, 76, 66, 56, 55, 54, 53, 43, 33, 23, 13, 14, 24, 34, 44, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17, 57, 67, 77, 87, 86, 96, 97], "path_length": 60, "computation_time_ms": 2.1181000000000116, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [64, 65, 66], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17, 67, 77, 76, 75, 74, 84, 94, 95, 85, 86, 96, 97, 87], "path_length": 60, "computation_time_ms": 2.1596999999999866, "total_distance": 63.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [87, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [65, 66, 67], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 75, 76, 86, 96, 97, 87, 77, 57, 56, 55, 45, 35, 25, 15, 16, 26, 36, 46, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.4904000000000037, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [71, 72, 73], "path_sequence": [91, 81, 82, 92, 93, 83, 84, 74, 64, 63, 62, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 65, 75, 85, 95, 94, 96, 86, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97], "path_length": 60, "computation_time_ms": 2.0739999999999092, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [72, 73, 74], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 84, 83, 82, 92, 93, 94, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "path_length": 60, "computation_time_ms": 2.10699999999997, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [73, 74, 75], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 84, 94, 95, 85, 86, 76, 66, 65, 64, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97, 96], "path_length": 60, "computation_time_ms": 2.2075999999999762, "total_distance": 63.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [96, 95, 94, 93, 92, 91], "return_length": 6}, {"no_fly_zones": [74, 75, 76], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 67, 57, 47, 37, 27, 17, 77, 87, 86, 85, 84, 94, 95, 96, 97], "path_length": 60, "computation_time_ms": 2.164300000000008, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [75, 76, 77], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 85, 86, 96, 97, 87, 67, 66, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.178200000000019, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [81, 82, 83], "path_sequence": [91, 92, 93, 94, 84, 74, 73, 72, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 75, 85, 95, 96, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "path_length": 60, "computation_time_ms": 2.0979999999999333, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [82, 83, 84], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 85, 95, 94, 93, 92, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.0852000000000093, "total_distance": 62.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [83, 84, 85], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 94, 95, 96, 86, 76, 75, 74, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 67, 57, 47, 37, 27, 17, 77, 87, 97], "path_length": 60, "computation_time_ms": 2.0822999999999814, "total_distance": 64.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 96, 95, 94, 93, 92, 91], "return_length": 7}, {"no_fly_zones": [84, 85, 86], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 77, 67, 57, 47, 37, 27, 17, 87, 97, 96, 95, 94], "path_length": 60, "computation_time_ms": 2.0354000000000205, "total_distance": 65.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [94, 93, 92, 91], "return_length": 4}, {"no_fly_zones": [85, 86, 87], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 95, 96, 97, 77, 76, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.1063000000000054, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [92, 93, 94], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 95, 97, 87, 77, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.3910999999999794, "total_distance": 60.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [93, 94, 95], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 96, 97, 87, 77, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.092899999999953, "total_distance": 59.41421356237309, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}, {"no_fly_zones": [94, 95, 96], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 87, 77, 67, 57, 47, 37, 27, 17, 97], "path_length": 60, "computation_time_ms": 2.066200000000018, "total_distance": 66.0, "coverage_rate": 100.0, "is_valid": true, "return_path": [97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 87, 97, 91], "return_length": 22}, {"no_fly_zones": [95, 96, 97], "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 85, 75, 65, 55, 45, 35, 25, 15, 16, 26, 36, 46, 56, 66, 76, 86, 87, 77, 67, 57, 47, 37, 27, 17], "path_length": 60, "computation_time_ms": 2.1514000000000255, "total_distance": 59.41421356237309, "coverage_rate": 100.0, "is_valid": true, "return_path": [17, 26, 35, 44, 53, 62, 71, 81, 91], "return_length": 9}]}