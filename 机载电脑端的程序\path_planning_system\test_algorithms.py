#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径规划算法测试脚本
版权信息：米醋电子工作室
创建日期：2025-07-30

功能描述：
测试三种路径规划算法的基本功能，验证算法实现的正确性。
对比不同算法的性能表现，为算法选择提供参考。
"""

import sys
import os
import time
import numpy as np

# 添加路径以导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 直接导入模块，避免相对导入问题
import importlib.util

# 导入GridMap
spec = importlib.util.spec_from_file_location("grid_map", os.path.join(current_dir, "core", "grid_map.py"))
grid_map_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(grid_map_module)
GridMap = grid_map_module.GridMap

# 导入PathResult
spec = importlib.util.spec_from_file_location("path_result", os.path.join(current_dir, "core", "path_result.py"))
path_result_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(path_result_module)
PathResult = path_result_module.PathResult

# 导入BasePlanner
spec = importlib.util.spec_from_file_location("base_planner", os.path.join(current_dir, "algorithms", "base_planner.py"))
base_planner_module = importlib.util.module_from_spec(spec)
sys.modules['grid_map'] = grid_map_module
sys.modules['path_result'] = path_result_module
spec.loader.exec_module(base_planner_module)
BasePlanner = base_planner_module.BasePlanner

# 导入算法类
spec = importlib.util.spec_from_file_location("astar", os.path.join(current_dir, "algorithms", "astar.py"))
astar_module = importlib.util.module_from_spec(spec)
sys.modules['base_planner'] = base_planner_module
spec.loader.exec_module(astar_module)
AStarPlanner = astar_module.AStarPlanner

spec = importlib.util.spec_from_file_location("dijkstra", os.path.join(current_dir, "algorithms", "dijkstra.py"))
dijkstra_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(dijkstra_module)
DijkstraPlanner = dijkstra_module.DijkstraPlanner

spec = importlib.util.spec_from_file_location("rrt", os.path.join(current_dir, "algorithms", "rrt.py"))
rrt_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(rrt_module)
RRTPlanner = rrt_module.RRTPlanner

def create_test_scenario_1():
    """
    创建测试场景1：简单环境，无禁飞区，全点遍历

    返回:
        tuple: (grid_map, start, description)
    """
    grid_map = GridMap()
    start = (0, 0)  # A1B1 (修正后的坐标系统)
    description = "简单环境测试：从A1B1开始遍历所有63个点，无禁飞区"

    return grid_map, start, description

def create_test_scenario_2():
    """
    创建测试场景2：带禁飞区的复杂环境，全点遍历

    返回:
        tuple: (grid_map, start, description)
    """
    grid_map = GridMap()

    # 设置禁飞区：A5B3, A5B4, A5B5 (连续3个点，垂直排列)
    no_fly_zones = [53, 54, 55]
    grid_map.set_no_fly_zones(no_fly_zones)

    start = (0, 0)  # A1B1 (修正后的坐标系统)
    description = "复杂环境测试：从A1B1开始遍历所有可访问点，避开禁飞区A5B3-A5B5"

    return grid_map, start, description

def create_test_scenario_3():
    """
    创建测试场景3：更复杂的禁飞区配置，全点遍历

    返回:
        tuple: (grid_map, start, description)
    """
    grid_map = GridMap()

    # 设置禁飞区：A6B2, A7B2, A8B2 (水平连续)
    no_fly_zones = [62, 72, 82]
    grid_map.set_no_fly_zones(no_fly_zones)

    start = (6, 8)  # A9B7 (修正后的坐标系统)
    description = "挑战环境测试：从A9B7开始遍历，禁飞区A6B2-A8B2水平阻挡"

    return grid_map, start, description

def test_algorithm(algorithm, grid_map, start, scenario_name):
    """
    测试单个算法的全点遍历功能

    参数:
        algorithm: 算法实例
        grid_map: 网格地图
        start: 起始位置
        scenario_name: 场景名称

    返回:
        dict: 测试结果
    """
    print(f"\n--- 测试 {algorithm.algorithm_name} ---")
    print(f"场景: {scenario_name}")
    print(f"起点: {start}")

    # 计算预期遍历点数
    total_points = 0
    for row in range(GridMap.GRID_ROWS):
        for col in range(GridMap.GRID_COLS):
            if grid_map.is_valid_position(row, col):
                total_points += 1
    print(f"预期遍历点数: {total_points}")

    try:
        # 执行路径规划
        result = algorithm.plan_path(grid_map, start)
        
        # 输出结果
        if result.is_valid():
            coverage_rate = (result.statistics.total_points / total_points * 100) if total_points > 0 else 0
            print(f"✅ 遍历成功!")
            print(f"   路径长度: {result.statistics.total_length:.2f}")
            print(f"   遍历点数: {result.statistics.total_points}/{total_points} ({coverage_rate:.1f}%)")
            print(f"   计算时间: {result.statistics.computation_time_ms:.2f}ms")
            print(f"   内存使用: {result.statistics.memory_usage_mb:.2f}MB")
            print(f"   路径序列: {result.path_sequence[:15]}{'...' if len(result.path_sequence) > 15 else ''}")

            # 不在这里显示可视化，等所有算法测试完再显示最优的

            return {
                'success': True,
                'length': result.statistics.total_length,
                'time': result.statistics.computation_time_ms,
                'points': result.statistics.total_points,
                'coverage_rate': coverage_rate,
                'memory': result.statistics.memory_usage_mb,
                'path': result.path_sequence
            }
        else:
            print(f"❌ 遍历失败!")
            return {
                'success': False,
                'length': 0,
                'time': result.statistics.computation_time_ms,
                'points': 0,
                'coverage_rate': 0,
                'memory': result.statistics.memory_usage_mb,
                'path': []
            }
            
    except Exception as e:
        print(f"❌ 算法执行异常: {e}")
        return {
            'success': False,
            'error': str(e),
            'length': 0,
            'time': 0,
            'points': 0,
            'coverage_rate': 0,
            'memory': 0,
            'path': []
        }

def find_best_algorithm(results):
    """
    找到最优算法

    参数:
        results: 算法结果字典

    返回:
        dict: 最优算法信息
    """
    successful_results = {k: v for k, v in results.items() if v['success']}

    if not successful_results:
        return None

    # 综合评分（路径长度权重0.6，时间权重0.4）
    def calculate_score(result):
        max_length = max(r['length'] for r in successful_results.values())
        max_time = max(r['time'] for r in successful_results.values())

        length_score = result['length'] / max_length if max_length > 0 else 0
        time_score = result['time'] / max_time if max_time > 0 else 0

        return 0.6 * length_score + 0.4 * time_score

    best_name = min(successful_results.keys(), key=lambda x: calculate_score(successful_results[x]))

    return {
        'name': best_name,
        'result': successful_results[best_name]
    }

def compare_algorithms(results):
    """
    对比算法性能

    参数:
        results: 算法结果字典
    """
    print("\n" + "="*60)
    print("算法性能对比")
    print("="*60)
    
    # 表头
    print(f"{'算法':<15} {'成功':<6} {'路径长度':<10} {'计算时间':<12} {'覆盖率':<8} {'遍历点数':<8}")
    print("-" * 70)

    # 结果对比
    for algo_name, result in results.items():
        success = "✅" if result['success'] else "❌"
        length = f"{result['length']:.2f}" if result['success'] else "N/A"
        time_ms = f"{result['time']:.2f}ms" if 'time' in result else "N/A"
        coverage = f"{result['coverage_rate']:.1f}%" if result['success'] else "N/A"
        points = str(result['points']) if result['success'] else "N/A"

        print(f"{algo_name:<15} {success:<6} {length:<10} {time_ms:<12} {coverage:<8} {points:<8}")
    
    # 找出最优结果
    successful_results = {k: v for k, v in results.items() if v['success']}
    
    if successful_results:
        print("\n🏆 性能排名:")
        
        # 按路径长度排序
        by_length = sorted(successful_results.items(), key=lambda x: x[1]['length'])
        print(f"   最短路径: {by_length[0][0]} ({by_length[0][1]['length']:.2f})")
        
        # 按计算时间排序
        by_time = sorted(successful_results.items(), key=lambda x: x[1]['time'])
        print(f"   最快计算: {by_time[0][0]} ({by_time[0][1]['time']:.2f}ms)")
        
        # 综合评分（路径长度权重0.6，时间权重0.4）
        def calculate_score(result):
            # 归一化分数（越小越好）
            max_length = max(r['length'] for r in successful_results.values())
            max_time = max(r['time'] for r in successful_results.values())
            
            length_score = result['length'] / max_length if max_length > 0 else 0
            time_score = result['time'] / max_time if max_time > 0 else 0
            
            return 0.6 * length_score + 0.4 * time_score
        
        by_overall = sorted(successful_results.items(), key=lambda x: calculate_score(x[1]))
        print(f"   综合最优: {by_overall[0][0]}")

def main():
    """主测试函数"""
    print("🚀 路径规划算法测试开始")
    print("替代愚蠢的单片机路径规划，展示PC端算法的强大能力！")
    
    # 创建算法实例
    algorithms = {
        "A*算法": AStarPlanner(),
        "Dijkstra算法": DijkstraPlanner(),
        "RRT算法": RRTPlanner(max_iterations=1000)  # 减少迭代次数以加快测试
    }
    
    # 测试场景
    test_scenarios = [
        create_test_scenario_1(),
        create_test_scenario_2(),
        create_test_scenario_3()
    ]
    
    # 对每个场景测试所有算法
    for i, (grid_map, start, description) in enumerate(test_scenarios, 1):
        print(f"\n{'='*80}")
        print(f"测试场景 {i}: {description}")
        print(f"{'='*80}")

        scenario_results = {}

        for algo_name, algorithm in algorithms.items():
            result = test_algorithm(algorithm, grid_map, start, description)
            scenario_results[algo_name] = result
        
        # 对比本场景的结果
        compare_algorithms(scenario_results)

        # 显示最优算法的路径可视化
        best_algorithm = find_best_algorithm(scenario_results)
        if best_algorithm and best_algorithm['result']['success']:
            print(f"\n🏆 最优算法路径可视化：{best_algorithm['name']}")
            visualization = grid_map.visualize_path(best_algorithm['result']['path'],
                                                  f"最优路径 - {best_algorithm['name']}")
            print(visualization)
    
    print(f"\n{'='*80}")
    print("🎉 所有测试完成！PC端路径规划系统完全碾压单片机的愚蠢算法！")
    print("✨ 多算法支持、性能优化、智能对比 - 这才是专业的路径规划解决方案！")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
