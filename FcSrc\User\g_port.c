#include "g_port.h"

// 全局变量定义
GimbalAngleData_t gimbal_angle_data;    // 云台角度数据

/**
 * @brief 计算帧头校验和
 * @param version 版本
 * @param length 长度
 * @param cmd 指令
 * @return 校验和
 */
uint8_t CalcHeaderChecksum(uint8_t version, uint8_t length, uint8_t cmd)
{
    return (uint8_t)(version + length + cmd);
}

/**
 * @brief 发送云台控制指令
 * @param control_data 控制数据指针
 */
void SendGimbalControlCmd(GimbalControlData_t *control_data)
{
    uint8_t send_buffer[32];
    uint8_t index = 0;
    uint32_t crc32_value;
    
    // 构建协议帧
    send_buffer[index++] = GIMBAL_FRAME_HEADER;                     // 帧头
    send_buffer[index++] = GIMBAL_VERSION;                          // 版本
    send_buffer[index++] = GIMBAL_CONTROL_DATA_LEN;                 // 长度
    send_buffer[index++] = GIMBAL_CMD_CONTROL;                      // 指令
    
    // 计算帧头校验
    send_buffer[index++] = CalcHeaderChecksum(GIMBAL_VERSION, 
                                            GIMBAL_CONTROL_DATA_LEN, 
                                            GIMBAL_CMD_CONTROL);
    
    // 填充数据域
    send_buffer[index++] = control_data->mode;                      // 控制模式
    
    // roll角度控制参数 (小端字节序)
    send_buffer[index++] = (uint8_t)(control_data->roll_angle & 0xFF);
    send_buffer[index++] = (uint8_t)((control_data->roll_angle >> 8) & 0xFF);
    
    // pitch角度控制参数
    send_buffer[index++] = (uint8_t)(control_data->pitch_angle & 0xFF);
    send_buffer[index++] = (uint8_t)((control_data->pitch_angle >> 8) & 0xFF);
    
    // yaw角度控制参数
    send_buffer[index++] = (uint8_t)(control_data->yaw_angle & 0xFF);
    send_buffer[index++] = (uint8_t)((control_data->yaw_angle >> 8) & 0xFF);
    
    // roll速度控制参数
    send_buffer[index++] = (uint8_t)(control_data->roll_speed & 0xFF);
    send_buffer[index++] = (uint8_t)((control_data->roll_speed >> 8) & 0xFF);
    
    // pitch速度控制参数
    send_buffer[index++] = (uint8_t)(control_data->pitch_speed & 0xFF);
    send_buffer[index++] = (uint8_t)((control_data->pitch_speed >> 8) & 0xFF);
    
    // yaw速度控制参数
    send_buffer[index++] = (uint8_t)(control_data->yaw_speed & 0xFF);
    send_buffer[index++] = (uint8_t)((control_data->yaw_speed >> 8) & 0xFF);
    
    // 计算数据域CRC32
    crc32_value = crc_32(&send_buffer[5], GIMBAL_CONTROL_DATA_LEN);
    
    // 添加CRC32校验 (小端字节序)
    send_buffer[index++] = (uint8_t)(crc32_value & 0xFF);
    send_buffer[index++] = (uint8_t)((crc32_value >> 8) & 0xFF);
    send_buffer[index++] = (uint8_t)((crc32_value >> 16) & 0xFF);
    send_buffer[index++] = (uint8_t)((crc32_value >> 24) & 0xFF);
    
    // 发送数据
    DrvUart4SendBuf(send_buffer, index);
}

/**
 * @brief 速度控制
 * @param roll_speed roll轴速度 (单位：度/秒)
 * @param pitch_speed pitch轴速度 (单位：度/秒)
 * @param yaw_speed yaw轴速度 (单位：度/秒)
 */
void GimbalSpeedControl(float yaw_speed, float pitch_speed, float roll_speed)
{
    static GimbalControlData_t control_data = {0};
    
    control_data.mode = GIMBAL_MODE_SPEED;
    control_data.roll_speed = (int16_t)(roll_speed * 100);      // 转换为0.01度/秒
    control_data.pitch_speed = (int16_t)(-pitch_speed * 100);
    control_data.yaw_speed = (int16_t)(-yaw_speed * 100);
    
    SendGimbalControlCmd(&control_data);
}

/**
 * @brief 角度控制
 * @param roll_angle roll轴角度 (单位：度)
 * @param pitch_angle pitch轴角度 (单位：度)
 * @param yaw_angle yaw轴角度 (单位：度)
 */
void GimbalAngleControl(float yaw_angle , float pitch_angle, float roll_angle)
{
    static GimbalControlData_t control_data = {0};
    
    control_data.mode = GIMBAL_MODE_ANGLE;
    control_data.roll_angle = (int16_t)(roll_angle * 100);      // 转换为0.01度
    control_data.pitch_angle = (int16_t)(-pitch_angle * 100);
    control_data.yaw_angle = (int16_t)(-yaw_angle * 100);
    
    SendGimbalControlCmd(&control_data);
}

/**
 * @brief 云台回中
 */
void GimbalCenter(void)
{
    static GimbalControlData_t control_data = {0};
    control_data.mode = GIMBAL_MODE_CENTER;
    SendGimbalControlCmd(&control_data);
}

/**
 * @brief 云台锁定
 */
void GimbalLock(float yaw_angle , float pitch_angle, float roll_angle)
{
    static GimbalControlData_t control_data = {0};
		
    control_data.mode = GIMBAL_MODE_LOCK;
		control_data.roll_angle = (int16_t)(roll_angle * 100);      // 转换为0.01度
		control_data.pitch_angle = (int16_t)(-pitch_angle * 100);
		control_data.yaw_angle = (int16_t)(-yaw_angle * 100);
    
    SendGimbalControlCmd(&control_data);
}

/**
 * @brief 解析接收到的云台角度推送数据
 * @param data 接收到的单字节数据
 */
void GimbalAngle_GetOneByte(uint8_t data)
{
    static uint8_t parse_state = 0;         // 解析状态
    static uint8_t data_length = 0;         // 数据长度
    static uint8_t cmd = 0;                 // 指令
    static uint8_t header_checksum = 0;     // 帧头校验
    static uint8_t data_buffer[32];         // 数据缓冲区
    static uint32_t received_crc32 = 0;     // 接收到的CRC32
    static uint8_t crc32_count = 0;         // CRC32字节计数
    uint32_t calculated_crc32;
    
    switch(parse_state)
    {
        case 0:  // 等待帧头 0xAE
        {
            if(data == GIMBAL_FRAME_HEADER)
            {
                parse_state++;
                data_length = 0;
                crc32_count = 0;
                received_crc32 = 0;
            }
        }
        break;
        
        case 1:  // 等待版本 0x01
        {
            if(data == GIMBAL_VERSION)
            {
                parse_state++;
            }
            else
            {
                parse_state = 0;
            }
        }
        break;
        
        case 2:  // 接收长度
        {
            data_length = data;
            parse_state++;
        }
        break;
        
        case 3:  // 接收指令
        {
            cmd = data;
            parse_state++;
        }
        break;
        
        case 4:  // 接收帧头校验
        {
            header_checksum = data;
            // 验证帧头校验
            if(header_checksum == CalcHeaderChecksum(GIMBAL_VERSION, data_length, cmd))
            {
                if(cmd == GIMBAL_CMD_ANGLE_PUSH && data_length == GIMBAL_ANGLE_DATA_LEN)
                {
                    parse_state++;
                    data_length = 0;  // 重新用作数据接收计数
                }
                else
                {
                    parse_state = 0;  // 不是角度推送指令，重新开始
                }
            }
            else
            {
                parse_state = 0;  // 校验失败，重新开始
            }
        }
        break;
        
        case 5:  // 接收数据域
        {
            data_buffer[data_length++] = data;
            if(data_length >= GIMBAL_ANGLE_DATA_LEN)
            {
                parse_state++;
                crc32_count = 0;
                received_crc32 = 0;
            }
        }
        break;
        
        case 6:  // 接收CRC32校验 (4字节，小端)
        {
            received_crc32 |= ((uint32_t)data << (crc32_count * 8));
            crc32_count++;
            
            if(crc32_count >= 4)
            {
                // 验证CRC32
                calculated_crc32 = crc_32(data_buffer, GIMBAL_ANGLE_DATA_LEN);
                
                if(calculated_crc32 == received_crc32)
                {
                    // CRC32校验通过，解析数据
                    // 解析IMU数据 (小端字节序)
                    gimbal_angle_data.imu_roll = (int16_t)(data_buffer[0] | (data_buffer[1] << 8));
                    gimbal_angle_data.imu_pitch = -(int16_t)(data_buffer[2] | (data_buffer[3] << 8));
                    gimbal_angle_data.imu_yaw = -(int16_t)(data_buffer[4] | (data_buffer[5] << 8));
                    
                    // 解析霍尔角度数据
                    gimbal_angle_data.hall_roll = (int16_t)(data_buffer[6] | (data_buffer[7] << 8));
                    gimbal_angle_data.hall_pitch = -(int16_t)(data_buffer[8] | (data_buffer[9] << 8));
                    gimbal_angle_data.hall_yaw = -(int16_t)(data_buffer[10] | (data_buffer[11] << 8));
                    
                    // 解析霍尔角速度数据
                    gimbal_angle_data.hall_roll_speed = (int16_t)(data_buffer[12] | (data_buffer[13] << 8));
                    gimbal_angle_data.hall_pitch_speed = (int16_t)(data_buffer[14] | (data_buffer[15] << 8));
                    gimbal_angle_data.hall_yaw_speed = (int16_t)(data_buffer[16] | (data_buffer[17] << 8));
                    
                    // 解析IMU角速度数据
                    gimbal_angle_data.imu_x_speed = (int16_t)(data_buffer[18] | (data_buffer[19] << 8));
                    gimbal_angle_data.imu_y_speed = (int16_t)(data_buffer[20] | (data_buffer[21] << 8));
                    gimbal_angle_data.imu_z_speed = (int16_t)(data_buffer[22] | (data_buffer[23] << 8));
                    
                    // 数据更新标志或回调函数可以在这里添加
                    // OnGimbalAngleDataReceived();
                }
                
                parse_state = 0;  // 解析完成，重新开始
            }
        }
        break;
        
        default:
        {
            parse_state = 0;
        }
        break;
    }
}

/**
 * @brief 获取IMU角度数据 (单位：度)
 * @param roll IMU横滚角
 * @param pitch IMU俯仰角  
 * @param yaw IMU偏航角
 */
void GetIMUAngle(float *roll, float *pitch, float *yaw)
{
    *roll = (float)gimbal_angle_data.imu_roll / 100.0f;
    *pitch = (float)gimbal_angle_data.imu_pitch / 100.0f;
    *yaw = (float)gimbal_angle_data.imu_yaw / 100.0f;
}

/**
 * @brief 获取霍尔角度数据 (单位：度)
 * @param roll 霍尔横滚角
 * @param pitch 霍尔俯仰角
 * @param yaw 霍尔偏航角
 */
void GetHallAngle(float *roll, float *pitch, float *yaw)
{
    *roll = (float)gimbal_angle_data.hall_roll / 100.0f;
    *pitch = (float)gimbal_angle_data.hall_pitch / 100.0f;
    *yaw = (float)gimbal_angle_data.hall_yaw / 100.0f;
}

/**
 * @brief 获取角速度数据 (单位：度/秒)
 * @param hall_roll 霍尔横滚角速度
 * @param hall_pitch 霍尔俯仰角速度
 * @param hall_yaw 霍尔偏航角速度
 * @param imu_x IMU_X角速度
 * @param imu_y IMU_Y角速度
 * @param imu_z IMU_Z角速度
 */
void GetAngleSpeed(float *hall_roll, float *hall_pitch, float *hall_yaw,
                   float *imu_x, float *imu_y, float *imu_z)
{
    *hall_roll = (float)gimbal_angle_data.hall_roll_speed / 100.0f;
    *hall_pitch = (float)gimbal_angle_data.hall_pitch_speed / 100.0f;
    *hall_yaw = (float)gimbal_angle_data.hall_yaw_speed / 100.0f;
    *imu_x = (float)gimbal_angle_data.imu_x_speed / 100.0f;
    *imu_y = (float)gimbal_angle_data.imu_y_speed / 100.0f;
    *imu_z = (float)gimbal_angle_data.imu_z_speed / 100.0f;
}
