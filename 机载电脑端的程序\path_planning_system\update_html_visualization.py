#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新HTML可视化文件，使用安全修正后的数据
"""

import json
import os

def load_safe_data():
    """加载安全修正后的数据"""
    with open('safe_optimized_return_paths.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data['path_data']

def truncate_to_60_points(path_sequence):
    """将路径截断为60个点"""
    if len(path_sequence) > 60:
        return path_sequence[:60]
    return path_sequence

def position_code_to_grid(position_code):
    """将position_code转换为网格坐标"""
    row = (position_code % 10) - 1  # B1=0, B2=1, ..., B7=6
    col = (position_code // 10) - 1  # A1=0, A2=1, ..., A9=8
    return row, col

def generate_html_for_path(path_data, path_index):
    """为单个路径生成HTML可视化"""
    
    # 截断巡查路径为60个点
    patrol_path = truncate_to_60_points(path_data['patrol_path_sequence'])
    return_path = path_data['return_path_sequence']
    no_fly_zones = path_data['no_fly_zones']
    
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径{path_index+1} - 禁飞区{no_fly_zones} (安全修正版)</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }}
        .grid-container {{
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }}
        .grid {{
            display: grid;
            grid-template-columns: repeat(9, 50px);
            grid-template-rows: repeat(7, 50px);
            gap: 2px;
            border: 3px solid #333;
            background-color: #333;
            padding: 10px;
            border-radius: 10px;
        }}
        .cell {{
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            color: white;
            border-radius: 4px;
            position: relative;
        }}
        .normal {{ background-color: #4CAF50; }}
        .no-fly {{ background-color: #f44336; }}
        .start {{ background-color: #2196F3; }}
        .patrol {{ background-color: #FF9800; }}
        .return {{ background-color: #9C27B0; }}
        .path-number {{
            position: absolute;
            top: 2px;
            left: 2px;
            font-size: 8px;
            background: rgba(0,0,0,0.7);
            padding: 1px 3px;
            border-radius: 2px;
        }}
        .info-panel {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }}
        .info-box {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }}
        .legend {{
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }}
        .path-sequence {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }}
        .navigation {{
            text-align: center;
            margin: 30px 0;
        }}
        .nav-button {{
            background: #007bff;
            color: white;
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }}
        .nav-button:hover {{
            background: #0056b3;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>路径{path_index+1} 可视化 (安全修正版)</h1>
            <p>禁飞区: {no_fly_zones} | 巡查点数: {len(patrol_path)} | 返航点数: {len(return_path)}</p>
            <p>总飞行时间: {path_data.get('total_flight_time', 0):.1f}秒 | 覆盖率: {path_data.get('coverage_rate', 100)}%</p>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color start"></div>
                <span>起点/终点 (A9B1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color no-fly"></div>
                <span>禁飞区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color patrol"></div>
                <span>巡查路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color return"></div>
                <span>返航路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color normal"></div>
                <span>正常区域</span>
            </div>
        </div>

        <div class="grid-container">
            <div class="grid" id="pathGrid">
                <!-- 网格将由JavaScript生成 -->
            </div>
        </div>

        <div class="info-panel">
            <div class="info-box">
                <h3>🎯 巡查路径序列 ({len(patrol_path)}个点)</h3>
                <div class="path-sequence">
                    {' → '.join(map(str, patrol_path))}
                </div>
            </div>
            <div class="info-box">
                <h3>🏠 返航路径序列 ({len(return_path)}个点)</h3>
                <div class="path-sequence">
                    {' → '.join(map(str, return_path))}
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="visualization_index.html" class="nav-button">返回主页</a>
            <a href="path_{path_index:03d}.html" class="nav-button">上一个</a>
            <a href="path_{(path_index+2):03d}.html" class="nav-button">下一个</a>
        </div>
    </div>

    <script>
        // 网格数据
        const noFlyZones = {no_fly_zones};
        const patrolPath = {patrol_path};
        const returnPath = {return_path};
        
        // 生成网格
        function generateGrid() {{
            const grid = document.getElementById('pathGrid');
            
            // 创建7行9列的网格 (B7到B1, A1到A9)
            for (let row = 6; row >= 0; row--) {{
                for (let col = 0; col < 9; col++) {{
                    const positionCode = (col + 1) * 10 + (row + 1);
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.textContent = positionCode;
                    
                    // 确定单元格类型
                    if (positionCode === 91) {{
                        cell.classList.add('start');
                    }} else if (noFlyZones.includes(positionCode)) {{
                        cell.classList.add('no-fly');
                    }} else if (patrolPath.includes(positionCode)) {{
                        cell.classList.add('patrol');
                        // 添加路径序号
                        const pathIndex = patrolPath.indexOf(positionCode) + 1;
                        const pathNumber = document.createElement('div');
                        pathNumber.className = 'path-number';
                        pathNumber.textContent = pathIndex;
                        cell.appendChild(pathNumber);
                    }} else if (returnPath.includes(positionCode)) {{
                        cell.classList.add('return');
                        // 添加返航序号
                        const returnIndex = returnPath.indexOf(positionCode) + 1;
                        const pathNumber = document.createElement('div');
                        pathNumber.className = 'path-number';
                        pathNumber.textContent = 'R' + returnIndex;
                        cell.appendChild(pathNumber);
                    }} else {{
                        cell.classList.add('normal');
                    }}
                    
                    grid.appendChild(cell);
                }}
            }}
        }}
        
        // 页面加载时生成网格
        document.addEventListener('DOMContentLoaded', generateGrid);
    </script>
</body>
</html>'''
    
    return html_content

def update_all_html_files():
    """更新所有HTML可视化文件"""
    
    path_data = load_safe_data()
    
    print("正在更新HTML可视化文件...")
    
    # 更新每个路径的HTML文件
    for i, path in enumerate(path_data):
        html_content = generate_html_for_path(path, i)
        filename = f"path_{i+1:03d}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        if (i + 1) % 10 == 0:
            print(f"已更新 {i+1}/92 个HTML文件")
    
    print("所有HTML可视化文件已更新完成！")
    print("特别更新了路径50、52、90、92的安全修正数据")

if __name__ == "__main__":
    update_all_html_files()
