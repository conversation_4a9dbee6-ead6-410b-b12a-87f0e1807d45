*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\keil5\ARM\ARMCC\Bin'
Build target 'Ano_LX'
compiling User_Task.c...
..\FcSrc\User_Task.c(166): warning:  #177-D: variable "dadian_f"  was declared but never referenced
  static u8 dadian_f = 0;
..\FcSrc\User_Task.c(172): warning:  #550-D: variable "patrol_points_completed"  was set but never used
  static int patrol_points_completed = 0;        // 已完成巡查点数量
..\FcSrc\User_Task.c(193): warning:  #550-D: variable "descent_distance"  was set but never used
  static float descent_distance = 0;             // 降落距离
..\FcSrc\User_Task.c(194): warning:  #550-D: variable "descent_angle"  was set but never used
  static float descent_angle = 45.0f;            // 降落角度
..\FcSrc\User_Task.c(195): warning:  #550-D: variable "descent_speed"  was set but never used
  static float descent_speed = 20.0f;            // 降落速度 cm/s
..\FcSrc\User_Task.c(225): warning:  #550-D: variable "current_path_index"  was set but never used
  static int current_path_index = 0;        // 当前路径执行索引
..\FcSrc\User_Task.c(230): warning:  #550-D: variable "current_patrol_step"  was set but never used
  static int current_patrol_step = 0;            // 当前巡查步骤索引
..\FcSrc\User_Task.c(276): warning:  #550-D: variable "current_send_index"  was set but never used
  static u8 current_send_index = 0;                      // 当前发送的动物索引
..\FcSrc\User_Task.c(406): warning:  #177-D: function "is_dadian_down_command"  was declared but never referenced
  static inline bool is_dadian_down_command(uint16_t ch_value) {
..\FcSrc\User_Task.c(430): warning:  #177-D: function "is_CAM_reached"  was declared but never referenced
  static inline bool is_CAM_reached(void) {
..\FcSrc\User_Task.c(1946): warning:  #177-D: function "process_multi_animal_fusion"  was declared but never referenced
  static u8 process_multi_animal_fusion(animal_sample_t* samples, u8 sample_count, fusion_result_t* result)
..\FcSrc\User_Task.c: 11 warnings, 0 errors
linking...
Program Size: Code=95452 RO-data=13436 RW-data=3296 ZI-data=21008  
FromELF: creating hex file...
After Build - User command #1: fromelf.exe --bin -o ./ANO-LX.bin ./build/Ano_LX.axf
".\build\ANO_LX.axf" - 0 Error(s), 11 Warning(s).
Build Time Elapsed:  00:00:03
