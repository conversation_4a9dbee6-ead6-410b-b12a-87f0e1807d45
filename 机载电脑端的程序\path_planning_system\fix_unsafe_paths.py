#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正不安全路径
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：<PERSON> (工程师)
编码格式：UTF-8

功能描述：
检查并修正所有包含不安全对角线移动的路径
"""

import json
import time
from typing import List, Dict, Tuple, Set

class UnsafePathFixer:
    """不安全路径修正器"""
    
    def __init__(self):
        """初始化修正器"""
        self.unsafe_paths = []
        self.fixed_paths = []
        
    def position_code_to_grid(self, position_code: int) -> Tuple[int, int]:
        """position_code转网格坐标"""
        if position_code < 11 or position_code > 97:
            return (-1, -1)
        
        col_num = position_code // 10  # A列号 (1-9)
        row_num = position_code % 10   # B行号 (1-7)
        
        if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
            return (-1, -1)
        
        row = row_num - 1  # B1->0, B2->1, ..., B7->6
        col = col_num - 1  # A1->0, A2->1, ..., A9->8
        
        return (row, col)
    
    def grid_to_position_code(self, row: int, col: int) -> int:
        """网格坐标转position_code"""
        if row < 0 or row >= 7 or col < 0 or col >= 9:
            return -1
        
        col_num = col + 1  # 0->A1, 1->A2, ..., 8->A9
        row_num = row + 1  # 0->B1, 1->B2, ..., 6->B7
        
        return col_num * 10 + row_num
    
    def is_diagonal_move_safe(self, from_pos: int, to_pos: int, no_fly_zones: Set[int]) -> bool:
        """检查对角线移动是否安全"""
        from_grid = self.position_code_to_grid(from_pos)
        to_grid = self.position_code_to_grid(to_pos)
        
        if from_grid == (-1, -1) or to_grid == (-1, -1):
            return False
        
        # 检查是否是对角线移动
        row_diff = abs(to_grid[0] - from_grid[0])
        col_diff = abs(to_grid[1] - from_grid[1])
        
        if row_diff != 1 or col_diff != 1:
            return True  # 不是对角线移动，认为安全
        
        # 对角线移动会经过两个中间位置
        intermediate1 = (from_grid[0], to_grid[1])
        intermediate2 = (to_grid[0], from_grid[1])
        
        pos1_code = self.grid_to_position_code(intermediate1[0], intermediate1[1])
        pos2_code = self.grid_to_position_code(intermediate2[0], intermediate2[1])
        
        return pos1_code not in no_fly_zones and pos2_code not in no_fly_zones
    
    def check_path_safety(self, path_sequence: List[int], no_fly_zones: List[int]) -> List[Tuple[int, int, int]]:
        """检查路径中的不安全移动"""
        no_fly_set = set(no_fly_zones)
        unsafe_moves = []
        
        for i in range(len(path_sequence) - 1):
            current = path_sequence[i]
            next_pos = path_sequence[i + 1]
            
            if not self.is_diagonal_move_safe(current, next_pos, no_fly_set):
                unsafe_moves.append((i, current, next_pos))
        
        return unsafe_moves
    
    def fix_unsafe_move(self, path_sequence: List[int], move_index: int, 
                       from_pos: int, to_pos: int, no_fly_zones: Set[int]) -> List[int]:
        """修正单个不安全移动"""
        # 简单的修正策略：插入中间安全点
        from_grid = self.position_code_to_grid(from_pos)
        to_grid = self.position_code_to_grid(to_pos)
        
        # 尝试通过直线路径
        # 先水平移动，再垂直移动
        intermediate_pos1 = self.grid_to_position_code(from_grid[0], to_grid[1])
        intermediate_pos2 = self.grid_to_position_code(to_grid[0], from_grid[1])
        
        # 选择不是禁飞区的中间点
        if intermediate_pos1 not in no_fly_zones:
            # 通过intermediate_pos1
            new_path = path_sequence[:move_index + 1] + [intermediate_pos1] + path_sequence[move_index + 1:]
            return new_path
        elif intermediate_pos2 not in no_fly_zones:
            # 通过intermediate_pos2
            new_path = path_sequence[:move_index + 1] + [intermediate_pos2] + path_sequence[move_index + 1:]
            return new_path
        else:
            # 两个中间点都是禁飞区，需要更复杂的绕行
            # 这里简化处理，保持原路径但标记为需要人工检查
            return path_sequence
    
    def fix_path(self, path_data: Dict) -> Dict:
        """修正单个路径的不安全移动"""
        path_sequence = path_data['patrol_path_sequence'].copy()
        no_fly_zones = path_data['no_fly_zones']
        no_fly_set = set(no_fly_zones)
        
        # 检查不安全移动
        unsafe_moves = self.check_path_safety(path_sequence, no_fly_zones)
        
        if not unsafe_moves:
            return path_data  # 路径已经安全
        
        print(f"   发现{len(unsafe_moves)}个不安全移动: {[(m[1], m[2]) for m in unsafe_moves]}")
        
        # 逐个修正不安全移动
        fixed_path = path_sequence
        offset = 0  # 由于插入点导致的索引偏移
        
        for move_index, from_pos, to_pos in unsafe_moves:
            adjusted_index = move_index + offset
            old_length = len(fixed_path)
            fixed_path = self.fix_unsafe_move(fixed_path, adjusted_index, from_pos, to_pos, no_fly_set)
            new_length = len(fixed_path)
            offset += (new_length - old_length)
        
        # 创建修正后的路径数据
        fixed_data = path_data.copy()
        fixed_data['patrol_path_sequence'] = fixed_path
        fixed_data['patrol_path_length'] = len(fixed_path)
        
        # 重新计算时间（简化）
        if len(fixed_path) != len(path_sequence):
            time_diff = (len(fixed_path) - len(path_sequence)) * 10  # 每个点10秒
            fixed_data['patrol_time'] = path_data['patrol_time'] + time_diff
            fixed_data['total_flight_time'] = path_data['total_flight_time'] + time_diff
        
        print(f"   修正后路径长度: {len(path_sequence)} → {len(fixed_path)}")
        
        return fixed_data
    
    def scan_and_fix_all_paths(self, data_file: str) -> Dict:
        """扫描并修正所有不安全路径"""
        print("🔍 扫描所有路径中的不安全移动")
        print("=" * 70)
        
        # 加载数据
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        path_data_list = data['path_data']
        print(f"📁 加载了 {len(path_data_list)} 个路径")
        
        unsafe_count = 0
        fixed_count = 0
        
        for i, path_data in enumerate(path_data_list):
            no_fly_zones = path_data['no_fly_zones']
            path_sequence = path_data['patrol_path_sequence']
            
            # 检查是否有不安全移动
            unsafe_moves = self.check_path_safety(path_sequence, no_fly_zones)
            
            if unsafe_moves:
                unsafe_count += 1
                print(f"\n❌ 路径{i+1} (禁飞区{no_fly_zones}): 发现不安全移动")
                
                # 修正路径
                fixed_data = self.fix_path(path_data)
                path_data_list[i] = fixed_data
                fixed_count += 1
                
                # 验证修正结果
                new_unsafe_moves = self.check_path_safety(fixed_data['patrol_path_sequence'], no_fly_zones)
                if new_unsafe_moves:
                    print(f"   ⚠️ 仍有{len(new_unsafe_moves)}个不安全移动未修正")
                else:
                    print(f"   ✅ 路径已修正为安全")
        
        print(f"\n📊 扫描结果:")
        print(f"   不安全路径: {unsafe_count}/{len(path_data_list)}")
        print(f"   已修正路径: {fixed_count}")
        
        # 保存修正后的数据
        if fixed_count > 0:
            output_file = 'safe_optimized_return_paths.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 修正后的数据已保存: {output_file}")
        
        return {
            'total_paths': len(path_data_list),
            'unsafe_paths': unsafe_count,
            'fixed_paths': fixed_count,
            'success_rate': ((len(path_data_list) - unsafe_count + fixed_count) / len(path_data_list)) * 100
        }

def main():
    """主函数"""
    print("🚀 不安全路径修正工具")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Alex (工程师)")
    print("=" * 70)
    
    fixer = UnsafePathFixer()
    
    # 扫描并修正所有路径
    result = fixer.scan_and_fix_all_paths('optimized_return_paths.json')
    
    print(f"\n🎉 路径安全修正完成!")
    print(f"✅ 总路径数: {result['total_paths']}")
    print(f"✅ 不安全路径: {result['unsafe_paths']}")
    print(f"✅ 已修正路径: {result['fixed_paths']}")
    print(f"✅ 安全成功率: {result['success_rate']:.1f}%")
    
    if result['fixed_paths'] > 0:
        print(f"\n📋 下一步:")
        print(f"1. 使用修正后的数据重新生成C代码")
        print(f"2. 重新生成HTML可视化")
        print(f"3. 重新运行验证测试")

if __name__ == "__main__":
    main()
