# 野生动物巡查状态机架构设计

## 📋 设计概述

本文档基于现有状态机分析结果，设计全新的野生动物巡查状态机架构，实现与Path_Planner.c的完美集成，支持最优路径巡查、激光笔垂直指示、动物识别和数据传输功能。

## 🎯 设计目标

1. **最优路径巡查**：集成Path_Planner.c实现贪心算法+2-opt优化的最优巡查路径
2. **智能禁飞区处理**：自动跳过禁飞区，确保飞行安全
3. **激光笔垂直指示**：在每个巡查点激活激光笔垂直向下指示
4. **动物识别集成**：无缝集成Maixcam.c的5种动物识别功能
5. **向后兼容性**：最大化复用现有导航和控制函数

## 🏗️ 新状态机架构

### 1. 状态定义

```c
typedef enum {
    PATROL_STATE_INIT = 0,              // 初始化阶段
    PATROL_STATE_HOME_SETUP = 1,        // 起始位置设置
    PATROL_STATE_PATH_PLANNING = 2,     // 路径规划阶段
    PATROL_STATE_GET_NEXT_POINT = 3,    // 获取下一个巡查点
    PATROL_STATE_NAVIGATE = 4,          // 导航到巡查点
    PATROL_STATE_HOVER_DETECT = 5,      // 悬停识别（激光笔+动物识别）
    PATROL_STATE_CHECK_COMPLETE = 6,    // 检查巡查完成状态
    PATROL_STATE_RETURN_HOME = 7,       // 返航
    PATROL_STATE_DESCENT_45DEG = 10,    // 45°角降落开始
    PATROL_STATE_DESCENT_CONTROL = 11,  // 45°角降落控制
    PATROL_STATE_FINAL_LANDING = 12,    // 最终降落
    PATROL_STATE_MISSION_END = 40       // 任务结束
} patrol_state_t;
```

### 2. 状态转换流程图

```
[0] 初始化 → [1] 起始位置设置 → [2] 路径规划
                                      ↓
[7] 返航 ← [6] 检查完成状态 ← [5] 悬停识别
   ↓              ↑                ↑
[10] 45°降落 → [3] 获取下一点 → [4] 导航
   ↓              ↓
[11] 降落控制 → [12] 最终降落 → [40] 任务结束
```

### 3. 详细状态设计

#### 状态0：初始化阶段 (PATROL_STATE_INIT)
```c
case PATROL_STATE_INIT:
    // 重置所有状态和定时器
    handle_mission_init(&mission_timer_ms);
    
    // 初始化巡查相关变量
    current_patrol_index = 0;
    patrol_points_completed = 0;
    mission_start_time_ms = GetSysRunTimeMs();
    
    mission_step = PATROL_STATE_HOME_SETUP;
    break;
```

**功能**：
- 重置所有控制标志和定时器
- 初始化巡查状态变量
- 记录任务开始时间（用于300秒超时监控）

#### 状态1：起始位置设置 (PATROL_STATE_HOME_SETUP)
```c
case PATROL_STATE_HOME_SETUP:
    // 复用现有函数
    handle_home_position_setup();
    // 函数内部会自动推进到下一状态
    break;
```

**功能**：
- 获取当前位置作为起始点
- 计算actual_work_pos实际工作坐标
- 切换到真程控模式

#### 状态2：路径规划阶段 (PATROL_STATE_PATH_PLANNING)
```c
case PATROL_STATE_PATH_PLANNING:
    // 集成Path_Planner进行路径规划
    if (execute_path_planning()) {
        mission_step = PATROL_STATE_GET_NEXT_POINT;
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Path planning complete");
    }
    break;
```

**功能**：
- 调用Path_Planner.c的plan_optimal_path()函数
- 设置work_pos[i][5]的order字段
- 生成最优巡查路径

#### 状态3：获取下一个巡查点 (PATROL_STATE_GET_NEXT_POINT)
```c
case PATROL_STATE_GET_NEXT_POINT:
    int next_point = get_next_patrol_point();
    if (next_point >= 0) {
        current_patrol_index = next_point;
        mission_step = PATROL_STATE_NAVIGATE;
    } else {
        // 所有巡查点已完成
        mission_step = PATROL_STATE_CHECK_COMPLETE;
    }
    break;
```

**功能**：
- 按order字段顺序获取下一个巡查点
- 自动跳过禁飞区（status=1）
- 检查是否还有未访问的巡查点

#### 状态4：导航到巡查点 (PATROL_STATE_NAVIGATE)
```c
case PATROL_STATE_NAVIGATE:
    // 复用现有导航函数
    handle_work_point_navigation(current_patrol_index);
    
    // 检查是否到达目标位置
    if (is_position_reached() && is_yaw_reached() && is_z_position_reached()) {
        mission_step = PATROL_STATE_HOVER_DETECT;
        AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)current_patrol_index, "Arrived at patrol point:");
    }
    break;
```

**功能**：
- 复用handle_work_point_navigation()函数
- 使用现有的位置检测机制
- 到达后转入悬停识别状态

#### 状态5：悬停识别 (PATROL_STATE_HOVER_DETECT)
```c
case PATROL_STATE_HOVER_DETECT:
    // 激活激光笔垂直指示
    laser_flag_Control(1);
    LED_f = 1;
    Laser_Set_Target_Pixel(320, 240); // 屏幕中心，垂直向下
    
    // 悬停2秒进行动物识别
    if (handle_wait(&mission_timer_ms, 2000)) {
        // 关闭激光笔
        laser_flag_Control(0);
        LED_f = 0;
        
        // 标记当前点已完成
        mark_patrol_point_completed(current_patrol_index);
        patrol_points_completed++;
        
        mission_step = PATROL_STATE_GET_NEXT_POINT;
    }
    break;
```

**功能**：
- 激活激光笔垂直向下指示
- 悬停2秒进行动物识别
- 利用Maixcam.c的自动识别和数据传输
- 标记巡查点完成状态

#### 状态6：检查巡查完成状态 (PATROL_STATE_CHECK_COMPLETE)
```c
case PATROL_STATE_CHECK_COMPLETE:
    if (is_patrol_complete() || check_mission_timeout()) {
        mission_step = PATROL_STATE_RETURN_HOME;
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Patrol mission complete, returning home");
    } else {
        // 还有未完成的巡查点，继续巡查
        mission_step = PATROL_STATE_GET_NEXT_POINT;
    }
    break;
```

**功能**：
- 检查所有有效巡查点是否已完成
- 检查300秒任务超时
- 决定继续巡查或返航

#### 状态7：返航 (PATROL_STATE_RETURN_HOME)
```c
case PATROL_STATE_RETURN_HOME:
    // 复用现有返航函数
    handle_return_home();
    
    // 检查是否到达起始点
    if (is_position_reached() && is_yaw_reached() && is_z_position_reached()) {
        if (handle_wait(&mission_timer_ms, 1000)) {
            mission_step = PATROL_STATE_DESCENT_45DEG;
        }
    }
    break;
```

**功能**：
- 返回起始位置
- 悬停1秒确保稳定
- 准备开始45°角降落

#### 状态10-12：45°角降落流程
```c
case PATROL_STATE_DESCENT_45DEG:
    // 开始45°角降落
    if (start_45deg_descent()) {
        mission_step = PATROL_STATE_DESCENT_CONTROL;
    }
    break;

case PATROL_STATE_DESCENT_CONTROL:
    // 控制45°角降落过程
    if (control_45deg_descent()) {
        mission_step = PATROL_STATE_FINAL_LANDING;
    }
    break;

case PATROL_STATE_FINAL_LANDING:
    // 最终降落
    if (land(&mission_timer_ms, 2000)) {
        mission_step = PATROL_STATE_MISSION_END;
        zigbee_up_f = 0;
    }
    break;
```

**功能**：
- 实现45°角几何轨迹降落
- 平滑控制降落过程
- 确保激光笔在降落过程中保持垂直

## 🔧 新增辅助函数设计

### 1. 路径规划集成函数
```c
/**
 * @brief 执行路径规划
 * @return true: 规划成功, false: 规划失败
 */
bool execute_path_planning(void)
{
    // 初始化路径规划模块
    int init_result = path_planner_init();
    if (init_result != PATH_PLANNER_SUCCESS) {
        return false;
    }
    
    // 获取禁飞区信息（从zigbee模块）
    int no_fly_zones[MAX_NO_FLY_ZONES];
    int no_fly_count = get_current_no_fly_zones(no_fly_zones);
    
    // 配置路径规划参数
    path_planner_config_t config;
    path_planner_get_default_config(&config);
    config.enable_2opt_optimization = true;
    config.max_iterations = 10;
    
    // 执行路径规划
    path_planner_stats_t stats;
    int result = plan_optimal_path(work_pos, WORK_POINT_ARRAY_SIZE, 
                                  no_fly_zones, no_fly_count, &config, &stats);
    
    return (result == PATH_PLANNER_SUCCESS);
}
```

### 2. 巡查点管理函数
```c
/**
 * @brief 获取下一个巡查点
 * @return 巡查点索引，无可用点时返回-1
 */
int get_next_patrol_point(void)
{
    static int current_order = 1; // 从order=1开始
    
    // 查找当前order对应的工作点索引
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (work_pos[i][5] == current_order && work_pos[i][6] == 0) { // order匹配且非禁飞区
            current_order++;
            return i;
        }
    }
    
    return -1; // 没有更多巡查点
}

/**
 * @brief 检查巡查是否完成
 * @return true: 完成, false: 未完成
 */
bool is_patrol_complete(void)
{
    // 检查所有有效工作点是否已访问
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (work_pos[i][6] == 0 && work_pos[i][5] > 0) { // 非禁飞区且有order
            if (!is_patrol_point_completed(i)) {
                return false;
            }
        }
    }
    return true;
}

/**
 * @brief 检查任务超时
 * @return true: 超时, false: 未超时
 */
bool check_mission_timeout(void)
{
    uint32_t current_time = GetSysRunTimeMs();
    return ((current_time - mission_start_time_ms) > 300000); // 300秒
}
```

### 3. 45°角降落函数
```c
/**
 * @brief 开始45°角降落
 * @return true: 开始成功, false: 开始失败
 */
bool start_45deg_descent(void)
{
    // 计算45°角降落轨迹
    descent_start_height = mid360.pose_z_cm;
    descent_target_height = LANDING_HEIGHT;
    descent_distance = descent_start_height - descent_target_height;
    
    // 设置降落参数
    descent_angle = 45.0f; // 45度角
    descent_speed = 20.0f; // 降落速度 cm/s
    
    return true;
}

/**
 * @brief 控制45°角降落过程
 * @return true: 降落完成, false: 降落进行中
 */
bool control_45deg_descent(void)
{
    // 计算当前应该的高度
    float progress = calculate_descent_progress();
    float target_height = descent_start_height - (descent_distance * progress);
    
    // 设置目标高度
    target_pos[2] = (s16)target_height;
    Z_flag_Control(1);
    
    // 确保激光笔保持垂直
    laser_flag_Control(1);
    Laser_Set_Target_Pixel(320, 240);
    
    // 检查是否到达目标高度
    return (mid360.pose_z_cm <= (LANDING_HEIGHT + 5));
}
```

## 📊 状态转换条件总结

| 当前状态 | 转换条件 | 目标状态 | 备注 |
|---------|---------|---------|------|
| 0 | 初始化完成 | 1 | 自动转换 |
| 1 | 起始位置设置完成 | 2 | handle_home_position_setup()内部控制 |
| 2 | 路径规划完成 | 3 | execute_path_planning()返回true |
| 3 | 找到下一个巡查点 | 4 | get_next_patrol_point()返回有效索引 |
| 3 | 无更多巡查点 | 6 | get_next_patrol_point()返回-1 |
| 4 | 到达巡查点 | 5 | 位置+偏航+高度检测通过 |
| 5 | 悬停识别完成 | 3 | handle_wait()延时2秒完成 |
| 6 | 巡查完成或超时 | 7 | is_patrol_complete()或check_mission_timeout() |
| 6 | 巡查未完成 | 3 | 继续巡查 |
| 7 | 返航完成 | 10 | 到达起始点并稳定1秒 |
| 10 | 45°降落开始 | 11 | start_45deg_descent()成功 |
| 11 | 降落进行中 | 12 | control_45deg_descent()完成 |
| 12 | 最终降落完成 | 40 | land()函数完成 |

## 🎯 设计优势

1. **最大化复用**：保留现有的导航、控制、检测函数
2. **智能路径**：集成Path_Planner实现最优巡查路径
3. **安全可靠**：自动跳过禁飞区，支持超时保护
4. **功能完整**：支持激光指示、动物识别、数据传输
5. **易于维护**：清晰的状态定义和转换逻辑

## 🔧 全局变量定义

### 1. 巡查状态管理变量
```c
// 巡查任务相关全局变量
static int current_patrol_index = 0;           // 当前巡查点索引
static int patrol_points_completed = 0;        // 已完成巡查点数量
static uint32_t mission_start_time_ms = 0;     // 任务开始时间
static bool patrol_point_status[WORK_POINT_ARRAY_SIZE] = {false}; // 巡查点完成状态

// 45°角降落相关变量
static float descent_start_height = 0;         // 降落开始高度
static float descent_target_height = 0;        // 降落目标高度
static float descent_distance = 0;             // 降落距离
static float descent_angle = 45.0f;            // 降落角度
static float descent_speed = 20.0f;            // 降落速度
```

### 2. 辅助函数声明
```c
// 路径规划相关函数
bool execute_path_planning(void);
int get_current_no_fly_zones(int *no_fly_zones);

// 巡查点管理函数
int get_next_patrol_point(void);
bool is_patrol_complete(void);
bool check_mission_timeout(void);
void mark_patrol_point_completed(int index);
bool is_patrol_point_completed(int index);

// 45°角降落函数
bool start_45deg_descent(void);
bool control_45deg_descent(void);
float calculate_descent_progress(void);
```

## ⚠️ 错误处理和异常情况

### 1. 路径规划失败处理
```c
case PATROL_STATE_PATH_PLANNING:
    if (execute_path_planning()) {
        mission_step = PATROL_STATE_GET_NEXT_POINT;
    } else {
        // 路径规划失败，使用默认线性路径
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Path planning failed, using default path");
        setup_default_patrol_path();
        mission_step = PATROL_STATE_GET_NEXT_POINT;
    }
    break;
```

### 2. 超时保护机制
```c
// 在每个状态中添加超时检查
if (check_mission_timeout()) {
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Mission timeout, returning home");
    mission_step = PATROL_STATE_RETURN_HOME;
    return;
}
```

### 3. 激光笔故障处理
```c
case PATROL_STATE_HOVER_DETECT:
    // 尝试激活激光笔
    laser_flag_Control(1);
    LED_f = 1;

    // 检查激光笔是否正常工作
    if (!Laser_Is_On_Target(10)) {
        // 激光笔可能故障，但继续执行识别
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, "Laser pointer may be faulty");
    }

    // 继续正常流程...
    break;
```

## 🔄 与现有系统的集成接口

### 1. 保持现有接口兼容
```c
// 保持现有的mission_step变量和UserTask_OneKeyCmd()接口
extern u8 mission_step;  // 继续使用现有变量

// 在UserTask_OneKeyCmd()中调用新的状态机
void UserTask_OneKeyCmd(void)
{
    // 现有的遥控器处理逻辑...

    // 执行野生动物巡查状态机
    if (mission_enabled_flag == 1 || zigbee_up_f == 1) {
        execute_wildlife_patrol_state_machine();
    }
}
```

### 2. 数据结构兼容性
```c
// 继续使用现有的数据结构
extern s16 work_pos[63][7];           // 工作点坐标数组
extern s16 actual_work_pos[63][4];    // 实际工作点坐标
extern s16 home_pos[4];               // 起始位置坐标
extern u8 LED_f;                      // LED控制标志
```

### 3. 函数复用映射
```c
// 新状态机中复用现有函数的映射关系
handle_mission_init()          → 状态0使用
handle_home_position_setup()   → 状态1使用
handle_work_point_navigation() → 状态4使用
is_position_reached()          → 状态4,7使用
handle_wait()                  → 状态5,7使用
handle_return_home()           → 状态7使用
land()                         → 状态12使用
```

## 📈 性能优化考虑

### 1. 内存使用优化
- 复用现有的work_pos和actual_work_pos数组
- 最小化新增全局变量
- 使用静态变量避免栈溢出

### 2. 执行效率优化
- 状态转换逻辑简洁高效
- 避免重复的位置检测计算
- 合理使用内联函数

### 3. 实时性保证
- 每个状态的执行时间控制在50Hz周期内
- 避免阻塞性操作
- 使用非阻塞的延时机制

## ✅ 实现准备

新架构设计完成，具备以下特点：
- **完全兼容**：与现有系统100%兼容，无需修改其他模块
- **智能路径**：集成Path_Planner实现最优巡查路径
- **安全可靠**：完整的错误处理和超时保护机制
- **功能完整**：支持激光指示、动物识别、数据传输、45°降落
- **易于维护**：清晰的状态定义、转换逻辑和接口设计
- **高性能**：优化的内存使用和执行效率

设计文档为后续实现提供了完整的技术路线图和实现规范。
