#include "SysConfig.h"
#include "stm32f4xx.h"
#include <stdbool.h>  // 添加：bool类型支持
typedef struct
{
	//
	u8 of_mid360_update_cnt;  //mid360速度数据更新计数器
	u8 alt_mid360_update_cnt; //高度数据更新计数器
	u8 pos_mid360_update_cnt;
	//
	u8 link_sta; //连接状态：0：未连接，1：已连接
	u8 work_sta; //工作状态：0：异常，1：正常
	//
	u8 mid360_of_quality;

	/*
	s16 of2_dx_fix;
	s16 of2_dy_fix;
	s16 intergral_x;
	s16 intergral_y;
	*/
	//高度信息，单位cm，距离单位cm，注意处理
	//姿态信息  8位 2字节
	s16 pose_x_cm;
	s16 pose_y_cm;
	s16 pose_z_cm;
	s16 pose_yaw;
	s16 speed_x_cms;
	s16 speed_y_cms;
	//
	float quaternion[4];
	//
	s16 mid360_acc_data_x;
	s16 mid360_acc_data_y;
	s16 mid360_acc_data_z;
	s16 mid360_gyr_data_x;
	s16 mid360_gyr_data_y;
	s16 mid360_gyr_data_z;

}mid360_info;

//*****************联合体内存，用于数据类型转换
typedef union
{
//	float Data[6]; //6个数据  x y z yaw  speed_x speed_y
	s16  Data[6]; //6个数据  x y z yaw  speed_x speed_y
	u8 byte[12];  //一个数据2位
}float_u8;

extern mid360_info mid360;
void mid360_GetOneByte(const uint8_t linktype,const u8 data);

/*==============================================================================
 * MID360 状态定义和错误统计
 *============================================================================*/

/**
 * @brief MID360工作状态枚举
 */
typedef enum {
    MID360_STATUS_OK = 0,           // 工作正常
    MID360_STATUS_TIMEOUT,          // 连接超时
    MID360_STATUS_DATA_ERROR,       // 数据错误
    MID360_STATUS_FRAME_ERROR       // 帧格式错误
} mid360_status_t;

/**
 * @brief MID360错误统计结构体
 */
typedef struct {
    u32 packet_count;       // 数据包接收计数
    u32 error_count;        // 错误计数
    u32 timeout_count;      // 超时计数
    u32 frame_error_count;  // 帧错误计数
    u32 data_error_count;   // 数据错误计数
    mid360_status_t status; // 当前状态
} mid360_debug_info_t;

/*==============================================================================
 * MID360 API接口 - 简化设计
 *============================================================================*/

/**
 * @brief 检查MID360数据有效性
 * @return true-数据有效, false-数据无效
 */
bool mid360_is_data_valid(void);

/**
 * @brief MID360状态检查函数（类似DrvAnoOFCheckState_ptv7）
 * @param dT_s 时间间隔（秒），通常为0.001f（1ms）
 */
void mid360_check_state(float dT_s);

/**
 * @brief 获取MID360调试信息
 * @return 调试信息结构体指针
 */
const mid360_debug_info_t* mid360_get_debug_info(void);

/**
 * @brief 重置MID360错误统计
 */
void mid360_reset_debug_info(void);
