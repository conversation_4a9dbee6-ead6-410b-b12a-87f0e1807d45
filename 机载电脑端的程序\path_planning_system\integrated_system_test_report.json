{"metadata": {"test_time": "2025-07-31 18:38:55", "total_duration_seconds": 0.022915363311767578, "tester": "<PERSON> (团队领袖) 统筹全体团队"}, "test_results": {"modules": {"module_status": {"no_fly_zone_combinations.json": {"exists": true, "size": 6105}, "dijkstra_precomputed_paths.json": {"exists": true, "size": 129435}, "optimized_return_paths.json": {"exists": true, "size": 129435}, "visualization_index.html": {"exists": true, "size": 85956}, "comprehensive_validation_report.json": {"exists": true, "size": 7431}, "../../FcSrc/User/path_storage.h": {"exists": true, "size": 3763}, "../../FcSrc/User/path_storage.c": {"exists": true, "size": 88164}}, "success_count": 7, "total_count": 7, "success_rate": 100.0}, "data": {"data_tests": {"no_fly_combinations": {"loaded": true, "count": 92, "expected": 92, "valid": true}, "precomputed_paths": {"loaded": true, "count": 92, "expected": 92, "valid": true}, "validation_report": {"loaded": true, "success_rate": 100.0, "valid": true}}, "valid_tests": 3, "total_tests": 3, "integrity_rate": 100.0}, "html": {"index_page": {"exists": true, "size": 79463, "has_css": true, "has_grid": true, "has_stats": true}, "detail_pages": {"count": 92, "expected": 92, "complete": true}, "test_page": {"exists": true}}, "c_code": {"header_file": {"exists": true, "size": 2694, "has_struct": true, "has_functions": true, "has_macros": true}, "source_file": {"exists": true, "size": 77211, "has_data": true, "has_functions": true, "has_debug": true}}}, "performance_metrics": {"data_loading": {"time_seconds": 0.0011341571807861328, "data_size_mb": 0.12343883514404297, "performance": "excellent"}, "memory_usage": {"total_size_mb": 0.13634777069091797, "file_sizes": {"no_fly_zone_combinations.json": 6105, "optimized_return_paths.json": 129435, "comprehensive_validation_report.json": 7431}, "efficiency": "excellent"}}, "summary": {"modules_available": 100.0, "data_integrity": 100.0, "html_complete": true, "c_code_ready": true, "overall_success_rate": 100.0}}