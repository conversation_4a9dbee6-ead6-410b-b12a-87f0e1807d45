{"metadata": {"validation_time": "2025-07-31 18:34:25", "validator_version": "1.0", "data_source": "optimized_return_paths.json"}, "summary": {"success_rate": 100.0, "coverage_success_rate": 100.0, "continuity_success_rate": 21.73913043478261}, "validation_stats": {"total_paths": 92, "valid_paths": 92, "invalid_paths": 0, "perfect_coverage_paths": 92, "continuous_paths": 20, "errors": {}, "warnings": {}}, "quality_stats": {"coverage_rate": {"min": 100.0, "max": 100.0, "avg": 100.0}, "patrol_length": {"min": 60, "max": 60, "avg": 60.0}, "return_length": {"min": 4, "max": 12, "avg": 7.630434782608695}, "jump_count": {"min": 0, "max": 2, "avg": 0.8369565217391305}}, "detailed_results": [{"index": 0, "no_fly_zones": [11, 21, 31], "completeness": {"valid": true, "errors": [], "warnings": []}, "coverage": {"expected_accessible": 60, "actual_visited": 60, "coverage_rate": 100.0, "unvisited_points": [], "invalid_visits": [], "perfect_coverage": true}, "patrol_continuity": {"valid": true, "max_distance": 1.0, "jump_count": 0, "jumps": []}, "return_continuity": {"valid": true, "max_distance": 1.0, "jump_count": 0, "jumps": []}, "efficiency": {"patrol_length": 60, "return_length": 6, "total_length": 66, "patrol_distance": 59.0, "return_distance": 5.0, "total_distance": 64.0, "patrol_efficiency": 1.0169491525423728, "return_efficiency": 1.2}}, {"index": 1, "no_fly_zones": [21, 31, 41], "completeness": {"valid": true, "errors": [], "warnings": []}, "coverage": {"expected_accessible": 60, "actual_visited": 60, "coverage_rate": 100.0, "unvisited_points": [], "invalid_visits": [], "perfect_coverage": true}, "patrol_continuity": {"valid": false, "max_distance": 2.0, "jump_count": 1, "jumps": [{"from": 11, "to": 13, "distance": 2.0, "grid_from": [0, 0], "grid_to": [2, 0]}]}, "return_continuity": {"valid": true, "max_distance": 1.0, "jump_count": 0, "jumps": []}, "efficiency": {"patrol_length": 60, "return_length": 7, "total_length": 67, "patrol_distance": 60.0, "return_distance": 6.0, "total_distance": 66.0, "patrol_efficiency": 1.0, "return_efficiency": 1.1666666666666667}}, {"index": 2, "no_fly_zones": [31, 41, 51], "completeness": {"valid": true, "errors": [], "warnings": []}, "coverage": {"expected_accessible": 60, "actual_visited": 60, "coverage_rate": 100.0, "unvisited_points": [], "invalid_visits": [], "perfect_coverage": true}, "patrol_continuity": {"valid": false, "max_distance": 2.0, "jump_count": 1, "jumps": [{"from": 94, "to": 96, "distance": 2.0, "grid_from": [3, 8], "grid_to": [5, 8]}]}, "return_continuity": {"valid": true, "max_distance": 1.0, "jump_count": 0, "jumps": []}, "efficiency": {"patrol_length": 60, "return_length": 7, "total_length": 67, "patrol_distance": 60.0, "return_distance": 6.0, "total_distance": 66.0, "patrol_efficiency": 1.0, "return_efficiency": 1.1666666666666667}}, {"index": 3, "no_fly_zones": [41, 51, 61], "completeness": {"valid": true, "errors": [], "warnings": []}, "coverage": {"expected_accessible": 60, "actual_visited": 60, "coverage_rate": 100.0, "unvisited_points": [], "invalid_visits": [], "perfect_coverage": true}, "patrol_continuity": {"valid": false, "max_distance": 6.0, "jump_count": 1, "jumps": [{"from": 17, "to": 77, "distance": 6.0, "grid_from": [6, 0], "grid_to": [6, 6]}]}, "return_continuity": {"valid": true, "max_distance": 1.0, "jump_count": 0, "jumps": []}, "efficiency": {"patrol_length": 60, "return_length": 7, "total_length": 67, "patrol_distance": 64.0, "return_distance": 6.0, "total_distance": 70.0, "patrol_efficiency": 0.9375, "return_efficiency": 1.1666666666666667}}, {"index": 4, "no_fly_zones": [51, 61, 71], "completeness": {"valid": true, "errors": [], "warnings": []}, "coverage": {"expected_accessible": 60, "actual_visited": 60, "coverage_rate": 100.0, "unvisited_points": [], "invalid_visits": [], "perfect_coverage": true}, "patrol_continuity": {"valid": false, "max_distance": 7.0, "jump_count": 2, "jumps": [{"from": 92, "to": 94, "distance": 2.0, "grid_from": [1, 8], "grid_to": [3, 8]}, {"from": 17, "to": 87, "distance": 7.0, "grid_from": [6, 0], "grid_to": [6, 7]}]}, "return_continuity": {"valid": true, "max_distance": 1.0, "jump_count": 0, "jumps": []}, "efficiency": {"patrol_length": 60, "return_length": 7, "total_length": 67, "patrol_distance": 66.0, "return_distance": 6.0, "total_distance": 72.0, "patrol_efficiency": 0.9090909090909091, "return_efficiency": 1.1666666666666667}}]}