# Zigbee校准系统快速使用指南

## 🎯 核心功能

### 1. 任务启动
```bash
AA FF 01 00 EA  # 启动货物遍历任务
```

### 2. 坐标校准
```bash
AA FF 03 01 EA  # 备份原始数据
AA FF 03 04 EA  # 设置校准点1（原点）
AA FF 03 05 EA  # 设置校准点2并执行校准（终点）
AA FF 03 02 EA  # 恢复原始数据（如需要）
```

## 📋 完整校准流程

### 步骤1: 备份数据
```bash
# 发送命令
AA FF 03 01 EA
# 等待回复: AA FF 01 01 EA
```

### 步骤2: 设置原点校准
```bash
# 将飞机手动搬到原点位置
# 发送命令
AA FF 03 04 EA
# 等待回复: AA FF 04 04 EA
```

### 步骤3: 执行终点校准
```bash
# 将飞机手动搬到终点位置
# 发送命令
AA FF 03 05 EA
# 等待回复: AA FF 05 05 EA
```

### 步骤4: 启动任务
```bash
# 发送命令
AA FF 01 00 EA
# 等待回复: AA FF 01 01 EA
```

## ⚠️ 注意事项

1. **校准前必须备份**: 使用ID=1备份原始数据
2. **校准点距离**: 两个校准点距离建议>100cm
3. **MID360状态**: 确保连接正常（link_sta=1, work_sta=1）
4. **恢复机制**: 校准失败时使用ID=2恢复原始数据

## 🔧 故障排除

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 任务无法启动 | mission_enabled_flag!=0 | 确保没有其他任务在执行 |
| 校准点1未设置 | 未执行ID=4 | 先设置第一个校准点 |
| 校准点太近 | 距离<1cm | 选择距离更远的校准点 |
| MID360无效 | 连接异常 | 检查MID360状态 |

## 📊 预期效果

**校准前**: MID360(300,300) → 实际(297,295)  
**校准后**: 精度提升至±1cm

---
**版本**: v2.0 | **状态**: 可用
