#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面路径数据验证与质量检查工具
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：David (数据分析师)
编码格式：UTF-8

功能描述：
对生成的所有路径数据进行全面验证，检查路径完整性、覆盖率、最优性等质量指标。
生成详细的验证报告和统计分析。
"""

import json
import time
import math
from typing import Dict, List, Tuple, Set
from collections import Counter, defaultdict

class ComprehensivePathValidator:
    """全面路径数据验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.expected_start_code = 91  # A9B1
        self.grid_rows = 7  # B1-B7
        self.grid_cols = 9  # A1-A9
        self.expected_patrol_length = 60  # 预期巡查路径长度
        
    def position_code_to_grid(self, position_code: int) -> <PERSON><PERSON>[int, int]:
        """position_code转网格坐标"""
        if position_code < 11 or position_code > 97:
            return (-1, -1)
        
        col_num = position_code // 10  # A列号 (1-9)
        row_num = position_code % 10   # B行号 (1-7)
        
        if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
            return (-1, -1)
        
        row = row_num - 1  # B1->0, B2->1, ..., B7->6
        col = col_num - 1  # A1->0, A2->1, ..., A9->8
        
        return (row, col)
    
    def get_all_valid_positions(self) -> Set[int]:
        """获取所有有效的position_code"""
        valid_positions = set()
        for row in range(1, self.grid_rows + 1):
            for col in range(1, self.grid_cols + 1):
                position_code = col * 10 + row
                valid_positions.add(position_code)
        return valid_positions
    
    def validate_path_completeness(self, path_data: Dict) -> Dict:
        """验证单个路径的完整性"""
        errors = []
        warnings = []
        
        # 检查必要字段
        required_fields = ['no_fly_zones', 'patrol_path_sequence', 'patrol_path_length', 
                          'return_path_sequence', 'return_path_length', 'is_valid']
        
        for field in required_fields:
            if field not in path_data:
                errors.append(f"缺少必要字段: {field}")
        
        if errors:
            return {'valid': False, 'errors': errors, 'warnings': warnings}
        
        # 验证禁飞区
        no_fly_zones = path_data['no_fly_zones']
        if len(no_fly_zones) != 3:
            errors.append(f"禁飞区数量错误: {len(no_fly_zones)}, 期望: 3")
        
        if self.expected_start_code in no_fly_zones:
            errors.append(f"禁飞区包含起点A9B1: {no_fly_zones}")
        
        # 验证巡查路径
        patrol_path = path_data['patrol_path_sequence']
        patrol_length = path_data['patrol_path_length']
        
        if len(patrol_path) != patrol_length:
            errors.append(f"巡查路径长度不一致: 声明{patrol_length}, 实际{len(patrol_path)}")
        
        if patrol_length != self.expected_patrol_length:
            warnings.append(f"巡查路径长度异常: {patrol_length}, 期望: {self.expected_patrol_length}")
        
        # 验证起点
        if patrol_path and patrol_path[0] != self.expected_start_code:
            errors.append(f"起点错误: {patrol_path[0]}, 期望: {self.expected_start_code}")
        
        # 验证返航路径
        return_path = path_data['return_path_sequence']
        return_length = path_data['return_path_length']
        
        if len(return_path) != return_length:
            errors.append(f"返航路径长度不一致: 声明{return_length}, 实际{len(return_path)}")
        
        if return_path and return_path[-1] != self.expected_start_code:
            errors.append(f"返航终点错误: {return_path[-1]}, 期望: {self.expected_start_code}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def validate_path_coverage(self, path_data: Dict) -> Dict:
        """验证路径覆盖率"""
        no_fly_zones = set(path_data['no_fly_zones'])
        patrol_path = path_data['patrol_path_sequence']
        
        # 获取所有可访问点
        all_valid_positions = self.get_all_valid_positions()
        accessible_positions = all_valid_positions - no_fly_zones
        
        # 检查访问的点
        visited_positions = set(patrol_path)
        
        # 计算覆盖率
        expected_accessible = len(accessible_positions)
        actual_visited = len(visited_positions & accessible_positions)
        coverage_rate = (actual_visited / expected_accessible) * 100 if expected_accessible > 0 else 0
        
        # 检查未访问的点
        unvisited = accessible_positions - visited_positions
        
        # 检查访问了不应该访问的点
        invalid_visits = visited_positions - accessible_positions
        
        return {
            'expected_accessible': expected_accessible,
            'actual_visited': actual_visited,
            'coverage_rate': coverage_rate,
            'unvisited_points': list(unvisited),
            'invalid_visits': list(invalid_visits),
            'perfect_coverage': len(unvisited) == 0 and len(invalid_visits) == 0
        }
    
    def validate_path_continuity(self, path_sequence: List[int]) -> Dict:
        """验证路径连续性"""
        if len(path_sequence) < 2:
            return {'valid': True, 'max_distance': 0, 'jump_count': 0, 'jumps': []}
        
        jumps = []
        max_distance = 0
        
        for i in range(len(path_sequence) - 1):
            current_pos = self.position_code_to_grid(path_sequence[i])
            next_pos = self.position_code_to_grid(path_sequence[i + 1])
            
            if current_pos == (-1, -1) or next_pos == (-1, -1):
                jumps.append({
                    'from': path_sequence[i],
                    'to': path_sequence[i + 1],
                    'reason': 'invalid_position'
                })
                continue
            
            # 计算移动距离
            row_diff = abs(next_pos[0] - current_pos[0])
            col_diff = abs(next_pos[1] - current_pos[1])
            distance = math.sqrt(row_diff**2 + col_diff**2)
            
            max_distance = max(max_distance, distance)
            
            # 检查是否是相邻移动（8方向）
            if row_diff > 1 or col_diff > 1:
                jumps.append({
                    'from': path_sequence[i],
                    'to': path_sequence[i + 1],
                    'distance': distance,
                    'grid_from': current_pos,
                    'grid_to': next_pos
                })
        
        return {
            'valid': len(jumps) == 0,
            'max_distance': max_distance,
            'jump_count': len(jumps),
            'jumps': jumps[:10]  # 只保留前10个跳跃
        }
    
    def analyze_path_efficiency(self, path_data: Dict) -> Dict:
        """分析路径效率"""
        patrol_path = path_data['patrol_path_sequence']
        return_path = path_data['return_path_sequence']
        
        # 计算路径长度
        patrol_length = len(patrol_path)
        return_length = len(return_path)
        total_length = patrol_length + return_length
        
        # 计算实际移动距离
        def calculate_path_distance(path_sequence):
            if len(path_sequence) < 2:
                return 0
            
            total_distance = 0
            for i in range(len(path_sequence) - 1):
                current_pos = self.position_code_to_grid(path_sequence[i])
                next_pos = self.position_code_to_grid(path_sequence[i + 1])
                
                if current_pos != (-1, -1) and next_pos != (-1, -1):
                    row_diff = abs(next_pos[0] - current_pos[0])
                    col_diff = abs(next_pos[1] - current_pos[1])
                    distance = math.sqrt(row_diff**2 + col_diff**2)
                    total_distance += distance
            
            return total_distance
        
        patrol_distance = calculate_path_distance(patrol_path)
        return_distance = calculate_path_distance(return_path)
        total_distance = patrol_distance + return_distance
        
        # 计算效率指标
        patrol_efficiency = patrol_length / patrol_distance if patrol_distance > 0 else 0
        return_efficiency = return_length / return_distance if return_distance > 0 else 0
        
        return {
            'patrol_length': patrol_length,
            'return_length': return_length,
            'total_length': total_length,
            'patrol_distance': patrol_distance,
            'return_distance': return_distance,
            'total_distance': total_distance,
            'patrol_efficiency': patrol_efficiency,
            'return_efficiency': return_efficiency
        }
    
    def validate_all_paths(self, data_file: str) -> Dict:
        """验证所有路径数据"""
        print("🔍 开始全面路径数据验证")
        print("=" * 70)
        
        # 加载数据
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            path_data_list = data['path_data']
            print(f"📁 成功加载 {len(path_data_list)} 个路径数据")
        except FileNotFoundError:
            return {'error': f'数据文件不存在: {data_file}'}
        except Exception as e:
            return {'error': f'数据加载失败: {e}'}
        
        # 验证统计
        validation_stats = {
            'total_paths': len(path_data_list),
            'valid_paths': 0,
            'invalid_paths': 0,
            'perfect_coverage_paths': 0,
            'continuous_paths': 0,
            'errors': defaultdict(int),
            'warnings': defaultdict(int)
        }
        
        # 质量指标统计
        coverage_rates = []
        patrol_lengths = []
        return_lengths = []
        patrol_distances = []
        return_distances = []
        jump_counts = []
        
        detailed_results = []
        
        print("\n🔍 逐个验证路径数据...")
        
        for i, path_data in enumerate(path_data_list):
            if (i + 1) % 20 == 0:
                print(f"   验证进度: {i + 1}/{len(path_data_list)}")
            
            # 完整性验证
            completeness = self.validate_path_completeness(path_data)
            
            # 覆盖率验证
            coverage = self.validate_path_coverage(path_data)
            
            # 连续性验证
            patrol_continuity = self.validate_path_continuity(path_data['patrol_path_sequence'])
            return_continuity = self.validate_path_continuity(path_data['return_path_sequence'])
            
            # 效率分析
            efficiency = self.analyze_path_efficiency(path_data)
            
            # 统计结果
            if completeness['valid'] and path_data.get('is_valid', False):
                validation_stats['valid_paths'] += 1
            else:
                validation_stats['invalid_paths'] += 1
            
            if coverage['perfect_coverage']:
                validation_stats['perfect_coverage_paths'] += 1
            
            if patrol_continuity['valid'] and return_continuity['valid']:
                validation_stats['continuous_paths'] += 1
            
            # 收集错误和警告
            for error in completeness['errors']:
                validation_stats['errors'][error] += 1
            
            for warning in completeness['warnings']:
                validation_stats['warnings'][warning] += 1
            
            # 收集质量指标
            coverage_rates.append(coverage['coverage_rate'])
            patrol_lengths.append(efficiency['patrol_length'])
            return_lengths.append(efficiency['return_length'])
            patrol_distances.append(efficiency['patrol_distance'])
            return_distances.append(efficiency['return_distance'])
            jump_counts.append(patrol_continuity['jump_count'] + return_continuity['jump_count'])
            
            # 保存详细结果（只保存前5个用于示例）
            if i < 5:
                detailed_results.append({
                    'index': i,
                    'no_fly_zones': path_data['no_fly_zones'],
                    'completeness': completeness,
                    'coverage': coverage,
                    'patrol_continuity': patrol_continuity,
                    'return_continuity': return_continuity,
                    'efficiency': efficiency
                })
        
        # 计算质量指标统计
        quality_stats = {
            'coverage_rate': {
                'min': min(coverage_rates) if coverage_rates else 0,
                'max': max(coverage_rates) if coverage_rates else 0,
                'avg': sum(coverage_rates) / len(coverage_rates) if coverage_rates else 0
            },
            'patrol_length': {
                'min': min(patrol_lengths) if patrol_lengths else 0,
                'max': max(patrol_lengths) if patrol_lengths else 0,
                'avg': sum(patrol_lengths) / len(patrol_lengths) if patrol_lengths else 0
            },
            'return_length': {
                'min': min(return_lengths) if return_lengths else 0,
                'max': max(return_lengths) if return_lengths else 0,
                'avg': sum(return_lengths) / len(return_lengths) if return_lengths else 0
            },
            'jump_count': {
                'min': min(jump_counts) if jump_counts else 0,
                'max': max(jump_counts) if jump_counts else 0,
                'avg': sum(jump_counts) / len(jump_counts) if jump_counts else 0
            }
        }
        
        # 计算成功率
        success_rate = (validation_stats['valid_paths'] / validation_stats['total_paths']) * 100 if validation_stats['total_paths'] > 0 else 0
        coverage_success_rate = (validation_stats['perfect_coverage_paths'] / validation_stats['total_paths']) * 100 if validation_stats['total_paths'] > 0 else 0
        continuity_success_rate = (validation_stats['continuous_paths'] / validation_stats['total_paths']) * 100 if validation_stats['total_paths'] > 0 else 0
        
        return {
            'validation_stats': validation_stats,
            'quality_stats': quality_stats,
            'success_rate': success_rate,
            'coverage_success_rate': coverage_success_rate,
            'continuity_success_rate': continuity_success_rate,
            'detailed_results': detailed_results
        }

def main():
    """主函数"""
    print("🚀 全面路径数据验证与质量检查")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：David (数据分析师)")
    print("=" * 70)
    
    validator = ComprehensivePathValidator()
    
    # 验证优化后的路径数据
    results = validator.validate_all_paths('optimized_return_paths.json')
    
    if 'error' in results:
        print(f"❌ 验证失败: {results['error']}")
        return
    
    # 输出验证结果
    validation_stats = results['validation_stats']
    quality_stats = results['quality_stats']
    
    print(f"\n📊 验证结果统计:")
    print(f"   总路径数: {validation_stats['total_paths']}")
    print(f"   有效路径: {validation_stats['valid_paths']}")
    print(f"   无效路径: {validation_stats['invalid_paths']}")
    print(f"   完美覆盖: {validation_stats['perfect_coverage_paths']}")
    print(f"   连续路径: {validation_stats['continuous_paths']}")
    print(f"   成功率: {results['success_rate']:.1f}%")
    print(f"   覆盖成功率: {results['coverage_success_rate']:.1f}%")
    print(f"   连续性成功率: {results['continuity_success_rate']:.1f}%")
    
    print(f"\n📈 质量指标统计:")
    print(f"   覆盖率: {quality_stats['coverage_rate']['min']:.1f}% - {quality_stats['coverage_rate']['max']:.1f}% (平均: {quality_stats['coverage_rate']['avg']:.1f}%)")
    print(f"   巡查长度: {quality_stats['patrol_length']['min']} - {quality_stats['patrol_length']['max']} (平均: {quality_stats['patrol_length']['avg']:.1f})")
    print(f"   返航长度: {quality_stats['return_length']['min']} - {quality_stats['return_length']['max']} (平均: {quality_stats['return_length']['avg']:.1f})")
    print(f"   跳跃次数: {quality_stats['jump_count']['min']} - {quality_stats['jump_count']['max']} (平均: {quality_stats['jump_count']['avg']:.1f})")
    
    # 输出错误统计
    if validation_stats['errors']:
        print(f"\n❌ 错误统计:")
        for error, count in validation_stats['errors'].items():
            print(f"   {error}: {count}次")
    
    if validation_stats['warnings']:
        print(f"\n⚠️ 警告统计:")
        for warning, count in validation_stats['warnings'].items():
            print(f"   {warning}: {count}次")
    
    # 保存验证报告
    report_data = {
        'metadata': {
            'validation_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'validator_version': '1.0',
            'data_source': 'optimized_return_paths.json'
        },
        'summary': {
            'success_rate': results['success_rate'],
            'coverage_success_rate': results['coverage_success_rate'],
            'continuity_success_rate': results['continuity_success_rate']
        },
        'validation_stats': validation_stats,
        'quality_stats': quality_stats,
        'detailed_results': results['detailed_results']
    }
    
    with open('comprehensive_validation_report.json', 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 详细验证报告已保存: comprehensive_validation_report.json")
    
    # 总结
    print(f"\n🎉 路径数据验证完成!")
    if results['success_rate'] >= 95:
        print("✅ 验证结果优秀，所有路径数据质量良好")
    elif results['success_rate'] >= 80:
        print("⚠️ 验证结果良好，部分路径需要关注")
    else:
        print("❌ 验证结果需要改进，存在较多问题")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
