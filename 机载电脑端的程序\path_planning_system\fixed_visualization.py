#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正后的可视化模块 - 完全独立实现
"""

def visualize_path_correct(path_sequence, title="路径可视化"):
    """
    正确的路径可视化函数
    
    参数:
        path_sequence: position_code序列 (如[11, 21, 31, 41, 51])
        title: 标题
        
    返回:
        str: 正确的可视化字符串
    """
    # 网格常量
    GRID_ROWS = 7    # B1-B7
    GRID_COLS = 9    # A1-A9
    
    # 创建可视化网格
    vis_grid = [['.' for _ in range(GRID_COLS)] for _ in range(GRID_ROWS)]
    
    # position_code转网格坐标的函数
    def position_code_to_grid(position_code):
        if position_code < 11 or position_code > 97:
            return (-1, -1)
        
        col_num = position_code // 10  # A列号 (1-9)
        row_num = position_code % 10   # B行号 (1-7)
        
        if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
            return (-1, -1)
        
        row = row_num - 1  # B1->0, B2->1, ..., B7->6
        col = col_num - 1  # A1->0, A2->1, ..., A9->8
        
        return (row, col)
    
    # 标记路径
    for i, position_code in enumerate(path_sequence):
        row, col = position_code_to_grid(position_code)
        if row >= 0 and col >= 0:
            if i == 0:
                vis_grid[row][col] = 'S'  # 起点
            elif i == len(path_sequence) - 1:
                vis_grid[row][col] = 'E'  # 终点
            else:
                # 使用数字表示访问顺序
                if i <= 9:
                    vis_grid[row][col] = str(i)
                else:
                    vis_grid[row][col] = '*'
    
    # 生成可视化字符串
    result = f"\n{'='*60}\n{title}\n{'='*60}\n"
    result += "    "
    
    # 列标题：A1 A2 A3 A4 A5 A6 A7 A8 A9
    for col in range(GRID_COLS):
        result += f"A{col+1} "
    result += "\n"
    
    # 行数据：B1到B7
    for row in range(GRID_ROWS):
        row_label = f"B{row+1}"  # B1, B2, B3, B4, B5, B6, B7
        result += f"{row_label}  "
        for col in range(GRID_COLS):
            result += f" {vis_grid[row][col]} "
        result += f"  {row_label}\n"
    
    # 底部列标题：A1 A2 A3 A4 A5 A6 A7 A8 A9
    result += "    "
    for col in range(GRID_COLS):
        result += f"A{col+1} "
    result += "\n"
    
    # 图例
    result += "\n图例说明:\n"
    result += "S = 起点, E = 终点, 1-9 = 访问顺序, * = 后续访问点\n"
    result += "█ = 禁飞区, . = 未访问点\n"
    result += f"路径长度: {len(path_sequence)} 个点\n"
    result += f"Position codes: {path_sequence[:15]}{'...' if len(path_sequence) > 15 else ''}\n"
    result += "="*60 + "\n"
    
    return result

def test_correct_visualization():
    """测试正确的可视化"""
    print("🎯 测试修正后的可视化函数")
    
    # 测试路径：A1B1 -> A2B1 -> A3B1 -> A4B1 -> A5B1
    path_sequence = [11, 21, 31, 41, 51]
    
    visualization = visualize_path_correct(path_sequence, "修正后的可视化测试")
    print(visualization)
    
    # 测试禁飞区场景
    print("\n🚫 测试带禁飞区的场景")
    # 路径：A1B1 -> A2B1 -> A3B1 -> A4B1 -> A5B1 -> A6B1 -> A7B1 -> A8B1 -> A9B1
    # 然后：A9B2 -> A8B2 -> A7B2 -> A6B2 -> A5B2 -> A4B2 -> A3B2 -> A2B2 -> A1B2
    path_with_no_fly = [11, 21, 31, 41, 51, 61, 71, 81, 91,  # B1行
                        92, 82, 72, 62, 52, 42, 32, 22, 12]  # B2行
    
    visualization2 = visualize_path_correct(path_with_no_fly, "带禁飞区的遍历路径")
    print(visualization2)

if __name__ == "__main__":
    test_correct_visualization()
