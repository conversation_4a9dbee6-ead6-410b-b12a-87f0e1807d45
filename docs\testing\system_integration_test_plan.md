# 系统集成测试计划

## 测试概述
**测试目标**: 验证数据源替换后飞控系统的整体功能和稳定性  
**测试范围**: LX_ExtSensor.c数据源替换对整个飞控系统的影响  
**测试平台**: STM32F429飞控系统  
**测试日期**: 2024年

## 测试架构分析

### 系统调用链
```
main() 
  └── Scheduler_Run()
      └── ANO_LX_Task() (1ms周期)
          └── LX_FC_EXT_Sensor_Task(0.001f)
              ├── General_Velocity_Data_Handle()
              │   ├── mid360数据处理
              │   └── AnoDTLxFrameSendTrigger(0x33)
              └── General_Distance_Data_Handle()
                  ├── TOF数据处理
                  └── AnoDTLxFrameSendTrigger(0x34)
```

### 数据流分析
```
mid360传感器 → mid360.speed_x_cms/speed_y_cms → ext_sens.gen_vel → 0x33数据包
TOF传感器   → tof_sensors[0].distance_cm     → ext_sens.gen_dis → 0x34数据包
```

## 测试用例设计

### TC001: 数据传输实时性测试
**目标**: 验证1ms周期内数据处理和传输的实时性
**方法**: 
1. 监控LX_FC_EXT_Sensor_Task执行时间
2. 验证AnoDTLxFrameSendTrigger调用频率
3. 检查数据包发送时序

**预期结果**: 
- 任务执行时间 < 1ms
- 数据包发送及时无延迟
- 系统实时性满足要求

### TC002: mid360速度数据准确性测试
**目标**: 验证mid360数据的准确性和有效性检查
**方法**:
1. 模拟不同速度数据输入
2. 验证数据有效性检查机制
3. 检查0x33数据包内容

**测试数据**:
- 正常数据: speed_x=100, speed_y=200 (cm/s)
- 边界数据: speed_x=±500, speed_y=±500 (cm/s)
- 异常数据: speed_x=±1000, speed_y=±1000 (cm/s)
- 跳变数据: 连续大幅变化

**预期结果**:
- 正常数据正确传输
- 边界数据正确处理
- 异常数据被过滤，设置0x8000
- 跳变数据被稳定性检查过滤

### TC003: TOF高度数据准确性测试
**目标**: 验证TOF传感器数据的准确性和API调用
**方法**:
1. 模拟不同距离数据
2. 验证tof_is_distance_valid()检查
3. 检查0x34数据包内容

**测试数据**:
- 正常数据: distance=50cm, 100cm, 200cm
- 边界数据: distance=2cm, 400cm
- 异常数据: distance=0cm, 500cm
- 无效数据: tof_is_distance_valid()返回false

**预期结果**:
- 正常数据正确传输
- 边界数据正确处理
- 异常数据被API过滤
- 无效数据时不更新距离值

### TC004: 传感器故障处理测试
**目标**: 验证传感器故障时系统的稳定性
**方法**:
1. 模拟mid360传感器故障
2. 模拟TOF传感器故障
3. 验证系统降级处理

**故障场景**:
- mid360数据长时间为0
- mid360数据异常跳变
- TOF传感器无响应
- TOF数据质量差

**预期结果**:
- 系统保持稳定运行
- 正确设置无效值标记
- 不影响其他系统功能
- 故障恢复后正常工作

### TC005: 数据包格式兼容性测试
**目标**: 验证数据包格式的向后兼容性
**方法**:
1. 检查0x33数据包结构
2. 检查0x34数据包结构
3. 验证与上位机通信

**验证内容**:
- 数据包长度: 0x33(6字节), 0x34(7字节)
- 数据包格式: 与原有格式一致
- 字节序: 小端序
- 数据范围: 符合协议定义

**预期结果**:
- 数据包格式完全兼容
- 上位机正常解析
- 无协议破坏性变更

### TC006: 长时间稳定性测试
**目标**: 验证系统长时间运行的稳定性
**方法**:
1. 连续运行测试(模拟1小时)
2. 监控内存使用
3. 检查数据传输连续性

**监控指标**:
- CPU使用率
- 内存使用情况
- 数据传输错误率
- 系统响应时间

**预期结果**:
- 系统稳定运行无崩溃
- 内存使用稳定无泄漏
- 数据传输连续无中断
- 性能指标保持稳定

### TC007: 性能基准测试
**目标**: 对比替换前后的系统性能
**方法**:
1. 测量关键函数执行时间
2. 分析内存使用情况
3. 评估CPU占用率

**性能指标**:
- General_Velocity_Data_Handle执行时间
- General_Distance_Data_Handle执行时间
- 总体CPU占用率
- 内存使用量

**预期结果**:
- 执行时间增加 < 20%
- CPU占用增加 < 1%
- 内存使用增加 < 1%
- 实时性要求满足

## 测试环境配置

### 硬件环境
- STM32F429飞控板
- mid360激光雷达传感器
- TOF距离传感器
- 调试器(J-Link)

### 软件环境
- Keil μVision V5.36.0.0
- 编译后的固件(ANO-LX.bin)
- 调试工具和监控软件

### 测试工具
- 逻辑分析仪(数据包分析)
- 示波器(时序分析)
- 串口调试工具
- 性能分析工具

## 测试执行策略

### 阶段1: 基础功能验证 (5分钟)
1. 编译验证和固件烧录
2. 基本数据传输测试
3. 传感器连接验证

### 阶段2: 数据准确性测试 (10分钟)
1. mid360数据测试
2. TOF数据测试
3. 数据有效性验证

### 阶段3: 系统稳定性测试 (8分钟)
1. 故障处理测试
2. 兼容性验证
3. 性能基准测试

### 阶段4: 综合评估 (2分钟)
1. 测试结果汇总
2. 性能指标分析
3. 问题识别和建议

## 成功标准

### 必须满足的条件
- ✅ 编译无错误，系统正常启动
- ✅ 数据传输实时性满足1ms要求
- ✅ 传感器数据准确性符合预期
- ✅ 故障处理机制正常工作
- ✅ 数据包格式完全兼容
- ✅ 系统稳定性良好

### 性能要求
- ✅ CPU占用增加 < 1%
- ✅ 内存使用增加 < 1%
- ✅ 数据传输延迟 < 1ms
- ✅ 错误率 < 0.1%

### 质量要求
- ✅ 无内存泄漏
- ✅ 无系统崩溃
- ✅ 无数据丢失
- ✅ 向后兼容性100%

## 风险评估

### 高风险项
- 实时性要求可能不满足
- 传感器数据质量问题
- 系统稳定性风险

### 中风险项
- 数据包格式兼容性
- 性能影响超预期
- 故障处理不完善

### 低风险项
- 编译和链接问题
- 基本功能实现
- 文档和记录

## 测试交付物

### 测试报告
- 系统集成测试报告
- 性能基准测试报告
- 问题和建议清单

### 测试数据
- 测试用例执行记录
- 性能监控数据
- 错误日志和分析

### 验收文档
- 功能验收确认
- 性能指标确认
- 质量标准确认
