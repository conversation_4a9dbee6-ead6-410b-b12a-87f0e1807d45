#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从最新的path_storage.c文件中提取数据并更新HTML可视化文件
"""

import re
import os

def extract_path_data_from_c_file():
    """从C文件中提取路径数据"""
    
    with open('../../FcSrc/User/path_storage.c', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取所有路径数据
    path_pattern = r'// 路径(\d+): 禁飞区\[(\d+), (\d+), (\d+)\], 巡查长度(\d+), 返航长度(\d+)\s*\{\s*\{(\d+), (\d+), (\d+)\},.*?巡查路径序列\s*(.*?)\s*\},\s*(\d+),.*?返航路径序列\s*(.*?)\s*\}'
    
    matches = re.findall(path_pattern, content, re.DOTALL)
    
    path_data = []
    for match in matches:
        path_num = int(match[0])
        no_fly_zones = [int(match[1]), int(match[2]), int(match[3])]
        patrol_length = int(match[4])
        return_length = int(match[5])
        
        # 提取巡查路径序列
        patrol_text = match[8]
        patrol_numbers = re.findall(r'\b\d+\b', patrol_text)
        patrol_path = [int(n) for n in patrol_numbers if int(n) > 0]
        
        # 提取返航路径序列
        return_text = match[10]
        return_numbers = re.findall(r'\b\d+\b', return_text)
        return_path = [int(n) for n in return_numbers if int(n) > 0]
        
        path_data.append({
            'path_num': path_num,
            'no_fly_zones': no_fly_zones,
            'patrol_path': patrol_path,
            'return_path': return_path,
            'patrol_length': patrol_length,
            'return_length': return_length
        })
    
    return path_data

def generate_html_for_path(path_data):
    """为单个路径生成HTML可视化"""
    
    path_num = path_data['path_num']
    no_fly_zones = path_data['no_fly_zones']
    patrol_path = path_data['patrol_path']
    return_path = path_data['return_path']
    
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径{path_num} - 禁飞区{no_fly_zones} (最新修正版)</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }}
        .grid-container {{
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }}
        .grid {{
            display: grid;
            grid-template-columns: repeat(9, 50px);
            grid-template-rows: repeat(7, 50px);
            gap: 2px;
            border: 3px solid #333;
            background-color: #333;
            padding: 10px;
            border-radius: 10px;
        }}
        .cell {{
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            color: white;
            border-radius: 4px;
            position: relative;
        }}
        .normal {{ background-color: #4CAF50; }}
        .no-fly {{ background-color: #f44336; }}
        .start {{ background-color: #2196F3; }}
        .patrol {{ background-color: #FF9800; }}
        .return {{ background-color: #9C27B0; }}
        .path-number {{
            position: absolute;
            top: 2px;
            left: 2px;
            font-size: 8px;
            background: rgba(0,0,0,0.7);
            padding: 1px 3px;
            border-radius: 2px;
        }}
        .info-panel {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }}
        .info-box {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }}
        .legend {{
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }}
        .path-sequence {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }}
        .navigation {{
            text-align: center;
            margin: 30px 0;
        }}
        .nav-button {{
            background: #007bff;
            color: white;
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }}
        .nav-button:hover {{
            background: #0056b3;
        }}
        .highlight-17 {{
            background-color: #FF5722 !important;
            animation: pulse 2s infinite;
        }}
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.7; }}
            100% {{ opacity: 1; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>路径{path_num} 可视化 (最新修正版)</h1>
            <p>禁飞区: {no_fly_zones} | 巡查点数: {len(patrol_path)} | 返航点数: {len(return_path)}</p>
            <p><strong>✅ 包含位置17，确保100%覆盖</strong></p>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color start"></div>
                <span>起点/终点 (A9B1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color no-fly"></div>
                <span>禁飞区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color patrol"></div>
                <span>巡查路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color return"></div>
                <span>返航路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color normal"></div>
                <span>正常区域</span>
            </div>
            <div class="legend-item">
                <div class="legend-color highlight-17" style="background-color: #FF5722;"></div>
                <span>位置17 (重点关注)</span>
            </div>
        </div>

        <div class="grid-container">
            <div class="grid" id="pathGrid">
                <!-- 网格将由JavaScript生成 -->
            </div>
        </div>

        <div class="info-panel">
            <div class="info-box">
                <h3>🎯 巡查路径序列 ({len(patrol_path)}个点)</h3>
                <div class="path-sequence">
                    {' → '.join(map(str, patrol_path))}
                </div>
                <p><strong>包含位置17: {'✅ 是' if 17 in patrol_path else '❌ 否'}</strong></p>
            </div>
            <div class="info-box">
                <h3>🏠 返航路径序列 ({len(return_path)}个点)</h3>
                <div class="path-sequence">
                    {' → '.join(map(str, return_path))}
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="visualization_index.html" class="nav-button">返回主页</a>
            <a href="path_{(path_num-1):03d}.html" class="nav-button">上一个</a>
            <a href="path_{(path_num+1):03d}.html" class="nav-button">下一个</a>
        </div>
    </div>

    <script>
        // 网格数据
        const noFlyZones = {no_fly_zones};
        const patrolPath = {patrol_path};
        const returnPath = {return_path};
        
        // 生成网格
        function generateGrid() {{
            const grid = document.getElementById('pathGrid');
            
            // 创建7行9列的网格 (B7到B1, A1到A9)
            for (let row = 6; row >= 0; row--) {{
                for (let col = 0; col < 9; col++) {{
                    const positionCode = (col + 1) * 10 + (row + 1);
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.textContent = positionCode;
                    
                    // 确定单元格类型
                    if (positionCode === 91) {{
                        cell.classList.add('start');
                    }} else if (noFlyZones.includes(positionCode)) {{
                        cell.classList.add('no-fly');
                    }} else if (patrolPath.includes(positionCode)) {{
                        if (positionCode === 17) {{
                            cell.classList.add('highlight-17');
                        }} else {{
                            cell.classList.add('patrol');
                        }}
                        // 添加路径序号
                        const pathIndex = patrolPath.indexOf(positionCode) + 1;
                        const pathNumber = document.createElement('div');
                        pathNumber.className = 'path-number';
                        pathNumber.textContent = pathIndex;
                        cell.appendChild(pathNumber);
                    }} else if (returnPath.includes(positionCode)) {{
                        cell.classList.add('return');
                        // 添加返航序号
                        const returnIndex = returnPath.indexOf(positionCode) + 1;
                        const pathNumber = document.createElement('div');
                        pathNumber.className = 'path-number';
                        pathNumber.textContent = 'R' + returnIndex;
                        cell.appendChild(pathNumber);
                    }} else {{
                        cell.classList.add('normal');
                    }}
                    
                    grid.appendChild(cell);
                }}
            }}
        }}
        
        // 页面加载时生成网格
        document.addEventListener('DOMContentLoaded', generateGrid);
    </script>
</body>
</html>'''
    
    return html_content

def update_specific_html_files():
    """更新特定路径的HTML文件"""
    
    print("正在从path_storage.c提取最新数据...")
    
    # 提取所有路径数据
    all_path_data = extract_path_data_from_c_file()
    
    # 更新特定路径的HTML文件
    target_paths = [50, 52, 90, 92]
    
    for path_data in all_path_data:
        if path_data['path_num'] in target_paths:
            print(f"更新路径{path_data['path_num']}:")
            print(f"  禁飞区: {path_data['no_fly_zones']}")
            print(f"  巡查点数: {len(path_data['patrol_path'])}")
            print(f"  包含位置17: {'✅' if 17 in path_data['patrol_path'] else '❌'}")
            
            # 生成HTML内容
            html_content = generate_html_for_path(path_data)
            
            # 保存HTML文件
            filename = f"path_{path_data['path_num']:03d}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"  ✅ 已更新 {filename}")
            print()
    
    print("HTML文件更新完成！")

if __name__ == "__main__":
    update_specific_html_files()
